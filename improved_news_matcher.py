import polars as pl
import pandas as pd
import numpy as np
from datetime import datetime, timedelta, timezone
import os
import glob
from pathlib import Path

def convert_utc8_to_utc0(time_str, date_str):
    """Convert UTC+8 time to UTC+0"""
    try:
        # Handle NaN dates
        if pd.isna(date_str) or str(date_str).lower() == 'nan':
            return None
            
        # Parse Chinese date format
        month_day = str(date_str).replace("月", "-").replace("日", "")
        if "-" in month_day:
            month, day = month_day.split("-")
            year = 2025
            
            dt_utc8 = datetime(year, int(month), int(day))
            
            if ":" in str(time_str):
                hour, minute = str(time_str).split(":")
                dt_utc8 = dt_utc8.replace(hour=int(hour), minute=int(minute))
            
            dt_utc0 = dt_utc8 - timedelta(hours=8)
            return dt_utc0
    except Exception as e:
        print(f"Error converting time {time_str} {date_str}: {e}")
        return None

def find_next_5min_timestamp(target_time):
    """Find the next 5-minute aligned timestamp after the target time"""
    minute = target_time.minute
    next_5min = ((minute // 5) + 1) * 5
    
    if next_5min >= 60:
        next_time = target_time.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
    else:
        next_time = target_time.replace(minute=next_5min, second=0, microsecond=0)
    
    return next_time

def load_symbol_labeled_data(symbol):
    """Load labeled data for a specific symbol"""
    file_path = f"/root/news_train/data/{symbol}_5m_relative_labeled.parquet"
    if not os.path.exists(file_path):
        return None
    
    try:
        df = pl.read_parquet(file_path)
        return df
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return None

def find_matching_label_data(symbol_df, target_timestamp):
    """Find the matching label data for a given timestamp"""
    try:
        # Ensure target timestamp has UTC timezone
        if target_timestamp.tzinfo is None:
            target_timestamp = target_timestamp.replace(tzinfo=timezone.utc)
        
        # Find records >= target timestamp
        filtered_df = symbol_df.filter(pl.col("timestamp") >= target_timestamp)
        
        if filtered_df.height == 0:
            return None
        
        # Get the first (earliest) matching record
        match_row = filtered_df.sort("timestamp").head(1)
        
        if match_row.height == 0:
            return None
        
        # Extract the data as dictionary
        row_dict = match_row.to_dicts()[0]
        
        # Return relevant features
        result = {
            'timestamp': row_dict['timestamp'],
            'close': row_dict.get('close'),
            'volume': row_dict.get('volume'),
            'relative_price': row_dict.get('relative_price'),
            'label': row_dict.get('label'),
            'natr': row_dict.get('natr'),
            'mfi': row_dict.get('mfi'),
            'rsi': row_dict.get('rsi'),
            'btc_close': row_dict.get('btc_close'),
            'btc_natr': row_dict.get('btc_natr'),
            'btc_mfi': row_dict.get('btc_mfi'),
            'btc_rsi': row_dict.get('btc_rsi'),
            'event_starts': row_dict.get('event_starts'),
            'event_ends': row_dict.get('event_ends')
        }
        
        return result
        
    except Exception as e:
        print(f"Error finding matching data: {e}")
        return None

def should_skip_news(relative_symbols):
    """Check if news should be skipped"""
    if pd.isna(relative_symbols) or str(relative_symbols).strip() == '' or str(relative_symbols).lower() == 'nan':
        return True
    
    symbols = [s.strip() for s in str(relative_symbols).split(',')]
    symbols = [s for s in symbols if s and s != 'nan']
    
    if not symbols:
        return True
    
    # Skip if all symbols are BTCUSDT
    if all(symbol == 'BTCUSDT' for symbol in symbols):
        return True
    
    return False

def filter_non_btc_symbols(relative_symbols):
    """Filter out BTCUSDT from symbol list"""
    if pd.isna(relative_symbols) or str(relative_symbols).strip() == '' or str(relative_symbols).lower() == 'nan':
        return []
    
    symbols = [s.strip() for s in str(relative_symbols).split(',')]
    symbols = [s for s in symbols if s and s != 'BTCUSDT' and s != 'nan']
    
    return symbols

def process_news_file_improved(news_file_path):
    """Process a single news CSV file with improved error handling"""
    print(f"📰 Processing news file: {news_file_path}")
    
    try:
        # Read news data
        news_df = pd.read_csv(news_file_path)
        print(f"📊 Loaded {len(news_df)} news items")
        
        training_samples = []
        processed_count = 0
        skipped_count = 0
        time_conversion_errors = 0
        no_label_found = 0
        no_symbol_data = 0
        
        for idx, row in news_df.iterrows():
            try:
                # Extract news information
                title = row['title']
                content = row['content']
                time_str = row['time']
                date_str = row['date']
                relative_symbols = row['relative_symbols']
                
                # Skip BTC-only news or empty symbols
                if should_skip_news(relative_symbols):
                    skipped_count += 1
                    continue
                
                # Convert UTC+8 to UTC+0
                utc_time = convert_utc8_to_utc0(time_str, date_str)
                if utc_time is None:
                    time_conversion_errors += 1
                    continue
                
                # Find next 5-minute aligned timestamp
                target_timestamp = find_next_5min_timestamp(utc_time)
                
                # Parse relative symbols and filter out BTCUSDT
                symbols = filter_non_btc_symbols(relative_symbols)
                
                if not symbols:
                    skipped_count += 1
                    continue
                
                # Process each symbol separately
                for symbol in symbols:
                    # Load symbol labeled data
                    symbol_df = load_symbol_labeled_data(symbol)
                    if symbol_df is None:
                        no_symbol_data += 1
                        continue
                    
                    # Find matching label data
                    match_data = find_matching_label_data(symbol_df, target_timestamp)
                    if match_data is None:
                        no_label_found += 1
                        continue
                    
                    # Create training sample
                    sample = {
                        'news_title': title,
                        'news_content': content,
                        'news_time_utc8': f"{date_str} {time_str}",
                        'news_time_utc0': utc_time.isoformat(),
                        'target_timestamp': target_timestamp.isoformat(),
                        'matched_timestamp': match_data['timestamp'].isoformat() if match_data['timestamp'] else None,
                        'symbol': symbol,
                        'close': match_data['close'],
                        'volume': match_data['volume'],
                        'relative_price': match_data['relative_price'],
                        'label': match_data['label'],
                        'natr': match_data['natr'],
                        'mfi': match_data['mfi'],
                        'rsi': match_data['rsi'],
                        'btc_close': match_data['btc_close'],
                        'btc_natr': match_data['btc_natr'],
                        'btc_mfi': match_data['btc_mfi'],
                        'btc_rsi': match_data['btc_rsi'],
                        'event_starts': match_data['event_starts'].isoformat() if match_data['event_starts'] else None,
                        'event_ends': match_data['event_ends'].isoformat() if match_data['event_ends'] else None
                    }
                    
                    training_samples.append(sample)
                    processed_count += 1
                    
            except Exception as e:
                print(f"❌ Error processing news item {idx}: {e}")
                skipped_count += 1
                continue
        
        print(f"✅ Results:")
        print(f"   Processed: {processed_count} training samples")
        print(f"   Skipped: {skipped_count} items")
        print(f"   Time conversion errors: {time_conversion_errors}")
        print(f"   No symbol data: {no_symbol_data}")
        print(f"   No label found: {no_label_found}")
        
        return training_samples
        
    except Exception as e:
        print(f"❌ Error processing news file {news_file_path}: {e}")
        return []

def main():
    """Test the improved matcher on a single file"""
    print("🧪 Testing Improved News-Label Matcher")
    print("=" * 50)
    
    # Test on first file
    test_file = "/root/news_train/filter_news/panews_flash_20250101.csv"
    samples = process_news_file_improved(test_file)
    
    if samples:
        print(f"\n📊 Generated {len(samples)} samples")
        
        # Show sample
        if len(samples) > 0:
            sample = samples[0]
            print(f"\n📰 Sample training data:")
            print(f"   Title: {sample['news_title'][:60]}...")
            print(f"   Symbol: {sample['symbol']}")
            print(f"   UTC+8 Time: {sample['news_time_utc8']}")
            print(f"   UTC+0 Time: {sample['news_time_utc0']}")
            print(f"   Target: {sample['target_timestamp']}")
            print(f"   Matched: {sample['matched_timestamp']}")
            print(f"   Label: {sample['label']}")
            print(f"   Close: {sample['close']}")
            print(f"   Relative Price: {sample['relative_price']}")
        
        # Save test results
        df = pd.DataFrame(samples)
        output_file = "/root/news_train/test_samples.csv"
        df.to_csv(output_file, index=False)
        print(f"\n💾 Test results saved to: {output_file}")
        
        # Quick analysis
        print(f"\n📈 Quick Analysis:")
        print(f"   Unique symbols: {df['symbol'].nunique()}")
        print(f"   Valid labels: {df['label'].notna().sum()}/{len(df)} ({df['label'].notna().sum()/len(df)*100:.1f}%)")
        
        if df['label'].notna().sum() > 0:
            valid_labels = df.dropna(subset=['label'])
            print(f"   Label range: {valid_labels['label'].min():.6f} to {valid_labels['label'].max():.6f}")
    
    else:
        print("❌ No samples generated")

if __name__ == "__main__":
    main()