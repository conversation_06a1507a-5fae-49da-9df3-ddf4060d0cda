#!/usr/bin/env python3
"""
技术指标对比测试

对比：
1. 仅使用新闻文本
2. 新闻文本 + 技术指标

目的：验证技术指标的引入对模型性能的影响

作者：AI Assistant
日期：2025年7月27日
"""

import pandas as pd
import numpy as np
import os
import sentencepiece as smp
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score, classification_report
import warnings
warnings.filterwarnings('ignore')


def load_data():
    """加载数据"""
    print("=== 加载数据 ===")
    
    df = pd.read_csv('process/news_training_samples_complete_all.csv')
    df = df[df['label'] != 'TBD'].copy()
    df = df.dropna(subset=['news_title', 'news_content', 'label'])
    
    # 数值转换
    numeric_columns = ['label', 'natr', 'mfi', 'rsi', 'btc_natr', 'btc_mfi', 'btc_rsi']
    for col in numeric_columns:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    df = df.dropna(subset=numeric_columns)
    df['combined_text'] = df['news_title'].fillna('') + ' ' + df['news_content'].fillna('')
    
    # 取样本以确保结果稳定
    sample_df = df.sample(min(1500, len(df)), random_state=42)
    
    print(f"使用样本: {len(sample_df)} 条")
    print(f"收益率统计: 均值={sample_df['label'].mean():.4f}, 标准差={sample_df['label'].std():.4f}")
    
    return sample_df


def create_symmetric_categories(returns, thresholds=[-0.03, -0.008, 0.008, 0.03]):
    """创建对称分类（使用最优阈值）"""
    neg_high, neg_low, pos_low, pos_high = thresholds
    
    categories = np.zeros(len(returns), dtype=int)
    categories[returns < neg_high] = 0      # 大跌
    categories[(returns >= neg_high) & (returns < neg_low)] = 1  # 小跌
    categories[(returns >= neg_low) & (returns <= pos_low)] = 2  # 平稳
    categories[(returns > pos_low) & (returns <= pos_high)] = 3  # 小涨
    categories[returns > pos_high] = 4      # 大涨
    
    return categories


def prepare_text_features(df):
    """准备文本特征"""
    print("=== 准备文本特征 ===")
    
    # 使用最优tokenizer
    tokenizer_path = "models/baseline_2k.model"
    
    if os.path.exists(tokenizer_path):
        sp = smp.SentencePieceProcessor()
        sp.load(tokenizer_path)
        
        def tokenize_text(text):
            return ' '.join(sp.encode_as_pieces(text))
        
        print("使用最优tokenizer (baseline_2k)...")
        tokenized_texts = [tokenize_text(text) for text in df['combined_text']]
        
        vectorizer = TfidfVectorizer(max_features=300, min_df=2, max_df=0.8)
        X_text = vectorizer.fit_transform(tokenized_texts)
        
    else:
        print("使用默认TF-IDF...")
        vectorizer = TfidfVectorizer(max_features=300, min_df=2, max_df=0.8)
        X_text = vectorizer.fit_transform(df['combined_text'])
    
    print(f"文本特征维度: {X_text.shape}")
    return X_text


def prepare_technical_features(df):
    """准备技术指标特征"""
    print("=== 准备技术指标特征 ===")
    
    technical_cols = ['natr', 'mfi', 'rsi', 'btc_natr', 'btc_mfi', 'btc_rsi']
    technical_features = df[technical_cols].values
    
    print(f"技术指标特征维度: {technical_features.shape}")
    print("技术指标说明:")
    print("  natr, mfi, rsi: 个股技术指标")
    print("  btc_natr, btc_mfi, btc_rsi: BTC技术指标")
    
    return technical_features


def evaluate_model(X, y, model_name, feature_description):
    """评估模型性能"""
    print(f"\n--- 评估 {model_name} ---")
    print(f"特征: {feature_description}")
    print(f"特征维度: {X.shape}")
    
    # 检查类别分布
    unique_classes, counts = np.unique(y, return_counts=True)
    class_dist = {i: count for i, count in zip(unique_classes, counts)}
    print(f"类别分布: {class_dist}")
    
    if len(unique_classes) < 3 or min(counts) < 5:
        print("⚠️ 类别分布不均衡，跳过评估")
        return None
    
    # 分割数据
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, random_state=42, stratify=y
    )
    
    # 训练模型
    model = RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1)
    model.fit(X_train, y_train)
    
    # 预测
    y_pred = model.predict(X_test)
    
    # 计算指标
    accuracy = accuracy_score(y_test, y_pred)
    
    # 交叉验证
    cv_scores = cross_val_score(model, X_train, y_train, cv=5, scoring='accuracy')
    
    print(f"测试准确率: {accuracy:.4f}")
    print(f"交叉验证: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
    
    # 详细分类报告
    print("分类报告:")
    print(classification_report(y_test, y_pred, target_names=[
        '大跌', '小跌', '平稳', '小涨', '大涨'
    ][:len(unique_classes)]))
    
    return {
        'accuracy': accuracy,
        'cv_mean': cv_scores.mean(),
        'cv_std': cv_scores.std(),
        'feature_dim': X.shape[1],
        'class_distribution': class_dist
    }


def analyze_feature_importance(X_text, X_tech, y):
    """分析特征重要性"""
    print("\n=== 特征重要性分析 ===")
    
    # 合并特征
    X_combined = np.hstack([X_text.toarray(), X_tech])
    
    # 训练模型
    model = RandomForestClassifier(n_estimators=100, random_state=42)
    model.fit(X_combined, y)
    
    # 获取特征重要性
    importances = model.feature_importances_
    
    # 分析文本特征 vs 技术指标特征的重要性
    text_importance = np.sum(importances[:X_text.shape[1]])
    tech_importance = np.sum(importances[X_text.shape[1]:])
    
    print(f"文本特征总重要性: {text_importance:.4f} ({text_importance/(text_importance+tech_importance)*100:.1f}%)")
    print(f"技术指标总重要性: {tech_importance:.4f} ({tech_importance/(text_importance+tech_importance)*100:.1f}%)")
    
    # 技术指标各维度重要性
    tech_names = ['natr', 'mfi', 'rsi', 'btc_natr', 'btc_mfi', 'btc_rsi']
    tech_importances = importances[X_text.shape[1]:]
    
    print("\n各技术指标重要性:")
    for name, importance in zip(tech_names, tech_importances):
        print(f"  {name}: {importance:.4f}")
    
    return {
        'text_importance': text_importance,
        'tech_importance': tech_importance,
        'tech_individual': dict(zip(tech_names, tech_importances))
    }


def main():
    """主函数"""
    print("🚀 技术指标对比测试")
    print("="*60)
    
    # 1. 加载数据
    df = load_data()
    
    # 2. 准备特征
    X_text = prepare_text_features(df)
    X_tech = prepare_technical_features(df)
    
    # 3. 创建标签（使用最优阈值）
    y = create_symmetric_categories(df['label'].values)
    
    print(f"\n目标变量分布:")
    unique, counts = np.unique(y, return_counts=True)
    for cls, count in zip(unique, counts):
        class_names = ['大跌', '小跌', '平稳', '小涨', '大涨']
        print(f"  {cls} ({class_names[cls]}): {count} 条")
    
    # 4. 对比测试
    print("\n" + "="*60)
    print("🔬 开始对比测试")
    print("="*60)
    
    results = {}
    
    # 测试1: 仅使用新闻文本
    results['text_only'] = evaluate_model(
        X_text.toarray(), y, 
        "仅新闻文本", 
        "TF-IDF文本特征"
    )
    
    # 测试2: 新闻文本 + 技术指标
    X_combined = np.hstack([X_text.toarray(), X_tech])
    results['text_plus_tech'] = evaluate_model(
        X_combined, y,
        "新闻文本 + 技术指标",
        "TF-IDF文本特征 + 6维技术指标"
    )
    
    # 测试3: 仅使用技术指标（作为对照）
    results['tech_only'] = evaluate_model(
        X_tech, y,
        "仅技术指标",
        "6维技术指标"
    )
    
    # 5. 特征重要性分析
    importance_analysis = analyze_feature_importance(X_text, X_tech, y)
    
    # 6. 结果总结
    print("\n" + "="*60)
    print("📊 结果总结")
    print("="*60)
    
    if all(r is not None for r in results.values()):
        text_acc = results['text_only']['accuracy']
        combined_acc = results['text_plus_tech']['accuracy']
        tech_acc = results['tech_only']['accuracy']
        
        print(f"📈 准确率对比:")
        print(f"  仅新闻文本:        {text_acc:.4f}")
        print(f"  新闻文本+技术指标:  {combined_acc:.4f}")
        print(f"  仅技术指标:        {tech_acc:.4f}")
        
        # 计算提升
        improvement = combined_acc - text_acc
        improvement_pct = improvement / text_acc * 100
        
        print(f"\n💡 技术指标的作用:")
        print(f"  绝对提升: {improvement:+.4f}")
        print(f"  相对提升: {improvement_pct:+.1f}%")
        
        if improvement > 0:
            print(f"  ✅ 技术指标有正面作用")
        else:
            print(f"  ❌ 技术指标无明显帮助")
        
        # 交叉验证稳定性对比
        text_cv = results['text_only']['cv_std']
        combined_cv = results['text_plus_tech']['cv_std']
        
        print(f"\n🎯 模型稳定性 (CV标准差):")
        print(f"  仅新闻文本:        {text_cv:.4f}")
        print(f"  新闻文本+技术指标:  {combined_cv:.4f}")
        
        if combined_cv < text_cv:
            print(f"  ✅ 技术指标提高了模型稳定性")
        else:
            print(f"  ⚠️ 技术指标可能增加了模型复杂度")
        
        # 特征维度效率
        text_dim = results['text_only']['feature_dim']
        combined_dim = results['text_plus_tech']['feature_dim']
        
        print(f"\n📏 特征效率:")
        print(f"  文本特征维度: {text_dim}")
        print(f"  总特征维度: {combined_dim}")
        print(f"  技术指标占比: {6/combined_dim*100:.1f}%")
        print(f"  技术指标重要性: {importance_analysis['tech_importance']/(importance_analysis['text_importance']+importance_analysis['tech_importance'])*100:.1f}%")
        
        # 最重要的技术指标
        tech_importances = importance_analysis['tech_individual']
        most_important = max(tech_importances.items(), key=lambda x: x[1])
        print(f"  最重要技术指标: {most_important[0]} ({most_important[1]:.4f})")
        
        # 实际应用建议
        print(f"\n🚀 实际应用建议:")
        
        if improvement_pct > 5:
            print(f"  ✅ 强烈推荐使用技术指标，提升显著")
        elif improvement_pct > 1:
            print(f"  ✅ 推荐使用技术指标，有一定提升")
        elif improvement_pct > -1:
            print(f"  ⚠️ 技术指标作用有限，可选择使用")
        else:
            print(f"  ❌ 不推荐使用技术指标，可能有负面影响")
        
        # 成本效益分析
        feature_cost_ratio = 6 / text_dim  # 技术指标相对成本
        performance_gain_ratio = improvement_pct / 100
        
        if performance_gain_ratio > feature_cost_ratio:
            print(f"  💰 技术指标具有良好的成本效益比")
        else:
            print(f"  💸 技术指标的成本效益比一般")
    
    else:
        print("❌ 部分测试失败，无法进行完整对比")
    
    # 保存结果
    results_summary = {
        'results': results,
        'importance_analysis': importance_analysis,
        'improvement': {
            'absolute': improvement if 'improvement' in locals() else None,
            'percentage': improvement_pct if 'improvement_pct' in locals() else None
        }
    }
    
    # 保存到CSV
    if all(r is not None for r in results.values()):
        summary_df = pd.DataFrame({
            '方法': ['仅新闻文本', '新闻文本+技术指标', '仅技术指标'],
            '准确率': [text_acc, combined_acc, tech_acc],
            '交叉验证均值': [results['text_only']['cv_mean'], 
                         results['text_plus_tech']['cv_mean'],
                         results['tech_only']['cv_mean']],
            '交叉验证标准差': [results['text_only']['cv_std'],
                           results['text_plus_tech']['cv_std'], 
                           results['tech_only']['cv_std']],
            '特征维度': [results['text_only']['feature_dim'],
                       results['text_plus_tech']['feature_dim'],
                       results['tech_only']['feature_dim']]
        })
        
        summary_df.to_csv('技术指标对比结果.csv', index=False, encoding='utf-8')
        print(f"\n📁 详细结果已保存到: 技术指标对比结果.csv")
    
    print(f"\n✅ 技术指标对比测试完成！")


if __name__ == "__main__":
    main()
