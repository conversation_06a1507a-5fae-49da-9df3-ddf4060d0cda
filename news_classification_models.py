"""
新闻分类模型对比
使用多种传统机器学习方法进行新闻return分类
"""

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, AdaBoostClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.naive_bayes import MultinomialNB
from sklearn.neighbors import KNeighborsClassifier
from sklearn.tree import DecisionTreeClassifier
from sklearn.neural_network import MLPClassifier
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.model_selection import cross_val_score, GridSearchCV
import matplotlib.pyplot as plt
import seaborn as sns
from news_data_preprocessor import NewsDataPreprocessor
import time
import warnings
warnings.filterwarnings('ignore')

class NewsClassificationModels:
    def __init__(self):
        self.models = {}
        self.results = {}
        self.best_models = {}
        
    def initialize_models(self):
        """初始化各种分类模型"""
        self.models = {
            'Logistic Regression': LogisticRegression(
                random_state=42, 
                max_iter=1000,
                class_weight='balanced'
            ),
            
            'Random Forest': RandomForestClassifier(
                n_estimators=100,
                random_state=42,
                class_weight='balanced',
                n_jobs=-1
            ),
            
            'Gradient Boosting': GradientBoostingClassifier(
                n_estimators=100,
                random_state=42,
                learning_rate=0.1
            ),
            
            'AdaBoost': AdaBoostClassifier(
                n_estimators=100,
                random_state=42,
                learning_rate=1.0
            ),
            
            'Naive Bayes': MultinomialNB(
                alpha=1.0
            ),
            
            'K-Nearest Neighbors': KNeighborsClassifier(
                n_neighbors=5,
                weights='distance'
            ),
            
            'Decision Tree': DecisionTreeClassifier(
                random_state=42,
                class_weight='balanced',
                max_depth=10
            ),
            
            'MLP Neural Network': MLPClassifier(
                hidden_layer_sizes=(100, 50),
                random_state=42,
                max_iter=500,
                early_stopping=True
            )
        }
        
        print(f"初始化了{len(self.models)}个模型")
    
    def train_and_evaluate_models(self, X_train, X_test, y_train, y_test):
        """训练和评估所有模型"""
        print("=== 开始训练和评估模型 ===")
        
        for name, model in self.models.items():
            print(f"\n训练模型: {name}")
            start_time = time.time()
            
            try:
                # 训练模型
                model.fit(X_train, y_train)
                
                # 预测
                y_pred = model.predict(X_test)
                
                # 计算指标
                accuracy = accuracy_score(y_test, y_pred)
                
                # 交叉验证
                cv_scores = cross_val_score(model, X_train, y_train, cv=5, scoring='accuracy')
                
                training_time = time.time() - start_time
                
                # 保存结果
                self.results[name] = {
                    'model': model,
                    'accuracy': accuracy,
                    'cv_mean': cv_scores.mean(),
                    'cv_std': cv_scores.std(),
                    'y_pred': y_pred,
                    'training_time': training_time,
                    'classification_report': classification_report(y_test, y_pred, output_dict=True)
                }
                
                print(f"准确率: {accuracy:.4f}")
                print(f"交叉验证: {cv_scores.mean():.4f} (+/- {cv_scores.std() * 2:.4f})")
                print(f"训练时间: {training_time:.2f}秒")
                
            except Exception as e:
                print(f"模型{name}训练失败: {e}")
                continue
    
    def hyperparameter_tuning(self, X_train, y_train, top_models=3):
        """对表现最好的几个模型进行超参数调优"""
        print(f"\n=== 对前{top_models}个模型进行超参数调优 ===")
        
        # 按准确率排序，选择前几个模型
        sorted_models = sorted(self.results.items(), 
                             key=lambda x: x[1]['accuracy'], 
                             reverse=True)[:top_models]
        
        param_grids = {
            'Random Forest': {
                'n_estimators': [50, 100, 200],
                'max_depth': [5, 10, None],
                'min_samples_split': [2, 5, 10]
            },
            
            'Gradient Boosting': {
                'n_estimators': [50, 100, 150],
                'learning_rate': [0.05, 0.1, 0.2],
                'max_depth': [3, 5, 7]
            },
            
            'Logistic Regression': {
                'C': [0.1, 1, 10, 100],
                'penalty': ['l1', 'l2'],
                'solver': ['liblinear', 'saga']
            }
        }
        
        for name, result in sorted_models:
            if name in param_grids:
                print(f"\n调优模型: {name}")
                
                try:
                    grid_search = GridSearchCV(
                        self.models[name],
                        param_grids[name],
                        cv=3,
                        scoring='accuracy',
                        n_jobs=-1,
                        verbose=0
                    )
                    
                    grid_search.fit(X_train, y_train)
                    
                    self.best_models[name] = {
                        'model': grid_search.best_estimator_,
                        'best_params': grid_search.best_params_,
                        'best_score': grid_search.best_score_
                    }
                    
                    print(f"最佳参数: {grid_search.best_params_}")
                    print(f"最佳交叉验证分数: {grid_search.best_score_:.4f}")
                    
                except Exception as e:
                    print(f"模型{name}调优失败: {e}")
    
    def generate_detailed_report(self, y_test):
        """生成详细的模型对比报告"""
        print("\n" + "="*80)
        print("详细模型对比报告")
        print("="*80)
        
        # 创建结果DataFrame
        results_data = []
        for name, result in self.results.items():
            results_data.append({
                'Model': name,
                'Accuracy': result['accuracy'],
                'CV Mean': result['cv_mean'],
                'CV Std': result['cv_std'],
                'Training Time (s)': result['training_time']
            })
        
        results_df = pd.DataFrame(results_data)
        results_df = results_df.sort_values('Accuracy', ascending=False)
        
        print("\n模型性能排名:")
        print(results_df.to_string(index=False, float_format='%.4f'))
        
        # 详细分类报告
        print(f"\n{'='*50}")
        print("各模型详细分类报告:")
        print(f"{'='*50}")
        
        class_names = ['强跌', '弱跌', '横盘', '弱涨', '强涨']
        
        for name, result in sorted(self.results.items(), 
                                 key=lambda x: x[1]['accuracy'], 
                                 reverse=True):
            print(f"\n{name}:")
            print("-" * 40)
            
            # 分类报告
            report = result['classification_report']
            for i in range(5):
                if str(i) in report:
                    precision = report[str(i)]['precision']
                    recall = report[str(i)]['recall']
                    f1 = report[str(i)]['f1-score']
                    support = report[str(i)]['support']
                    print(f"{class_names[i]:>6}: P={precision:.3f} R={recall:.3f} F1={f1:.3f} Support={support}")
            
            # 整体指标
            macro_avg = report['macro avg']
            print(f"{'Macro':>6}: P={macro_avg['precision']:.3f} R={macro_avg['recall']:.3f} F1={macro_avg['f1-score']:.3f}")
    
    def plot_model_comparison(self):
        """绘制模型对比图"""
        # 准备数据
        models = list(self.results.keys())
        accuracies = [self.results[model]['accuracy'] for model in models]
        cv_means = [self.results[model]['cv_mean'] for model in models]
        training_times = [self.results[model]['training_time'] for model in models]
        
        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('模型性能对比', fontsize=16, fontweight='bold')
        
        # 1. 准确率对比
        axes[0, 0].bar(range(len(models)), accuracies, color='skyblue', alpha=0.7)
        axes[0, 0].set_title('测试集准确率')
        axes[0, 0].set_ylabel('准确率')
        axes[0, 0].set_xticks(range(len(models)))
        axes[0, 0].set_xticklabels(models, rotation=45, ha='right')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 添加数值标签
        for i, v in enumerate(accuracies):
            axes[0, 0].text(i, v + 0.005, f'{v:.3f}', ha='center', va='bottom')
        
        # 2. 交叉验证分数对比
        axes[0, 1].bar(range(len(models)), cv_means, color='lightgreen', alpha=0.7)
        axes[0, 1].set_title('交叉验证平均分数')
        axes[0, 1].set_ylabel('CV分数')
        axes[0, 1].set_xticks(range(len(models)))
        axes[0, 1].set_xticklabels(models, rotation=45, ha='right')
        axes[0, 1].grid(True, alpha=0.3)
        
        for i, v in enumerate(cv_means):
            axes[0, 1].text(i, v + 0.005, f'{v:.3f}', ha='center', va='bottom')
        
        # 3. 训练时间对比
        axes[1, 0].bar(range(len(models)), training_times, color='salmon', alpha=0.7)
        axes[1, 0].set_title('训练时间')
        axes[1, 0].set_ylabel('时间 (秒)')
        axes[1, 0].set_xticks(range(len(models)))
        axes[1, 0].set_xticklabels(models, rotation=45, ha='right')
        axes[1, 0].grid(True, alpha=0.3)
        
        for i, v in enumerate(training_times):
            axes[1, 0].text(i, v + max(training_times)*0.01, f'{v:.1f}s', ha='center', va='bottom')
        
        # 4. 准确率vs训练时间散点图
        axes[1, 1].scatter(training_times, accuracies, s=100, alpha=0.7, c='purple')
        axes[1, 1].set_xlabel('训练时间 (秒)')
        axes[1, 1].set_ylabel('准确率')
        axes[1, 1].set_title('准确率 vs 训练时间')
        axes[1, 1].grid(True, alpha=0.3)
        
        # 添加模型名称标签
        for i, model in enumerate(models):
            axes[1, 1].annotate(model, (training_times[i], accuracies[i]), 
                              xytext=(5, 5), textcoords='offset points', 
                              fontsize=8, alpha=0.8)
        
        plt.tight_layout()
        plt.savefig('model_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("模型对比图已保存为 model_comparison.png")
    
    def plot_confusion_matrices(self, y_test, top_n=4):
        """绘制最佳几个模型的混淆矩阵"""
        # 选择表现最好的几个模型
        sorted_models = sorted(self.results.items(), 
                             key=lambda x: x[1]['accuracy'], 
                             reverse=True)[:top_n]
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        fig.suptitle('最佳模型混淆矩阵', fontsize=16, fontweight='bold')
        axes = axes.ravel()
        
        class_names = ['强跌', '弱跌', '横盘', '弱涨', '强涨']
        
        for i, (name, result) in enumerate(sorted_models):
            if i >= 4:
                break
                
            cm = confusion_matrix(y_test, result['y_pred'])
            
            sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                       xticklabels=class_names, yticklabels=class_names,
                       ax=axes[i])
            axes[i].set_title(f'{name}\n准确率: {result["accuracy"]:.3f}')
            axes[i].set_xlabel('预测标签')
            axes[i].set_ylabel('真实标签')
        
        plt.tight_layout()
        plt.savefig('confusion_matrices.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("混淆矩阵图已保存为 confusion_matrices.png")
    
    def save_best_model(self, filename='best_news_classifier.pkl'):
        """保存最佳模型"""
        if not self.results:
            print("没有训练好的模型可保存")
            return
            
        # 找到最佳模型
        best_model_name = max(self.results.keys(), 
                            key=lambda x: self.results[x]['accuracy'])
        best_model = self.results[best_model_name]['model']
        
        import pickle
        with open(filename, 'wb') as f:
            pickle.dump({
                'model': best_model,
                'model_name': best_model_name,
                'accuracy': self.results[best_model_name]['accuracy']
            }, f)
        
        print(f"最佳模型 {best_model_name} 已保存到 {filename}")
        print(f"模型准确率: {self.results[best_model_name]['accuracy']:.4f}")

def main():
    """主函数"""
    print("=== 新闻分类模型对比实验 ===")
    
    # 加载预处理后的数据
    try:
        preprocessor = NewsDataPreprocessor()
        X_train, X_test, y_train, y_test = preprocessor.load_processed_data()
        print("成功加载预处理数据")
    except FileNotFoundError:
        print("未找到预处理数据，请先运行 news_data_preprocessor.py")
        return
    except Exception as e:
        print(f"加载数据失败: {e}")
        return
    
    # 初始化模型对比器
    classifier = NewsClassificationModels()
    classifier.initialize_models()
    
    # 训练和评估模型
    classifier.train_and_evaluate_models(X_train, X_test, y_train, y_test)
    
    # 超参数调优
    classifier.hyperparameter_tuning(X_train, y_train, top_models=3)
    
    # 生成详细报告
    classifier.generate_detailed_report(y_test)
    
    # 绘制对比图
    classifier.plot_model_comparison()
    classifier.plot_confusion_matrices(y_test)
    
    # 保存最佳模型
    classifier.save_best_model()
    
    print("\n=== 实验完成 ===")
    print("生成的文件:")
    print("- model_comparison.png: 模型性能对比图")
    print("- confusion_matrices.png: 混淆矩阵图")
    print("- best_news_classifier.pkl: 最佳模型文件")

if __name__ == "__main__":
    main()