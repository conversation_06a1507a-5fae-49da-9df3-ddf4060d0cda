#!/usr/bin/env python3
"""
最终优化测试 - 基于最优tokenizer和对称阈值
"""

import pandas as pd
import numpy as np
import os
import sentencepiece as smp
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score
import warnings
warnings.filterwarnings('ignore')


def load_data():
    """加载数据"""
    print("=== 加载数据 ===")
    
    df = pd.read_csv('process/news_training_samples_complete_all.csv')
    df = df[df['label'] != 'TBD'].copy()
    df = df.dropna(subset=['news_title', 'news_content', 'label'])
    
    # 数值转换
    numeric_columns = ['label', 'natr', 'mfi', 'rsi', 'btc_natr', 'btc_mfi', 'btc_rsi']
    for col in numeric_columns:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    df = df.dropna(subset=numeric_columns)
    df['combined_text'] = df['news_title'].fillna('') + ' ' + df['news_content'].fillna('')
    
    # 取样本以加快测试
    sample_df = df.sample(min(1500, len(df)), random_state=42)
    
    print(f"使用样本: {len(sample_df)} 条")
    print(f"收益率统计: 均值={sample_df['label'].mean():.4f}, 标准差={sample_df['label'].std():.4f}")
    
    return sample_df


def create_symmetric_categories(returns, thresholds):
    """创建对称分类"""
    neg_high, neg_low, pos_low, pos_high = thresholds
    
    categories = np.zeros(len(returns), dtype=int)
    categories[returns < neg_high] = 0      # 大跌
    categories[(returns >= neg_high) & (returns < neg_low)] = 1  # 小跌
    categories[(returns >= neg_low) & (returns <= pos_low)] = 2  # 平稳
    categories[(returns > pos_low) & (returns <= pos_high)] = 3  # 小涨
    categories[returns > pos_high] = 4      # 大涨
    
    return categories


def prepare_features(df):
    """准备特征"""
    print("=== 准备特征 ===")
    
    # 使用最优tokenizer
    tokenizer_path = "models/baseline_2k.model"
    
    if os.path.exists(tokenizer_path):
        sp = smp.SentencePieceProcessor()
        sp.load(tokenizer_path)
        
        def tokenize_text(text):
            return ' '.join(sp.encode_as_pieces(text))
        
        print("使用最优tokenizer...")
        tokenized_texts = [tokenize_text(text) for text in df['combined_text']]
        
        vectorizer = TfidfVectorizer(max_features=300, min_df=2, max_df=0.8)
        X_text = vectorizer.fit_transform(tokenized_texts)
        
    else:
        print("使用默认TF-IDF...")
        vectorizer = TfidfVectorizer(max_features=300, min_df=2, max_df=0.8)
        X_text = vectorizer.fit_transform(df['combined_text'])
    
    # 技术指标
    technical_features = df[['natr', 'mfi', 'rsi', 'btc_natr', 'btc_mfi', 'btc_rsi']].values
    
    # 合并特征
    X = np.hstack([X_text.toarray(), technical_features])
    
    print(f"特征维度: {X.shape}")
    return X


def test_configuration(X, returns, thresholds, model_name="RandomForest"):
    """测试单个配置"""
    try:
        # 创建分类
        y = create_symmetric_categories(returns, thresholds)
        
        # 检查类别分布
        unique_classes, counts = np.unique(y, return_counts=True)
        if len(unique_classes) < 3 or min(counts) < 5:
            return None
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.3, random_state=42, stratify=y
        )
        
        # 训练模型
        model = RandomForestClassifier(n_estimators=50, random_state=42)
        model.fit(X_train, y_train)
        
        # 预测
        y_pred = model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        
        # 类别分布
        class_dist = {i: count for i, count in zip(unique_classes, counts)}
        
        return {
            'accuracy': accuracy,
            'class_distribution': class_dist,
            'n_classes': len(unique_classes)
        }
        
    except Exception as e:
        print(f"配置测试失败: {e}")
        return None


def main():
    """主函数"""
    print("🚀 最终优化测试")
    
    # 1. 加载数据
    df = load_data()
    X = prepare_features(df)
    returns = df['label'].values
    
    # 2. 定义对称阈值配置
    trading_fee = 0.001
    
    # 基于交易可行性的阈值配置
    threshold_configs = [
        # [大跌阈值, 小跌阈值, 小涨阈值, 大涨阈值]
        [-0.020, -0.005, 0.005, 0.020],  # 标准: 5倍手续费
        [-0.030, -0.008, 0.008, 0.030],  # 宽松: 8倍手续费
        [-0.015, -0.003, 0.003, 0.015],  # 紧凑: 3倍手续费
        [-0.025, -0.010, 0.010, 0.025],  # 中等: 10倍手续费
        [-0.040, -0.012, 0.012, 0.040],  # 保守: 12倍手续费
        [-0.035, -0.015, 0.015, 0.035],  # 极保守: 15倍手续费
    ]
    
    # 3. 测试所有配置
    print(f"\n=== 测试 {len(threshold_configs)} 个对称阈值配置 ===")
    
    results = []
    
    for i, thresholds in enumerate(threshold_configs):
        print(f"\n配置 {i+1}: {thresholds}")
        
        # 计算交易可行性
        min_signal = min(abs(thresholds[1]), thresholds[2])
        fee_ratio = min_signal / trading_fee
        
        print(f"  最小信号阈值: {min_signal:.4f}")
        print(f"  手续费比率: {fee_ratio:.1f}倍")
        
        # 测试配置
        result = test_configuration(X, returns, thresholds)
        
        if result is not None:
            results.append({
                'config_id': i+1,
                'thresholds': thresholds,
                'accuracy': result['accuracy'],
                'class_distribution': result['class_distribution'],
                'n_classes': result['n_classes'],
                'min_signal': min_signal,
                'fee_ratio': fee_ratio
            })
            
            print(f"  ✅ 准确率: {result['accuracy']:.4f}")
            print(f"  类别分布: {result['class_distribution']}")
        else:
            print(f"  ❌ 配置无效")
    
    # 4. 分析结果
    print("\n" + "="*60)
    print("🏆 最终结果分析")
    print("="*60)
    
    if results:
        # 排序
        results.sort(key=lambda x: x['accuracy'], reverse=True)
        
        print("📊 所有配置结果:")
        for result in results:
            thresholds = result['thresholds']
            print(f"\n配置{result['config_id']}: {thresholds}")
            print(f"  准确率: {result['accuracy']:.4f}")
            print(f"  类别分布: {result['class_distribution']}")
            print(f"  交易可行性: {result['fee_ratio']:.1f}倍手续费")
        
        # 最佳配置
        best = results[0]
        print(f"\n🥇 最佳配置:")
        print(f"  阈值: {best['thresholds']}")
        print(f"  准确率: {best['accuracy']:.4f}")
        print(f"  类别分布: {best['class_distribution']}")
        print(f"  最小信号: {best['min_signal']:.4f}")
        print(f"  手续费比率: {best['fee_ratio']:.1f}倍")
        
        # 交易建议
        if best['fee_ratio'] >= 3:
            print(f"  ✅ 交易可行")
        else:
            print(f"  ⚠️ 信号强度不足")
        
        # 关键洞察
        print(f"\n💡 关键洞察:")
        
        # 分析阈值大小与准确率的关系
        small_thresh_results = [r for r in results if r['min_signal'] <= 0.005]
        large_thresh_results = [r for r in results if r['min_signal'] > 0.010]
        
        if small_thresh_results and large_thresh_results:
            small_avg = np.mean([r['accuracy'] for r in small_thresh_results])
            large_avg = np.mean([r['accuracy'] for r in large_thresh_results])
            
            print(f"  小阈值(≤0.5%)平均准确率: {small_avg:.4f}")
            print(f"  大阈值(>1.0%)平均准确率: {large_avg:.4f}")
            
            if small_avg > large_avg:
                print(f"  📈 小阈值表现更好，提升 {(small_avg-large_avg)/large_avg*100:.1f}%")
            else:
                print(f"  📉 大阈值表现更好，提升 {(large_avg-small_avg)/small_avg*100:.1f}%")
        
        # 对称性验证
        print(f"\n🎯 对称性验证:")
        for result in results[:3]:  # 检查前3个配置
            dist = result['class_distribution']
            if 0 in dist and 4 in dist and 1 in dist and 3 in dist:
                symmetry_score = min(dist[0]/dist[4], dist[4]/dist[0]) * min(dist[1]/dist[3], dist[3]/dist[1])
                print(f"  配置{result['config_id']} 对称性得分: {symmetry_score:.3f}")
        
        # 实际应用建议
        print(f"\n🚀 实际应用建议:")
        
        # 找到平衡点：准确率高且交易可行
        viable_results = [r for r in results if r['fee_ratio'] >= 5]  # 至少5倍手续费
        
        if viable_results:
            recommended = viable_results[0]
            print(f"  推荐配置: {recommended['thresholds']}")
            print(f"  准确率: {recommended['accuracy']:.4f}")
            print(f"  信号强度: {recommended['fee_ratio']:.1f}倍手续费")
            print(f"  适合实盘交易")
        else:
            print(f"  所有配置的信号强度都较弱")
            print(f"  建议增加阈值或降低交易频率")
        
        # 保存结果
        df_results = pd.DataFrame(results)
        df_results.to_csv('final_optimal_results.csv', index=False)
        print(f"\n📁 结果已保存到: final_optimal_results.csv")
    
    else:
        print("❌ 没有有效结果")


if __name__ == "__main__":
    main()
