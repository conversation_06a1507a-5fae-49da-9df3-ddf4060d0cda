#!/usr/bin/env python3
"""
简化版新闻预测策略回测脚本

专注于核心回测功能，使用1000PEPEUSDT作为测试标的
"""

import asyncio
import logging
from pathlib import Path
from datetime import datetime, timezone
import pandas as pd

# Nautilus Trader imports
from nautilus_trader.config import BacktestVenueConfig, BacktestDataConfig, BacktestEngineConfig, BacktestRunConfig
from nautilus_trader.config import ImportableStrategyConfig, LoggingConfig
from nautilus_trader.backtest.node import BacktestNode
from nautilus_trader.model.identifiers import InstrumentId, Venue
from nautilus_trader.model.currencies import USD, USDT
from nautilus_trader.model.enums import AccountType, OmsType
from nautilus_trader.model.objects import Money
from nautilus_trader.test_kit.providers import TestInstrumentProvider
from nautilus_trader.model.data import Bar, BarType
from nautilus_trader.model.enums import BarAggregation, PriceType
from nautilus_trader.core.datetime import dt_to_unix_nanos

# 导入数据类型
from news_data_type import NewsData

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def create_sample_news_data(symbol: str = "1000PEPEUSDT", venue: str = "BINANCE") -> NewsData:
    """创建示例新闻数据用于测试"""
    
    # 创建示例新闻
    ts_event = dt_to_unix_nanos(datetime(2024, 1, 15, 10, 0, 0, tzinfo=timezone.utc))
    ts_init = ts_event
    
    news = NewsData(
        instrument_id=InstrumentId.from_str(f"{symbol}.{venue}"),
        title="PEPE价格突破关键阻力位，市场情绪乐观",
        content="PEPE代币今日突破重要技术阻力位，交易量大幅增加，市场分析师认为这可能是新一轮上涨的开始。技术指标显示强劲的买入信号。",
        time="10:00:00",
        date="2024-01-15",
        url="https://example.com/news/pepe-breakout",
        is_featured=True,
        scraped_at="2024-01-15T10:00:00Z",
        ts_event=ts_event,
        ts_init=ts_init
    )
    
    return news

def create_sample_bars(instrument_id: InstrumentId, start_time: datetime, num_bars: int = 100):
    """创建示例K线数据"""
    bars = []
    bar_type = BarType(
        instrument_id=instrument_id,
        bar_spec=BarAggregation.MINUTE,
        aggregation_source=PriceType.LAST
    )
    
    # 创建简单的价格数据
    base_price = 0.00001234 if "PEPE" in str(instrument_id) else 45000.0
    
    for i in range(num_bars):
        ts = dt_to_unix_nanos(start_time.replace(minute=start_time.minute + i * 5))
        
        # 简单的价格变动
        price_change = (i % 10 - 5) * 0.001
        open_price = base_price * (1 + price_change)
        high_price = open_price * 1.002
        low_price = open_price * 0.998
        close_price = open_price * (1 + (i % 3 - 1) * 0.001)
        volume = 1000000 + (i % 100) * 10000
        
        # 创建Bar对象需要使用instrument的make_price和make_qty方法
        # 这里先跳过实际的Bar创建，因为需要instrument对象
        
    return bars

def create_strategy_config(symbol: str = "1000PEPEUSDT", venue: str = "BINANCE") -> ImportableStrategyConfig:
    """创建策略配置"""
    
    # 模型文件路径
    base_path = Path("/root/news_train")
    models_path = base_path / "process" / "models"
    
    tokenizer_path = str(models_path / "crypto_tokenizer_small_with_symbols.model")
    tfidf_path = str(models_path / "real_tfidf.pkl")
    model_path = str(models_path / "real_rf_model.pkl")
    
    strategy_config = {
        # 模型路径
        "tokenizer_path": tokenizer_path,
        "tfidf_path": tfidf_path,
        "model_path": model_path,
        
        # 交易工具
        "instrument_str": f"{symbol}.{venue}",
        "btc_instrument_str": f"BTCUSDT.{venue}",
        "bar_str": f"{symbol}.{venue}-5-MINUTE-LAST-EXTERNAL",
        "btc_bar_str": f"BTCUSDT.{venue}-5-MINUTE-LAST-EXTERNAL",
        
        # 风险管理参数
        "stop_loss_pct": 0.02,
        "low_trail_stop_loss_pct": 0.5,
        "trail_stop_loss_pct": 0.3,
        "higher_trail_stop_loss_pct": 0.2,
        "low_trail_profit_threshold": 0.01,
        "first_trail_profit_threshold": 0.02,
        "second_trail_profit_threshold": 0.03,
        
        # 交易金额
        "max_trade_usd": 1000.0,
        "min_trade_usd": 100.0,
        
        # 策略ID
        "strategy_id": "NewsPnlStrategy-001"
    }
    
    return ImportableStrategyConfig(
        strategy_path="process.strategy.news_prediction_strategy:NewsPnlTradingStrategy",
        config_path="process.strategy.news_prediction_strategy:NewsPnlTradingStrategyConfig",
        config=strategy_config
    )

def create_backtest_config(symbol: str = "1000PEPEUSDT", venue: str = "BINANCE") -> BacktestRunConfig:
    """创建回测配置"""
    
    # 场所配置
    venue_config = BacktestVenueConfig(
        name=venue,
        oms_type=OmsType.HEDGING,
        account_type=AccountType.MARGIN,
        base_currency=USDT,
        starting_balances=[Money(100000, USDT)],  # 10万USDT起始资金
    )
    
    # 策略配置
    strategy_config = create_strategy_config(symbol, venue)
    
    # 引擎配置
    engine_config = BacktestEngineConfig(
        strategies=[strategy_config],
        logging=LoggingConfig(
            log_level="INFO",
            bypass_logging=False,
        ),
    )
    
    # 回测运行配置
    run_config = BacktestRunConfig(
        engine=engine_config,
        venues=[venue_config],
        data=[],  # 暂时不加载外部数据
        start="2024-01-01T00:00:00Z",
        end="2024-01-31T23:59:59Z",
    )
    
    return run_config

async def run_simple_backtest():
    """运行简化回测"""
    print("🚀 开始新闻预测策略简化回测...")
    
    symbol = "1000PEPEUSDT"
    venue = "BINANCE"
    
    print(f"📈 测试标的: {symbol}")
    print(f"🏦 交易所: {venue}")
    
    # 检查必要文件
    base_path = Path("/root/news_train")
    models_path = base_path / "process" / "models"
    
    required_files = [
        models_path / "crypto_tokenizer_small_with_symbols.model",
        models_path / "real_tfidf.pkl",
        models_path / "real_rf_model.pkl"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not file_path.exists():
            missing_files.append(str(file_path))
            
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        print("💡 请先训练模型或检查文件路径")
        return False
        
    print("✅ 所有必要文件检查通过")
    
    # 创建示例新闻数据
    start_news = create_sample_news_data(symbol, venue)
    print(f"📰 创建示例新闻: {start_news.title[:50]}...")
    
    # 创建回测配置
    config = create_backtest_config(symbol, venue)
    
    # 创建回测节点
    node = BacktestNode(configs=[config])
    
    try:
        print("⏳ 正在运行回测...")
        await node.run_async()
        
        print("✅ 回测完成!")
        
        # 获取回测结果
        engine = node.get_engine()
        if engine:
            print("\n📊 回测结果:")
            account = engine.portfolio.account()
            print(f"账户余额: {account.balance_total()}")
            print(f"总PnL: {account.unrealized_pnl()}")
            
            # 显示订单统计
            orders = engine.cache.orders()
            print(f"总订单数: {len(orders)}")
            
            # 显示仓位统计
            positions = engine.cache.positions()
            print(f"总仓位数: {len(positions)}")
            
        return True
        
    except Exception as e:
        print(f"❌ 回测失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        await node.dispose()

async def main():
    """主函数"""
    print("=" * 60)
    print("🎯 新闻预测策略简化回测系统")
    print("=" * 60)
    
    # 运行回测
    success = await run_simple_backtest()
    
    if success:
        print("\n🎉 回测成功完成!")
        print("💡 这是一个简化版本，主要用于测试策略框架")
        print("💡 要进行完整回测，需要加载实际的K线数据")
    else:
        print("\n💥 回测失败!")
        
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
