"""
新闻PnL预测Controller

- 订阅新闻数据并加载PnL预测模型
- 动态创建和管理交易策略
- 发布预测信号给专门的交易策略
- 管理策略生命周期（创建、销毁）

用法：
    python news_pnl_controller.py
"""

import torch
import os
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, Optional

from nautilus_trader.common.component import TimeEvent
from nautilus_trader.common.config import ActorConfig
from nautilus_trader.core.correctness import PyCondition
from nautilus_trader.core.uuid import UUID4
from nautilus_trader.model.data import DataType
from nautilus_trader.model.identifiers import InstrumentId, StrategyId
from nautilus_trader.model.instruments import Instrument
from nautilus_trader.trading.controller import Controller
from nautilus_trader.trading.strategy import Strategy
from nautilus_trader.trading.trader import Trader
from nautilus_trader.model.data import BarType
from nautilus_trader.model.enums import OrderSide, TimeInForce
from nautilus_trader.model.objects import Quantity
from nautilus_trader.model.orders import MarketOrder, StopMarketOrder
from nautilus_trader.indicators.average.sma import SimpleMovingAverage
from nautilus_trader.indicators.volatility.atr import AverageTrueRange

from news_data_type import NewsData


class NewsPnlPrediction(DataType):
    """新闻PnL预测数据类型"""
    
    def __init__(self, news_id, symbol, pnl_type, confidence, ts_event, ts_init):
        self.news_id = news_id
        self.symbol = symbol
        self.pnl_type = pnl_type  
        self.confidence = confidence
        self._ts_event = ts_event
        self._ts_init = ts_init

    @property
    def ts_event(self):
        return self._ts_event

    @property
    def ts_init(self):
        return self._ts_init

    def __repr__(self):
        return f"NewsPnlPrediction(news_id={self.news_id}, symbol={self.symbol}, pnl_type={self.pnl_type}, conf={self.confidence:.2f})"


class NewsPnlControllerConfig(ActorConfig, frozen=True):
    """Controller配置"""
    model_path: str = "./best_model.pt"
    tokenizer_path: str = "./models/crypto_tokenizer_large_with_symbols.model"
    use_torchscript: bool = True
    device: str = "cpu"
    # 交易参数
    trade_size: Decimal = Decimal("100.0")  # 每次交易数量
    stop_loss_pct: Decimal = Decimal("0.02")  # 止损百分比 2%
    take_profit_pct: Decimal = Decimal("0.04")  # 止盈百分比 4%
    max_positions: int = 5  # 最大同时持仓数
    min_confidence: float = 0.7  # 最小置信度


class NewsPnlController(Controller):
    """
    新闻PnL预测Controller
    
    负责：
    1. 订阅新闻数据并预测PnL
    2. 动态创建交易策略
    3. 发布预测信号
    4. 管理策略生命周期
    """

    def __init__(
        self,
        trader: Trader,
        config: NewsPnlControllerConfig | None = None,
    ) -> None:
        if config is None:
            config = NewsPnlControllerConfig()
        PyCondition.type(config, NewsPnlControllerConfig, "config")
        super().__init__(config=config, trader=trader)

        # 模型相关
        self.model = None
        self.tokenizer = None
        self.device = torch.device(config.device)
        
        # 交易参数
        self.trade_size = config.trade_size
        self.stop_loss_pct = config.stop_loss_pct
        self.take_profit_pct = config.take_profit_pct
        self.max_positions = config.max_positions
        self.min_confidence = config.min_confidence
        
        # 策略管理
        self.active_strategies: Dict[str, Strategy] = {}
        self.strategy_positions: Dict[str, bool] = {}  # 记录策略是否有持仓
        
        # 订阅新闻数据
        self.subscribe_data(DataType(NewsData))
        self.log.info("✅ Controller已订阅NewsData")

    def on_start(self) -> None:
        """Controller启动时加载模型"""
        from pnl_training_local import CustomTokenizer, CustomBertClassifier, config as model_config
        
        try:
            self.tokenizer = CustomTokenizer(self.config.tokenizer_path)
            
            # 尝试加载TorchScript模型
            torchscript_path = self.config.model_path.replace('.pt', '_torchscript.pt')
            if self.config.use_torchscript and os.path.exists(torchscript_path):
                try:
                    self.model = torch.jit.load(torchscript_path, map_location=self.device)
                    self.model.eval()
                    self.log.info(f"✅ 已加载TorchScript模型: {torchscript_path}")
                except Exception as e:
                    self.log.warning(f"TorchScript模型加载失败: {e}，回退到PyTorch模型")
                    return
                
        except Exception as e:
            self.log.error(f"❌ 模型加载失败: {e}")
            return
    
        self.log.info(f"✅ Controller启动完成，设备: {self.device}")

    def on_data(self, data) -> None:
        """处理新闻数据并预测PnL"""
        if isinstance(data, NewsData):
            self._process_news_data(data)

    def _process_news_data(self, news_data: NewsData) -> None:
        """处理新闻数据并预测PnL"""
        try:
            # 预测PnL
            text = f"{news_data.title} {news_data.content}"
            encoding = self.tokenizer.encode(text, max_length=256)
            input_ids = encoding['input_ids'].unsqueeze(0).to(self.device)
            attention_mask = encoding['attention_mask'].unsqueeze(0).to(self.device)
            
            with torch.no_grad():
                if isinstance(self.model, torch.jit.ScriptModule):
                    logits = self.model(input_ids, attention_mask)
                else:
                    logits = self.model(input_ids, attention_mask)
                
                probs = torch.softmax(logits, dim=1).cpu().numpy()[0]
                pred = int(probs.argmax())
                confidence = float(probs[pred])
            
            # 检查置信度是否满足要求
            if confidence < self.min_confidence:
                self.log.info(f"置信度不足: {confidence:.2f} < {self.min_confidence}")
                return
            
            # 检查是否已有足够持仓
            active_positions = sum(1 for has_position in self.strategy_positions.values() if has_position)
            if active_positions >= self.max_positions:
                self.log.info(f"已达到最大持仓数: {active_positions}/{self.max_positions}")
                return
            
            # 创建预测结果
            prediction = NewsPnlPrediction(
                news_id=news_data.ts_event,
                symbol=news_data.instrument_id.value,
                pnl_type=pred,
                confidence=confidence,
                ts_event=news_data.ts_event,
                ts_init=news_data.ts_init,
            )
            
            # 发布预测信号
            self.publish_data(DataType(NewsPnlPrediction), prediction)
            self.log.info(f"🟢 发布预测: symbol={news_data.instrument_id.value}, pnl_type={pred}, conf={confidence:.2f}")
            
            # 创建或获取交易策略
            self._manage_trading_strategy(news_data.instrument_id, prediction)
            
        except Exception as e:
            self.log.error(f"处理新闻数据失败: {e}")

    def _manage_trading_strategy(self, instrument_id: InstrumentId, prediction: NewsPnlPrediction) -> None:
        """管理交易策略"""
        strategy_id = f"NewsPnlStrategy_{instrument_id.value}"
        
        # 检查策略是否已存在
        if strategy_id in self.active_strategies:
            strategy = self.active_strategies[strategy_id]
            # 如果策略没有持仓，发送预测信号
            if not self.strategy_positions.get(strategy_id, False):
                self.log.info(f"向现有策略 {strategy_id} 发送预测信号")
                # 通过消息总线发送预测信号
                self.msgbus.publish(f"strategy.{strategy_id}.prediction", prediction)
            return
        
        # 检查是否达到最大策略数量
        if len(self.active_strategies) >= self.max_positions * 2:  # 允许更多策略但限制持仓
            self.log.warning(f"已达到最大策略数量: {len(self.active_strategies)}")
            return
        
        # 创建新策略
        try:
            strategy = self._create_trading_strategy(instrument_id, strategy_id)
            if strategy:
                self.create_strategy(strategy, start=True)
                self.active_strategies[strategy_id] = strategy
                self.strategy_positions[strategy_id] = False
                self.log.info(f"✅ 创建新策略: {strategy_id}")
                
                # 发送预测信号给新策略
                self.msgbus.publish(f"strategy.{strategy_id}.prediction", prediction)
                
        except Exception as e:
            self.log.error(f"创建策略失败 {strategy_id}: {e}")

    def _create_trading_strategy(self, instrument_id: InstrumentId, strategy_id: str) -> Optional[Strategy]:
        """创建交易策略实例"""
        try:
            # 获取交易对信息
            instrument = self.cache.instrument(instrument_id)
            if not instrument:
                self.log.error(f"未找到交易对: {instrument_id}")
                return None
            
            # 创建策略配置
            config = NewsPnlTradingStrategyConfig(
                instrument_id=instrument_id,
                bar_type=BarType.from_str(f"{instrument_id.value}-5-MINUTE-LAST-EXTERNAL"),
                trade_size=self.trade_size,
                stop_loss_pct=self.stop_loss_pct,
                take_profit_pct=self.take_profit_pct,
                strategy_id=StrategyId(strategy_id),
            )
            
            # 创建策略实例
            strategy = NewsPnlTradingStrategy(config=config)
            return strategy
            
        except Exception as e:
            self.log.error(f"创建策略实例失败: {e}")
            return None

    def on_stop(self) -> None:
        """Controller停止时清理资源"""
        self.log.info("🛑 Controller停止，清理资源...")
        
        # 停止所有活跃策略
        for strategy_id, strategy in self.active_strategies.items():
            try:
                self.stop_strategy(strategy)
                self.remove_strategy(strategy)
                self.log.info(f"已停止策略: {strategy_id}")
            except Exception as e:
                self.log.error(f"停止策略失败 {strategy_id}: {e}")
        
        self.active_strategies.clear()
        self.strategy_positions.clear()


# 交易策略配置
class NewsPnlTradingStrategyConfig(ActorConfig, frozen=True):
    """新闻PnL交易策略配置"""
    instrument_id: InstrumentId
    bar_type: BarType
    trade_size: Decimal
    stop_loss_pct: Decimal
    take_profit_pct: Decimal
    strategy_id: StrategyId


class NewsPnlTradingStrategy(Strategy):
    """
    新闻PnL交易策略
    
    负责：
    1. 接收预测信号
    2. 执行交易决策
    3. 管理止损止盈
    4. 监控5分钟K线
    """

    def __init__(self, config: NewsPnlTradingStrategyConfig) -> None:
        super().__init__(config)
        
        self.instrument_id = config.instrument_id
        self.bar_type = config.bar_type
        self.trade_size = config.trade_size
        self.stop_loss_pct = config.stop_loss_pct
        self.take_profit_pct = config.take_profit_pct
        
        # 状态管理
        self.has_position = False
        self.entry_price = None
        self.entry_time = None
        self.stop_loss_order = None
        self.take_profit_order = None
        
        # 技术指标
        self.sma_20 = SimpleMovingAverage(20)
        self.atr = AverageTrueRange(14)
        
        # 订阅预测信号
        self.msgbus.subscribe(f"strategy.{self.id}.prediction", self._on_prediction_signal)

    def on_start(self) -> None:
        """策略启动"""
        # 订阅5分钟K线数据
        self.subscribe_bars(self.bar_type)
        
        # 注册技术指标
        self.register_indicator_for_bars(self.bar_type, self.sma_20)
        self.register_indicator_for_bars(self.bar_type, self.atr)
        
        self.log.info(f"✅ 策略启动: {self.id}")

    def _on_prediction_signal(self, prediction: NewsPnlPrediction) -> None:
        """处理预测信号"""
        if self.has_position:
            self.log.info(f"已有持仓，忽略预测信号: {prediction}")
            return
        
        self.log.info(f"📊 收到预测信号: {prediction}")
        
        # 根据预测类型决定交易方向
        if prediction.pnl_type == 1:  # 上涨
            self._enter_long_position()
        elif prediction.pnl_type == 0:  # 下跌
            self._enter_short_position()
        else:  # 震荡，不交易
            self.log.info("预测为震荡，不进行交易")

    def _enter_long_position(self) -> None:
        """进入多头仓位"""
        try:
            instrument = self.cache.instrument(self.instrument_id)
            if not instrument:
                self.log.error(f"未找到交易对: {self.instrument_id}")
                return
            
            # 创建市价买入订单
            order = self.order_factory.market(
                instrument_id=self.instrument_id,
                order_side=OrderSide.BUY,
                quantity=Quantity.from_decimal(self.trade_size),
                time_in_force=TimeInForce.GTC,
            )
            
            self.submit_order(order)
            self.log.info(f"🟢 提交多头订单: {order}")
            
        except Exception as e:
            self.log.error(f"进入多头仓位失败: {e}")

    def _enter_short_position(self) -> None:
        """进入空头仓位"""
        try:
            instrument = self.cache.instrument(self.instrument_id)
            if not instrument:
                self.log.error(f"未找到交易对: {self.instrument_id}")
                return
            
            # 创建市价卖出订单
            order = self.order_factory.market(
                instrument_id=self.instrument_id,
                order_side=OrderSide.SELL,
                quantity=Quantity.from_decimal(self.trade_size),
                time_in_force=TimeInForce.GTC,
            )
            
            self.submit_order(order)
            self.log.info(f"🔴 提交空头订单: {order}")
            
        except Exception as e:
            self.log.error(f"进入空头仓位失败: {e}")

    def on_bar(self, bar) -> None:
        """处理5分钟K线数据"""
        if not self.has_position:
            return
        
        # 更新技术指标
        if self.sma_20.has_inputs and self.atr.has_inputs:
            current_price = bar.close
            sma_value = self.sma_20.value
            atr_value = self.atr.value
            
            # 计算止损止盈价格
            if self.entry_price:
                if self.entry_price < current_price:  # 多头
                    stop_loss_price = current_price * (1 - self.stop_loss_pct)
                    take_profit_price = current_price * (1 + self.take_profit_pct)
                else:  # 空头
                    stop_loss_price = current_price * (1 + self.stop_loss_pct)
                    take_profit_price = current_price * (1 - self.take_profit_pct)
                
                # 检查是否需要调整止损止盈
                self._update_stop_orders(stop_loss_price, take_profit_price)

    def _update_stop_orders(self, stop_loss_price: float, take_profit_price: float) -> None:
        """更新止损止盈订单"""
        try:
            instrument = self.cache.instrument(self.instrument_id)
            if not instrument:
                return
            
            # 取消现有止损止盈订单
            if self.stop_loss_order:
                self.cancel_order(self.stop_loss_order)
            if self.take_profit_order:
                self.cancel_order(self.take_profit_order)
            
            # 创建新的止损止盈订单
            if self.has_position:
                position = self.portfolio.position(self.instrument_id)
                if position:
                    # 止损订单
                    self.stop_loss_order = self.order_factory.stop_market(
                        instrument_id=self.instrument_id,
                        order_side=OrderSide.SELL if position.is_long else OrderSide.BUY,
                        quantity=position.quantity,
                        trigger_price=stop_loss_price,
                        time_in_force=TimeInForce.GTC,
                    )
                    self.submit_order(self.stop_loss_order)
                    
                    # 止盈订单
                    self.take_profit_order = self.order_factory.stop_market(
                        instrument_id=self.instrument_id,
                        order_side=OrderSide.SELL if position.is_long else OrderSide.BUY,
                        quantity=position.quantity,
                        trigger_price=take_profit_price,
                        time_in_force=TimeInForce.GTC,
                    )
                    self.submit_order(self.take_profit_order)
                    
        except Exception as e:
            self.log.error(f"更新止损止盈订单失败: {e}")

    def on_order_accepted(self, event) -> None:
        """订单被接受"""
        self.log.info(f"订单被接受: {event.order}")

    def on_order_filled(self, event) -> None:
        """订单被成交"""
        self.log.info(f"订单被成交: {event.order}")
        
        # 更新持仓状态
        position = self.portfolio.position(self.instrument_id)
        if position and position.quantity > 0:
            self.has_position = True
            self.entry_price = event.order.avg_px
            self.entry_time = event.ts_event
            self.log.info(f"🟢 进入仓位: 价格={self.entry_price}, 数量={position.quantity}")

    def on_order_canceled(self, event) -> None:
        """订单被取消"""
        self.log.info(f"订单被取消: {event.order}")

    def on_position_closed(self, event) -> None:
        """仓位被关闭"""
        self.log.info(f"仓位被关闭: {event.position}")
        
        # 重置状态
        self.has_position = False
        self.entry_price = None
        self.entry_time = None
        self.stop_loss_order = None
        self.take_profit_order = None
        
        # 通知Controller策略已无持仓
        self.msgbus.publish(f"strategy.{self.id}.position_closed", {
            "strategy_id": str(self.id),
            "timestamp": self.clock.timestamp_ns()
        })
        
        # 策略完成任务，可以销毁
        self.log.info(f"🔄 策略 {self.id} 完成交易，准备销毁")
        self.stop()

    def on_stop(self) -> None:
        """策略停止"""
        self.log.info(f"🛑 策略停止: {self.id}")
        
        # 关闭所有持仓
        if self.has_position:
            position = self.portfolio.position(self.instrument_id)
            if position:
                # 市价平仓
                order = self.order_factory.market(
                    instrument_id=self.instrument_id,
                    order_side=OrderSide.SELL if position.is_long else OrderSide.BUY,
                    quantity=position.quantity,
                    time_in_force=TimeInForce.GTC,
                )
                self.submit_order(order)
                self.log.info(f"强制平仓: {order}")

    def on_dispose(self) -> None:
        """策略销毁"""
        self.log.info(f"🗑️ 策略销毁: {self.id}")


# 回测运行函数
def run_news_pnl_backtest():
    """运行新闻PnL预测回测"""
    from nautilus_trader.backtest.engine import BacktestEngine
    from nautilus_trader.backtest.config import BacktestEngineConfig, BacktestRunConfig
    from nautilus_trader.config import LoggingConfig
    from nautilus_trader.model.identifiers import TraderId
    from nautilus_trader.persistence.config import DataCatalogConfig
    from nautilus_trader.backtest.config import BacktestDataConfig
    from nautilus_trader.backtest.node import BacktestNode
    from nautilus_trader.config import ImportableActorConfig
    from pathlib import Path
    
    # 检查必要文件
    catalog_path = Path("./catalog")
    model_path = Path("./best_model.pt")
    tokenizer_path = Path("./models/crypto_tokenizer_large_with_symbols.model")
    
    assert catalog_path.exists(), f"catalog目录不存在: {catalog_path}"
    assert model_path.exists(), f"模型文件不存在: {model_path}"
    assert tokenizer_path.exists(), f"分词器文件不存在: {tokenizer_path}"

    # 创建catalog配置
    catalog_config = DataCatalogConfig(
        path=str(catalog_path),
        fs_protocol="file",
    )
    
    # 创建数据配置
    data_config = BacktestDataConfig(
        catalog_path=str(catalog_path),
        data_cls=f"{NewsData.__module__}:{NewsData.__name__}",
        client_id="NewsDataClient",
    )
    
    # 创建Controller配置
    controller_config = ImportableActorConfig(
        actor_path=f"{NewsPnlController.__module__}:{NewsPnlController.__name__}",
        config_path=f"{NewsPnlControllerConfig.__module__}:{NewsPnlControllerConfig.__name__}",
        config={
            "model_path": str(model_path),
            "tokenizer_path": str(tokenizer_path),
            "use_torchscript": True,
            "device": "cpu",
            "trade_size": "100.0",
            "stop_loss_pct": "0.02",
            "take_profit_pct": "0.04",
            "max_positions": 5,
            "min_confidence": 0.7,
        },
    )
    
    # 创建引擎配置
    engine_config = BacktestEngineConfig(
        trader_id=TraderId("NEWS_PNL_TRADER-001"),
        logging=LoggingConfig(
            log_level="INFO",
            bypass_logging=False,
        ),
        catalogs=[catalog_config],
        actors=[controller_config],
    )
    
    # 创建回测运行配置
    run_config = BacktestRunConfig(
        engine=engine_config,
        venues=[],
        data=[data_config],
        start="2025-01-01",
        end="2025-01-31",
    )
    
    # 创建回测节点
    node = BacktestNode(configs=[run_config])
    
    print("🚀 启动新闻PnL预测回测...")
    node.run()
    print("✅ 回测完成！")


if __name__ == "__main__":
    run_news_pnl_backtest() 