#!/usr/bin/env python
"""
新闻PNL预测策略配置文件

用于配置和运行新闻PNL预测策略的示例脚本。
"""

import asyncio
import logging
from pathlib import Path

from nautilus_trader.config import TradingNodeConfig
from nautilus_trader.config import LoggingConfig
from nautilus_trader.config import CacheConfig
from nautilus_trader.config import MessageBusConfig
from nautilus_trader.config import RiskEngineConfig
from nautilus_trader.config import ExecEngineConfig
from nautilus_trader.config import StreamingConfig
from nautilus_trader.config import ImportableStrategyConfig
from nautilus_trader.config import BacktestVenueConfig
from nautilus_trader.config import BacktestDataConfig
from nautilus_trader.config import BacktestEngineConfig
from nautilus_trader.config import BacktestRunConfig

from nautilus_trader.live.node import TradingNode
from nautilus_trader.backtest.node import BacktestNode

# 导入我们的策略
from news_pnl_prediction_strategy import NewsPnLPredictionStrategy

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def create_strategy_config():
    """创建策略配置"""
    return ImportableStrategyConfig(
        strategy_path="news_pnl_prediction_strategy:NewsPnLPredictionStrategy",
        config_path="news_pnl_prediction_strategy:NewsPnLPredictionStrategyConfig",
        config={
            "model_path": "best_model.pt",
            "tokenizer_path": "models/crypto_tokenizer_large_with_symbols.model", 
            "max_length": 256,
            "confidence_threshold": 0.7,
            "position_size_pct": 0.1,
            "enable_trading": False,  # 默认只预测不交易
        }
    )

def create_backtest_config():
    """创建回测配置"""
    
    # 数据配置
    data_config = BacktestDataConfig(
        catalog_path="/tmp/test_catalog",  # 使用我们之前创建的测试catalog
        data_cls="write_news_to_catalog:NewsData",
        client_id="BINANCE",
        venue="BINANCE",
    )
    
    # 场所配置
    venue_config = BacktestVenueConfig(
        name="BINANCE",
        oms_type="HEDGING",
        account_type="MARGIN",
        base_currency="USDT",
        starting_balances=["1000000 USDT"],
    )
    
    # 策略配置
    strategy_config = create_strategy_config()
    
    # 引擎配置
    engine_config = BacktestEngineConfig(
        strategies=[strategy_config],
        logging=LoggingConfig(log_level="INFO"),
        cache=CacheConfig(),
        message_bus=MessageBusConfig(),
        risk_engine=RiskEngineConfig(),
        exec_engine=ExecEngineConfig(),
        streaming=StreamingConfig(),
    )
    
    # 回测运行配置
    run_config = BacktestRunConfig(
        engine=engine_config,
        venues=[venue_config],
        data=[data_config],
    )
    
    return run_config

def create_live_config():
    """创建实盘配置"""
    
    # 策略配置
    strategy_config = create_strategy_config()
    
    # 交易节点配置
    config = TradingNodeConfig(
        trader_id="TRADER-001",
        strategies=[strategy_config],
        logging=LoggingConfig(log_level="INFO"),
        cache=CacheConfig(),
        message_bus=MessageBusConfig(),
        risk_engine=RiskEngineConfig(),
        exec_engine=ExecEngineConfig(),
        streaming=StreamingConfig(),
        # 这里需要添加实际的数据提供商和执行适配器配置
        # data_clients={},
        # exec_clients={},
    )
    
    return config

async def run_backtest():
    """运行回测"""
    print("开始运行新闻PNL预测策略回测...")
    
    # 创建回测配置
    config = create_backtest_config()
    
    # 创建回测节点
    node = BacktestNode(config=config)
    
    try:
        # 运行回测
        await node.run_async()
        
        print("回测完成")
        
    except Exception as e:
        print(f"回测失败: {e}")
        raise
    finally:
        await node.dispose()

async def run_live():
    """运行实盘策略"""
    print("开始运行新闻PNL预测策略实盘...")
    
    # 创建实盘配置
    config = create_live_config()
    
    # 创建交易节点
    node = TradingNode(config=config)
    
    try:
        # 启动节点
        await node.start_async()
        
        print("策略已启动，按Ctrl+C停止...")
        
        # 保持运行
        await asyncio.Event().wait()
        
    except KeyboardInterrupt:
        print("收到停止信号...")
    except Exception as e:
        print(f"策略运行失败: {e}")
        raise
    finally:
        await node.stop_async()
        await node.dispose()

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="新闻PNL预测策略")
    parser.add_argument(
        "--mode", 
        choices=["backtest", "live"], 
        default="backtest",
        help="运行模式: backtest(回测) 或 live(实盘)"
    )
    
    args = parser.parse_args()
    
    if args.mode == "backtest":
        asyncio.run(run_backtest())
    elif args.mode == "live":
        asyncio.run(run_live())

if __name__ == "__main__":
    main()
