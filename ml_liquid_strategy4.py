# -------------------------------------------------------------------------------------------------
#  Copyright (C) 2015-2025 Nautech Systems Pty Ltd. All rights reserved.
#  https://nautechsystems.io
#
#  Licensed under the GNU Lesser General Public License Version 3.0 (the "License");
#  You may not use this file except in compliance with the License.
#  You may obtain a copy of the License at https://www.gnu.org/licenses/lgpl-3.0.en.html
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
# -------------------------------------------------------------------------------------------------
import os 
import gc
from collections import deque 
import numpy as np 
from typing import Optional, List, Dict, Tuple, Any, Union
from datetime import datetime, UTC
from nautilus_trader.adapters.binance.futures.types import BinanceFuturesLiquidationOrder
from nautilus_trader.model.data import DataType
from nautilus_trader.core.data import Data
from nautilus_trader.model.data import Bar 
from nautilus_trader.model.data import BarType
from nautilus_trader.model.identifiers import ClientId
from nautilus_trader.model.identifiers import InstrumentId
from nautilus_trader.model.instruments import Instrument
from nautilus_trader.model.enums import OrderType
from nautilus_trader.model.enums import OrderSide
from nautilus_trader.model.enums import PositionSide
from nautilus_trader.core.message import Event
from nautilus_trader.indicators.atr import AverageTrueRange 
from nautilus_trader.indicators.efficiency_ratio import EfficiencyRatio
from nautilus_trader.model.enums import AggressorSide
from nautilus_trader.model.enums import TimeInForce
from nautilus_trader.model import Quantity
from nautilus_trader.indicators.mfi import MoneyFlowIndex
from nautilus_trader.indicators.bollinger_bands import BollingerBands 
from nautilus_trader.indicators.heikinashi import  HeikinAshi

from hft_toolbox.position_executor import PositionExecutor, PositionExecutorConfig
from hft_toolbox.streaming_agg_improve import MultiWindowAggregator
from hft_toolbox.trade_indicators import TradeIndicators 
from hft_toolbox.high_pass_3_filter import NumbaHighPassFilter
from hft_toolbox.liquid_indicators import generate_liquidation_configs
from ml_tools.lightgbm_model.lightgbm_tools import (
    get_bet_size,   
    predict_average_probability,
    load_lightgbm_models,
)
from nautilus_trader.model.identifiers import PositionId

import pickle
import pandas as pd


class OrderLiquidConfig(PositionExecutorConfig, kw_only=True):
    """
    Configuration for ``OrderLiquid`` instances.
    """
    instrument_id: str 
    bar_type: str 
    internal_bar_type: str
    based_bar_type: str
    request_bar_days: int 
    #position exectuor params 
    stop_loss: Optional[float]
    speed: Optional[float]
    take_profit: Optional[float]
    order_time_limit: Optional[int]
    position_time_limit: Optional[int] 
    trailing_stop: Optional[float]
    trail_stop_loss_pct: Optional[float] 
    trail_stop_order_type: OrderType = OrderType.LIMIT
    take_profit_order_type: OrderType = OrderType.LIMIT
    stop_loss_order_type: OrderType = OrderType.LIMIT
    time_limit_order_type: OrderType = OrderType.LIMIT
    atr_multiplier: Optional[float]
    tp_n_tickers : Optional[int] 

    #ml model params 
    model_prefix: str 
    model_save_dir: str 
    lgb_model_num: int 
    max_trade_usd: float 
    min_notional: float 
    liQKA_step: float


    trade_features_path: str  # Path to trade features config
    liquidation_features_path: str  # Path to liquidation features config
    liquidation_data_path: str 


class OrderLiquidStrategy(PositionExecutor):
    def __init__(self, config: OrderLiquidConfig):
        super().__init__(config)
        self.position_count = 0 
        self.instrument_id: InstrumentId = InstrumentId.from_str(config.instrument_id)
        self.bar_type: BarType = BarType.from_str(config.bar_type)
        self.internal_bar_type = BarType.from_str(config.internal_bar_type)
        self.based_bar_type: BarType = BarType.from_str(config.based_bar_type)
        self.request_bar_days = config.request_bar_days
        # 保留原有的配置参数
        self.stop_loss: Optional[float] = config.stop_loss
        self.speed: Optional[float] = config.speed 
        self.take_profit: Optional[float] = config.take_profit
        self.order_time_limit: Optional[int] = config.order_time_limit
        if self.position_time_limit:
            self.position_time_limit: Optional[int] = config.position_time_limit * 60 
        else:
            self.position_time_limit = None 
        self.atr_multiplier: Optional[float] = config.atr_multiplier
        self.trailing_stop: Optional[float] = config.trailing_stop
        self.trail_stop_loss_pct: Optional[float] = config.trail_stop_loss_pct 
        self.trail_stop_order_type: OrderType = config.trail_stop_order_type
        self.take_profit_order_type: OrderType = config.take_profit_order_type
        self.stop_loss_order_type: OrderType = config.stop_loss_order_type
        self.time_limit_order_type: OrderType = config.time_limit_order_type
        self.tp_n_tickers: Optional[int] = config.tp_n_tickers
        self.model_prefix: str = config.model_prefix
        self.lgb_model_num: int = config.lgb_model_num
        self.model_save_dir: str = config.model_save_dir
        self.max_trade_usd: float = config.max_trade_usd
        self.min_notional: float = config.min_notional
        self.liquidation_data_path: str = config.liquidation_data_path 
        self.liQKA_step: float = config.liQKA_step

    
        # 其他初始化
        self.latest_trade_features = {}
        self.latest_liquidation_features = {}
        self.latest_bar = None 
        #self.latest_filter_close = 0.0 
        self.last_liquidation_order = None 
        self.atr = AverageTrueRange(period=14)
        self.atr_array = deque(maxlen=60)
        self.efficiency_ratio = EfficiencyRatio(period=14)
        self.high_pass_filter = NumbaHighPassFilter(length=10) 
        self.mfi_6 = MoneyFlowIndex(6)
        self.mfi_14 = MoneyFlowIndex(14)
        self.mfi_480 = MoneyFlowIndex(480)
        self.bd_30 = BollingerBands(period=30,k=0.5)
        self.bd_120 = BollingerBands(period=120,k=0.5)
        self.bd_480 = BollingerBands(period=480,k=0.5)
        self.bd_960 = BollingerBands(period=960,k=0.5)
        # self.heikinashi = HeikinAshi()


         # #load lgb models
        self.lgb_models = load_lightgbm_models(
            model_prefix=self.model_prefix,
            num_models=self.lgb_model_num,
            directory=self.model_save_dir,
        )

        # Load feature configurations
        trade_feature_path = os.path.join(config.model_save_dir, config.trade_features_path)
        liquidation_feature_path = os.path.join(config.model_save_dir, config.liquidation_features_path)
        try:
            with open(trade_feature_path, "rb") as f:
                self.trade_features_order = pickle.load(f)
            with open(liquidation_feature_path, "rb") as f:
                self.liquidation_features_order = pickle.load(f)
            self.log.info("Successfully loaded feature configurations")
        except Exception as e:
            self.log.error(f"Failed to load feature configurations: {e}")
            self.stop()
        self.log.info(f"Trade features: {self.trade_features_order}")
        self.log.info(f"Liquidation features: {self.liquidation_features_order}")


    def create_feature_vector(
        self,
        liquidation_features: Dict[str, float],
        trade_features: Dict[str, float],
    ) -> Optional[np.ndarray]:
        """
        Create a feature vector in the correct order for LightGBM model input.
        """
        # Check liquidation features
        if not all(feature in liquidation_features for feature in self.liquidation_features_order):
            self.log.warning("Missing required liquidation features")
            return None
            
        # Check trade features
        if not all(feature in trade_features for feature in self.trade_features_order):
            self.log.warning("Missing required trade features")
            return None
            
        # Create feature vector in correct order
        feature_vector = np.concatenate([
            [liquidation_features[feature] for feature in self.liquidation_features_order],
            [trade_features[feature] for feature in self.trade_features_order],
        ])
        return feature_vector.reshape(1, -1)

    def load_historical_liquidation_data(
        self,
        start_time_ns: int,
        data_path: str,
    ) -> None:
        """
        Load historical liquidation data from feather files.
        
        Parameters
        ----------
        start_time_ns : int
            Start time in nanoseconds
        data_path : str
            Path to directory containing feather files
        """
        try:
            import glob
            import pyarrow.ipc as ipc
            from nautilus_trader.serialization.arrow.serializer import ArrowSerializer

            # Find feather files that are newer than start_time
            feather_pattern = f"{data_path}/*feather"
            feather_files = glob.glob(feather_pattern)
            
            # Filter files based on timestamp in filename
            relevant_files = []
            for file in feather_files:
                try:
                    # Extract timestamp from filename
                    ts = int(file.split('_')[-1].replace('.feather', ''))
                    if ts >= start_time_ns:
                        relevant_files.append(file)
                except ValueError:
                    continue

            # Collect liquidation data from files
            all_data = []
            for file in relevant_files:
                with open(file, 'rb') as source:
                    reader = ipc.open_stream(source)
                    table = reader.read_all()
                    data = ArrowSerializer.deserialize(
                        data_cls=BinanceFuturesLiquidationOrder, 
                        batch=table
                    )
                    # Filter by instrument_id and append to all_data
                    filtered_data = [item for item in data if item.instrument_id == self.instrument_id]
                    all_data.extend(filtered_data)

            # Sort data by ts_event
            all_data.sort(key=lambda x: x.ts_init)

            # Process each liquidation order
            for item in all_data:
                self.on_data(item)  # Process through existing on_data method

            del all_data  # Clear memory
            self.log.info(f"Loaded historical liquidation data from {len(relevant_files)} files")
        except Exception as e:
            self.log.error(f"Error loading historical liquidation data: {e}")
        gc.collect()

    def on_start(self):
        """Actions to be performed on strategy start."""
        self.log.info(f"on_start: {self.instrument_id}")       
        self.instrument = self.cache.instrument(self.instrument_id)

        if self.instrument is None:
            self.log.error(f"Could not find instrument for {self.instrument_id}")
            self.stop()
        # Get historical data
        current_time = pd.Timestamp(self.clock.timestamp_ns(), unit='ns', tz='UTC')
        start_time = current_time - pd.Timedelta(days=self.request_bar_days)
        start_time_ns = start_time.value
        self.subscribe_data(
            data_type=DataType(BinanceFuturesLiquidationOrder, metadata={"instrument_id": self.instrument_id}),
            client_id=ClientId("BINANCE"),
        )
        self.register_indicator_for_bars(self.internal_bar_type, self.atr)
        self.register_indicator_for_bars(self.internal_bar_type, self.efficiency_ratio)
        self.register_indicator_for_bars(self.bar_type, self.mfi_6)
        self.register_indicator_for_bars(self.bar_type, self.mfi_14)
        self.register_indicator_for_bars(self.bar_type, self.mfi_480)
        self.register_indicator_for_bars(self.bar_type, self.bd_30)
        self.register_indicator_for_bars(self.bar_type, self.bd_120)
        self.register_indicator_for_bars(self.bar_type, self.bd_480)
        self.register_indicator_for_bars(self.bar_type, self.bd_960)
        # self.register_indicator_for_bars(self.bar_type, self.heikinashi)
       
        self.request_bars(
            bar_type=self.based_bar_type,
            start=start_time.to_pydatetime(),
            end=current_time.to_pydatetime(),
        )
        self.request_bars(
            bar_type=self.internal_bar_type,
            start=start_time.to_pydatetime(),
            end=current_time.to_pydatetime(),
        )
        self.request_bars(
            bar_type=self.bar_type,
            start=start_time.to_pydatetime(),
            end=current_time.to_pydatetime(),
        )
        #subscribe real data 
        self.subscribe_bars(self.bar_type)
        self.subscribe_bars(self.internal_bar_type)
        self.subscribe_bars(self.based_bar_type)
        
        self.liquidation_aggregator = MultiWindowAggregator(
            configs=generate_liquidation_configs(self.liquidation_features_order),
            clock=self.clock,
            auto_compute=True,
        )
        
        # 初始化数据存储
        self.trade_arrays = {
            "based_ofi": deque(maxlen=1000),
            "based_close": deque(maxlen=1000),
            "based_volume": deque(maxlen=1000),
            "close": deque(maxlen=1000),
            "volume": deque(maxlen=1000),
            "ofi": deque(maxlen=1000),
            "timestamp": deque(maxlen=1000),
        }
        
        # 初始化指标计算器
        self.trade_indicators = TradeIndicators(
            trade_windows_list=[30,120,480,960],
            indicators_to_compute=self.trade_features_order
        )
        # try to load history liquid ata 
        self.load_historical_liquidation_data(start_time_ns, self.liquidation_data_path)

    def on_historical_data(self, data: Data):
        """
        preprocessing bars.
        """
        if isinstance(data, Bar):
            if data.bar_type == self.bar_type:
                self.latest_bar = data  
                self.atr_array.append(self.atr.value)
                self.latest_filter_close = self.high_pass_filter.filter(data.close.as_double())
                self.trade_arrays["close"].append(data.close.as_double())
                self.trade_arrays["volume"].append(data.volume.as_double())
                self.trade_arrays["ofi"].append(2.0 * float(data.taker_buy_base_volume) - data.volume.as_double())
                self.trade_arrays["timestamp"].append(data.ts_init) 
                if len(self.trade_arrays["close"]) >= 1000: 
                    self.latest_trade_features = self.trade_indicators.get_latest_indicators_for_aggregation_bars(
                        self.trade_arrays,
                        {
                            #extra features can not be computed by trade indicators
                            'close_corr(30)':0.0,
                            'close_corr(120)':0.0,
                            'close_corr(480)':0.0,
                            'close_corr(960)':0.0,
                            # 'ofi_corr(30)':ofi_corr_30,
                            'ofi_corr(120)':0.0,
                            'ofi_corr(480)':0.0,
                            'ofi_corr(960)':0.0,
                            'volume_corr(30)':0.0,
                            'volume_corr(120)':0.0,
                            'volume_corr(480)':0.0,
                            'volume_corr(960)':0.0,
                            # 'volume_corr_480':volume_corr_480,
                            'atr(14)':self.atr.value,
                            'efficiency_ratio(14)':self.efficiency_ratio.value,
                            "mfi(6)":self.mfi_6.value,
                            "mfi(14)":self.mfi_14.value,
                            "mfi(480)":self.mfi_480.value,
                            "bd_ratio(30)":(data.close.as_double()-self.bd_30.middle)/(self.bd_30.upper-self.bd_30.lower),
                            "bd_ratio(120)":(data.close.as_double()-self.bd_120.middle)/(self.bd_120.upper-self.bd_120.lower),
                            "bd_ratio(480)":(data.close.as_double()-self.bd_480.middle)/(self.bd_480.upper-self.bd_480.lower),
                            "bd_ratio(960)":(data.close.as_double()-self.bd_960.middle)/(self.bd_960.upper-self.bd_960.lower),
                            "count":data.count,
                            "volume":data.volume.as_double(),
                            "quote_volume":float(data.quote_volume),
                            'taker_buy_base_volume':float(data.taker_buy_base_volume),
                            'taker_buy_quote_volume':float(data.taker_buy_quote_volume),
                            'taker_sell_base_volume':float(data.taker_sell_base_volume),
                            'taker_sell_quote_volume':float(data.taker_sell_quote_volume),
                        }
                    )
            elif data.bar_type == self.based_bar_type:
                self.trade_arrays["based_close"].append(data.close.as_double())
                self.trade_arrays["based_volume"].append(data.volume.as_double())
                self.trade_arrays["based_ofi"].append(2.0 * float(data.taker_buy_base_volume) - data.volume.as_double())


    def on_bar(self, bar: Bar):
        """
        Actions to be performed when the strategy is running and receives a bar.
        Parameters
        ----------
        bar : Bar
            The bar received.
        """
        self.on_historical_data(bar)
        if bar.bar_type == self.bar_type:
            #control positon 
            if self.portfolio.is_flat(self.instrument_id):
                return 
            positions = self.cache.positions(instrument_id=self.instrument_id)
            for position in positions:
                current_position_pnl = self.current_pnl_rate(position, bar.close.as_double()) 
                self.log.info(f"current position pnl is {current_position_pnl}")
                # self.handle_er_atr_stop_loss(
                #     position=position,
                #     current_price=self.latest_bar.close.as_double(),
                #     atr_value=self.atr.value,
                #     multiplier=self.atr_multiplier,
                #     efficiency_ratio_value=self.efficiency_ratio.value,
                #     liQKA_step=0.078,
                #     min_liQKA=0.5,
                # )
                self.handle_moving_stop(
                    position=position,
                    current_price=self.latest_filter_close,
                    atr_value=self.atr.value,
                    multiplier=self.atr_multiplier,
                    liQKA_step=self.liQKA_step,
                    min_liQKA=0.01,
                )
                # self.handle_take_profit(
                #     position=position,
                #     current_price=self.latest_bar.close.as_double(),
                # )

    def on_data(self, data: Data):
        """处理清算订单数据"""
        if isinstance(data, BinanceFuturesLiquidationOrder):
            # 更新清算订单聚合器
            self.position_count += 1
            #self.log.info(f"current time stamp is {data.ts_init/1e6}")
            self.liquidation_aggregator.add(data, data.ts_init)
            self.latest_liquidation_features = {} #clear cache
            #only consider the liquidation order in the last 10 seconds
            timestamp_condition = data.ts_init >= self.clock.timestamp_ns() - 10*1e9
            if (
                self.liquidation_aggregator.initialized() and self.last_liquidation_order is not None and timestamp_condition
            ):
                self.latest_liquidation_features['avg_price'] = data.avg_price.as_double() 
                self.latest_liquidation_features['buy_sell_flag'] = 1 if data.order_side == OrderSide.BUY else -1 
                self.latest_liquidation_features["time_diff"] = (data.ts_init - self.last_liquidation_order.ts_init)/1e9 #in seconds 
                self.latest_liquidation_features["original_order_value"]  = data.price.as_double() * data.original_quantity.as_double() 
                self.latest_liquidation_features["last_fill_value"] = data.price.as_double() * data.last_filled_quantity.as_double()
                self.latest_liquidation_features["distance_to_original_value"] = self.latest_liquidation_features["last_fill_value"] - self.latest_liquidation_features["original_order_value"]
                self.latest_liquidation_features["distance_pct_to_average_price"] = (data.price.as_double() - data.avg_price.as_double())/data.avg_price.as_double()
                self.latest_liquidation_features["price_change_ratio"] = (data.price.as_double() - self.last_liquidation_order.price.as_double()) / self.last_liquidation_order.price.as_double()
                self.latest_liquidation_features["price_change_ratio_with_time"] = self.latest_liquidation_features["price_change_ratio"] / self.latest_liquidation_features["time_diff"]
                for period, config in self.liquidation_aggregator.configs.items():
                    for indicator in config.aggregators:
                        self.latest_liquidation_features[f"{indicator.name}_{period}"] = self.liquidation_aggregator.get_latest(period)[indicator.name]
            #assign the last liquidation order
            self.last_liquidation_order = data
            
            #better update for corr features 
            if len(self.trade_arrays["close"]) >= 1000 and  len(self.trade_arrays["based_volume"]) >=1000:
                    self.latest_trade_features['close_corr(30)'] = np.corrcoef(np.array(self.trade_arrays["close"])[-30:], np.array(self.trade_arrays["based_close"])[-30:])[0, 1]
                    self.latest_trade_features['close_corr(120)'] = np.corrcoef(np.array(self.trade_arrays["close"])[-120:], np.array(self.trade_arrays["based_close"])[-120:])[0, 1]
                    self.latest_trade_features['close_corr(480)'] = np.corrcoef(np.array(self.trade_arrays["close"])[-480:], np.array(self.trade_arrays["based_close"])[-480:])[0, 1]
                    self.latest_trade_features['close_corr(960)'] = np.corrcoef(np.array(self.trade_arrays["close"])[-960:], np.array(self.trade_arrays["based_close"])[-960:])[0, 1]
                    self.latest_trade_features['ofi_corr(120)'] = np.corrcoef(np.array(self.trade_arrays["ofi"])[-120:], np.array(self.trade_arrays["based_ofi"])[-120:])[0, 1]
                    self.latest_trade_features['ofi_corr(480)'] = np.corrcoef(np.array(self.trade_arrays["ofi"])[-480:], np.array(self.trade_arrays["based_ofi"])[-480:])[0, 1]
                    self.latest_trade_features['ofi_corr(960)'] = np.corrcoef(np.array(self.trade_arrays["ofi"])[-960:], np.array(self.trade_arrays["based_ofi"])[-960:])[0, 1]
                    self.latest_trade_features['volume_corr(30)'] = np.corrcoef(np.array(self.trade_arrays["volume"])[-30:], np.array(self.trade_arrays["based_volume"])[-30:])[0, 1]
                    self.latest_trade_features['volume_corr(120)'] = np.corrcoef(np.array(self.trade_arrays["volume"])[-120:], np.array(self.trade_arrays["based_volume"])[-120:])[0, 1]
                    self.latest_trade_features['volume_corr(480)'] = np.corrcoef(np.array(self.trade_arrays["volume"])[-480:], np.array(self.trade_arrays["based_volume"])[-480:])[0, 1]
                    self.latest_trade_features['volume_corr(960)'] = np.corrcoef(np.array(self.trade_arrays["volume"])[-960:], np.array(self.trade_arrays["based_volume"])[-960:])[0, 1]
            # if self.latest_liquidation_features and self.latest_trade_features:
            #     self.feature_dict = {} 
            #     self.feature_dict["timestamp"] = data.ts_init
            #     for feature in self.liquidation_features_order:
            #         self.feature_dict[feature] = self.latest_liquidation_features[feature]
            #     for feature in self.trade_features_order:
            #         self.feature_dict[feature] = self.latest_trade_features[feature]
            #     self.feature_dict["ts_event"]= self.latest_bar.ts_event
            #     self.feature_dict["open"] = self.latest_bar.open.as_double()
            #     self.feature_dict["high"] = self.latest_bar.high.as_double()
            #     self.feature_dict["low"] = self.latest_bar.low.as_double()
            #     self.feature_dict["close"] = self.latest_bar.close.as_double()
            #     self.feature_dict["volume"] = self.latest_bar.volume.as_double()

            if self.latest_liquidation_features and self.latest_trade_features:
                feature_vector = self.create_feature_vector(
                    self.latest_liquidation_features,
                    self.latest_trade_features,
                )
                if feature_vector is not None:
                    probability = predict_average_probability(self.lgb_models, feature_vector)[0]
                    #probability is three dimension
                    predict_label = np.argmax(probability)
                    predict_proba = probability[predict_label]
                    self.log.info(f" predict probility is {probability}")
                    expect_use_usd = 0 
                    if (
                        (self.portfolio.is_net_short(self.instrument_id) and predict_label == 0) or 
                        (self.portfolio.is_net_long(self.instrument_id) and predict_label == 2)
                    ):
                        #add position should reduce expect_use_usd 
                        expect_use_usd = get_bet_size(
                                prob=predict_proba,
                                max_size=self.max_trade_usd*(1.0 - abs(self.position_ratio))*np.sqrt(self.atr.value/np.mean(self.atr_array)),
                                num_classes=len(probability)
                        )
                    else:
                        expect_use_usd = get_bet_size(
                                prob=predict_proba,
                                max_size=self.max_trade_usd*np.sqrt(self.atr.value/np.mean(self.atr_array)),
                                num_classes=len(probability)
                        )
                    if predict_label == 0 and predict_proba > 0.4 and expect_use_usd > self.min_notional:
                        max_size = expect_use_usd / self.latest_bar.close.as_double()
                        self.place_limit_entry_order(
                            instrument=self.instrument,
                            entry_price=self.latest_bar.high.as_double(),
                            entry_qty=max_size,
                            entry_side=OrderSide.SELL,
                            post_only=True,
                            time_in_force=TimeInForce.GTC,
                            expire_time_s=1*60,
                        )   
                        self.log.info(f"sell {self.instrument_id.value} at {self.latest_bar.close.as_double()} with position size {max_size}")
                    elif predict_label == 2 and predict_proba > 0.4 and expect_use_usd > self.min_notional:
                        max_size = expect_use_usd / self.latest_bar.close.as_double()
                        self.place_limit_entry_order(
                            instrument=self.instrument,
                            entry_price=self.latest_bar.low.as_double(),
                            entry_qty=max_size,
                            entry_side=OrderSide.BUY,
                            post_only=True,
                            time_in_force=TimeInForce.GTC,
                            expire_time_s=1*60,
                        )   
                        self.log.info(f"buy {self.instrument_id.value} at {self.latest_bar.close.as_double()} with position size {max_size}")
        

    @property
    def position_ratio(self) -> float:
        """
        Return the ratio of current position.

        Returns
        -------
        float 
            The ratio of current position.
        """
        positions = self.cache.positions_open(instrument_id=self.instrument_id)
        value = 0.0 
        for position in positions:
            if position.side == PositionSide.LONG:
                value +=  position.avg_px_open * position.quantity.as_double()
            elif position.side == PositionSide.SHORT:
                value -= position.avg_px_open * position.quantity.as_double()
        return value / self.max_trade_usd
    

    def on_event(self, event: Event):
        """处理事件"""
        self.process_position_opened_event(event)
        self.process_position_closed_event(event)
        self.process_on_order_rejected_event(event)

    def stop(self):
        """Stop the strategy."""
        # with open(os.path.join(self.model_save_dir, "total_feature_vector.pkl"), "wb") as f:
        #     pickle.dump(self.feature_dict, f)
        super().stop()
        