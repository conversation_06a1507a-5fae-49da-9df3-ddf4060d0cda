from typing import List, Dict, Tuple, Optional
from nautilus_trader.model.identifiers import InstrumentId, Venue
import re
from collections import Counter
import multiprocessing as mp
from functools import lru_cache

class NewsInstrumentExtractor:
    """
    从预处理后的新闻内容中提取相关交易对的工具类
    优化版本：支持大小写匹配和映射还原
    """

    def __init__(self, cache, venue: Venue = Venue("BINANCE"), max_symbols: int = 3,
                 confidence_params: Optional[Dict] = None, threshold_params: Optional[Dict] = None):
        self.cache = cache
        self.venue = venue
        self.max_symbols = max_symbols

        # 置信度计算参数
        self.confidence_params = confidence_params or {
            'base_multiplier': 0.3,           # 基础匹配次数倍数
            'trading_context_bonus': 1.2,     # 交易上下文加成
            'max_confidence': 1.0             # 最大置信度
        }

        # 阈值参数 
        self.threshold_params = threshold_params or {
            'min_confidence_with_context': 0.15,    # 降低：有交易上下文时的最低置信度
            'min_confidence_without_context': 0.25, # 降低：无交易上下文时的最低置信度
            'min_match_count': 1                     # 最小匹配次数
        }

        # 黑名单（已包含稳定币，无需单独处理）
        self.blacklist = {
            # 稳定币（波动性低，不适合交易）
            'usdt', 'usdc', 'busd', 'dai', 'tusd', 'frax', 'fdusd',
            # 常见英文词汇
            'the', 'and', 'or', 'but', 'for', 'with', 'from', 'to',
            'news', 'market', 'price', 'trade', 'trading', 'crypto',
            # 单字符排除
            'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm',
            'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'y', 'z',
            # 数字和常见词
            'x', 'lab', 'labs', 'coin', 'token', 'chain', 'network',
            # 容易误识别的短词
            'one', 'op', 'ark', 'ai', 'go', 'up', 'in', 'on', 'at', 'is',
            'as', 'be', 'do', 'if', 'no', 'so', 'we', 'my', 'by', 'an',
            'date','time','title','content','http','web3','web2','rest'
            # 金融相关容易误识别的词
            'etf', 'ipo', 'ceo', 'cto', 'cfo', 'sec', 'fda', 'api','gas'
        }

        # 中英文映射表（英文已转小写）
        self.symbol_mapping = {
            # 中文映射
            '比特币': 'btc', '以太坊': 'eth', '以太币': 'eth',
            '狗狗币': 'doge', '瑞波币': 'xrp', '莱特币': 'ltc',
            '柴犬币': 'shib', '艾达币': 'ada', '波卡': 'dot',

            # 英文别名映射（转为小写）
            'bitcoin': 'btc', 'ethereum': 'eth', 'dogecoin': 'doge',
            'ripple': 'xrp', 'litecoin': 'ltc', 'cardano': 'ada',
            'polkadot': 'dot', 'chainlink': 'link', 'solana': 'sol',
            'filecoin': 'fil', 'avalanche': 'avax', 'uniswap': 'uni'
        }

        # 交易相关的关键词（英文转小写）
        self.trading_keywords = {
            '买入', '卖出', '交易', '持仓', '建仓', '减仓', '清仓', '爆仓',
            '做多', '做空', '杠杆', '合约', '现货', '期货', '期权',
            '价格', '涨幅', '跌幅', '市值', '流通量', '成交量',
            '鲸鱼', '巨鲸', '大户', '散户', '机构', '基金',
            'buy', 'sell', 'trade', 'long', 'short', 'leverage',
            'price', 'volume', 'whale', 'pump', 'dump'
        }
        self.available_symbols = []
        self.base_tokens = {}  # 初始化 base_tokens 字典

        # 预编译正则表达式模式缓存
        self._compiled_patterns = {}
        self._trading_keywords_compiled = None
        self._base_token_pattern = None
        self._full_symbol_pattern = None
        self._mapped_symbol_pattern = None


    def _precompile_patterns(self):
        """预编译所有正则表达式以提高性能"""
        # 交易关键词
        trading_keywords_pattern = '|'.join(re.escape(k) for k in self.trading_keywords)
        self._trading_keywords_compiled = re.compile(trading_keywords_pattern)

        # 基础代币 (按长度降序排序以优先匹配长名称)
        sorted_base_tokens = sorted([t for t in self.base_tokens.keys() if t not in self.blacklist], key=len, reverse=True)
        self._base_token_pattern = re.compile(r'\b(' + '|'.join(re.escape(t) for t in sorted_base_tokens) + r')\b')

        # 完整交易对 (按长度降序排序)
        sorted_symbols = sorted([s for s in self.available_symbols if s not in self.blacklist], key=len, reverse=True)
        self._full_symbol_pattern = re.compile(r'\b(' + '|'.join(re.escape(s) for s in sorted_symbols) + r')\b')

        # 映射表关键词
        mapped_patterns = []
        for keyword in self.symbol_mapping.keys():
            if self.symbol_mapping[keyword] in self.blacklist:
                continue
            if keyword.isascii():
                mapped_patterns.append(r'\b' + re.escape(keyword) + r'\b')
            else:
                mapped_patterns.append(re.escape(keyword))
        self._mapped_symbol_pattern = re.compile('|'.join(mapped_patterns))


    def _set_available_symbols(self, symbols: set[str]) -> None:
        self.available_symbols = [s.lower() for s in symbols]
        self.base_tokens = self._extract_base_tokens()
        self._precompile_patterns()


    def _get_available_symbols(self) -> set[str]:
        """
        获取所有可交易的symbol（转为小写用于匹配）

        Returns:
            set[str]: 小写symbol集合
        """
        instruments = self.cache.instruments(venue=self.venue)
        
        if hasattr(instruments, 'values'):
            instrument_list = instruments.values()
        else:
            instrument_list = instruments

        symbols_lower = {inst.id.symbol.value.lower() for inst in instrument_list}
        
        self.available_symbols = list(symbols_lower)
        self.base_tokens = self._extract_base_tokens()
        self._precompile_patterns()
        return symbols_lower
    
    def _extract_base_tokens(self) -> Dict[str, str]:
        """
        从完整symbol中提取基础代币名称，建立映射关系
        所有映射都使用小写，稳定币已在黑名单中处理

        Returns:
            Dict[str, str]: 小写基础代币 -> 小写完整symbol的映射
        """
        base_to_full = {}
        for lower_symbol in self.available_symbols:
            # 只处理USDT交易对，因为这是主要的交易对
            if lower_symbol.endswith('usdt'):
                base_token = lower_symbol[:-4]  # 移除usdt后缀

                # 处理特殊前缀的代币
                original_base_token = base_token
                normalized_base_token = self._normalize_base_token(base_token)

                # 确保基础代币不在黑名单中
                if (normalized_base_token and len(normalized_base_token) >= 2 and
                    normalized_base_token not in self.blacklist):
                    # 建立两个映射：原始代币名和标准化代币名都映射到完整symbol
                    base_to_full[original_base_token] = lower_symbol
                    if normalized_base_token != original_base_token:
                        base_to_full[normalized_base_token] = lower_symbol

        return base_to_full

    def _normalize_base_token(self, base_asset: str) -> str:
        """标准化基础代币名称，移除特殊前缀"""
        # Check for 1000000 prefix
        if base_asset.startswith('1000000'):
            return base_asset[7:]  # Remove '1000000' prefix
        # Check for 1000 prefix
        elif base_asset.startswith('1000'):
            return base_asset[4:]  # Remove '1000' prefix
        # Check for 1M prefix
        elif base_asset.startswith('1M'):
            return base_asset[2:]  # Remove '1M' prefix
        else:
            return base_asset
    
    def extract_symbols_from_text(self, preprocessed_title: str, preprocessed_content: str) -> List[Tuple[str, int, float]]:
        """
        从预处理后的新闻内容中提取交易对symbols

        Args:
            preprocessed_title: 预处理后的标题（已转小写）
            preprocessed_content: 预处理后的内容（已转小写）

        Returns:
            List[Tuple[original_case_symbol, match_count, confidence]]
        """
        combined_text = f"{preprocessed_title} {preprocessed_content}"
        symbol_matches = Counter()
        extraction_sources = {}

        # 使用预编译的正则表达式进行一次性扫描
        # 1. 映射表提取
        for match in self._mapped_symbol_pattern.finditer(combined_text):
            keyword = match.group(0)
            base_token = self.symbol_mapping.get(keyword.lower())
            if base_token:
                full_symbol = self.base_tokens.get(base_token)
                if full_symbol:
                    symbol_matches[full_symbol] += 1
                    extraction_sources[full_symbol] = 'mapping'

        # 2. 基础代币提取
        for match in self._base_token_pattern.finditer(combined_text):
            base_token = match.group(1)
            full_symbol = self.base_tokens.get(base_token)
            if full_symbol and full_symbol not in extraction_sources:
                symbol_matches[full_symbol] += 1
                extraction_sources[full_symbol] = 'base_token'

        # 3. 完整交易对提取
        for match in self._full_symbol_pattern.finditer(combined_text):
            full_symbol = match.group(1)
            if full_symbol not in extraction_sources:
                symbol_matches[full_symbol] += 1
                extraction_sources[full_symbol] = 'full_symbol'

        has_trading_context = bool(self._trading_keywords_compiled.search(combined_text))

        filtered_matches = self._filter_and_rank_symbols(symbol_matches, has_trading_context, extraction_sources)

        return filtered_matches[:self.max_symbols]

    def _has_trading_context(self, text: str) -> bool:
        """检查文本是否包含交易相关内容（使用预编译正则）"""
        return bool(self._trading_keywords_compiled.search(text))
    
    def _extract_full_symbols(self, text: str) -> Counter:
        """提取完整的symbols（使用预编译正则）"""
        matches = Counter()
        for match in self._full_symbol_pattern.finditer(text):
            matches[match.group(1)] += 1
        return matches
    
    def _extract_base_tokens_from_text(self, text: str) -> Counter:
        """从文本中提取基础代币名称（使用预编译正则）"""
        matches = Counter()
        for match in self._base_token_pattern.finditer(text):
            base_token = match.group(1)
            full_symbol = self.base_tokens[base_token]
            matches[full_symbol] += 1
        return matches
    
    def _extract_mapped_symbols(self, text: str) -> Counter:
        """通过映射表提取symbols（使用预编译正则）"""
        matches = Counter()
        for match in self._mapped_symbol_pattern.finditer(text):
            keyword = match.group(0).lower()
            base_token = self.symbol_mapping.get(keyword)
            if base_token:
                full_symbol = self.base_tokens.get(base_token)
                if full_symbol:
                    matches[full_symbol] += 1
        return matches
    
    def _filter_and_rank_symbols(self, symbol_matches: Counter, has_trading_context: bool,
                                extraction_sources: Dict[str, str] = None) -> List[Tuple[str, int, float]]:
        """
        过滤和排序symbols，并还原为大写

        Args:
            symbol_matches: symbol匹配计数
            has_trading_context: 是否有交易上下文
            extraction_sources: symbol的提取来源映射

        Returns:
            List[Tuple[str, int, float]]: (大写symbol, 匹配次数, 置信度)
        """
        results = []
        extraction_sources = extraction_sources or {}

        for lower_symbol, count in symbol_matches.items():
            # 跳过黑名单（稳定币已在黑名单中）
            if lower_symbol in self.blacklist:
                continue

            # 获取提取来源
            source = extraction_sources.get(lower_symbol, 'unknown')

            # 计算置信度
            confidence = self._calculate_confidence(lower_symbol, count, has_trading_context, source)

            # 使用参数化阈值
            min_confidence = (self.threshold_params['min_confidence_with_context']
                            if has_trading_context
                            else self.threshold_params['min_confidence_without_context'])

            if confidence >= min_confidence:
                # 根据命名规则恢复：全部转为大写
                upper_symbol = lower_symbol.upper()
                results.append((upper_symbol, count, confidence))

        # 按置信度排序，置信度相同时按匹配次数排序
        results.sort(key=lambda x: (x[2], x[1]), reverse=True)

        return results


    
    def _calculate_confidence(self, lower_symbol: str, match_count: int, has_trading_context: bool,
                             extraction_source: str = 'unknown') -> float:
        """
        计算提取置信度（基于小写symbol）

        Args:
            lower_symbol: 小写的symbol
            match_count: 匹配次数
            has_trading_context: 是否有交易上下文
            extraction_source: 提取来源 ('mapping', 'base_token', 'full_symbol')

        Returns:
            float: 置信度 (0.0-1.0)
        """
        if match_count < self.threshold_params['min_match_count']:
            return 0.0

        # 基础置信度：基于匹配次数
        base_confidence = min(match_count * self.confidence_params['base_multiplier'],
                             self.confidence_params['max_confidence'])

        # 根据symbol特征调整置信度
        base_token = self._extract_base_token_from_symbol(lower_symbol)

        # 交易上下文加成
        if has_trading_context:
            base_confidence *= self.confidence_params['trading_context_bonus']

        # 根据提取来源调整
        base_confidence *= self._get_source_multiplier(extraction_source)

        # 特殊规则：多次匹配的额外加成
        if match_count >= 3:
            base_confidence *= 1.1

        return min(base_confidence, self.confidence_params['max_confidence'])

    def _extract_base_token_from_symbol(self, lower_symbol: str) -> str:
        """从symbol中提取基础代币名称"""
        # 移除常见的交易对后缀
        for suffix in ['usdt', 'usdc', 'btc', 'eth', 'bnb']:
            if lower_symbol.endswith(suffix):
                return lower_symbol[:-len(suffix)]
        return lower_symbol

    def _get_source_multiplier(self, extraction_source: str) -> float:
        """根据提取来源获取置信度倍数"""
        source_multipliers = {
            'mapping': 1.0,      # 映射表来源（最可靠）
            'base_token': 0.95,  # 基础代币匹配
            'full_symbol': 0.9,  # 完整symbol匹配
            'unknown': 0.8       # 未知来源
        }
        return source_multipliers.get(extraction_source, 0.8)
    
    def extract_instrument_ids(self, preprocessed_title: str, preprocessed_content: str) -> List[InstrumentId]:
        """
        从预处理后的文本中提取InstrumentId列表

        Args:
            preprocessed_title: 预处理后的标题（已转小写）
            preprocessed_content: 预处理后的内容（已转小写）

        Returns:
            List[InstrumentId]: 按置信度排序的instrument列表
        """
        symbols_with_confidence = self.extract_symbols_from_text(preprocessed_title, preprocessed_content)
        instrument_ids = []

        for symbol, count, confidence in symbols_with_confidence:
            # 构造完整的instrument_id: BTCUSDT-PERP.BINANCE
            instrument_str = f"{symbol}-PERP.{self.venue.value}"
            try:
                instrument_id = InstrumentId.from_str(instrument_str)
                instrument_ids.append(instrument_id)
            except Exception as e:
                # 如果构造失败，跳过这个symbol
                continue

        return instrument_ids

    def get_config_info(self) -> Dict:
        """获取配置信息"""
        return {
            'confidence_params': self.confidence_params,
            'threshold_params': self.threshold_params,
            'max_symbols': self.max_symbols,
            'venue': str(self.venue),
            'available_symbols_count': len(self.available_symbols),
            'base_tokens_count': len(self.base_tokens),
            'symbol_mapping_count': len(self.symbol_mapping),
            'blacklist_count': len(self.blacklist)
        }

    def update_confidence_params(self, new_params: Dict) -> None:
        """更新置信度参数"""
        self.confidence_params.update(new_params)

    def update_threshold_params(self, new_params: Dict) -> None:
        """更新阈值参数"""
        self.threshold_params.update(new_params)



def process_news_file(args):
    """
    处理单个新闻文件，用于并行化。
    """
    news_file, news_dir, news_data_filtered_dir, extractor_params = args
    import pandas as pd
    import os

    # 在子进程中重新创建提取器实例
    extractor = NewsInstrumentExtractor(
        cache=None,
        venue=Venue(extractor_params['venue']),
        max_symbols=extractor_params['max_symbols'],
        confidence_params=extractor_params['confidence_params'],
        threshold_params=extractor_params['threshold_params']
    )
    extractor._set_available_symbols(set(extractor_params['available_symbols']))

    input_path = os.path.join(news_dir, news_file)
    output_path = os.path.join(news_data_filtered_dir, news_file)

    try:
        df = pd.read_csv(input_path, encoding='utf-8')

        def extract_symbols_for_row(row):
            title = str(row.get('title', '')).lower()
            content = str(row.get('content', '')).lower()
            symbols_with_confidence = extractor.extract_symbols_from_text(title, content)
            if symbols_with_confidence:
                return ','.join([s[0] for s in symbols_with_confidence])
            return ''

        df['relative_symbols'] = df.apply(extract_symbols_for_row, axis=1)
        
        df_filtered = df[df['relative_symbols'] != ''].copy()

        if not df_filtered.empty:
            df_filtered.to_csv(output_path, index=False, encoding='utf-8-sig')
        
        return len(df), len(df_filtered)

    except Exception as e:
        print(f"处理文件 {news_file} 时出错: {e}")
        return 0, 0

def process_news_data():
    """
    主要处理函数：从新闻数据中提取symbols并保存到过滤后的目录（并行优化版）
    """
    import pandas as pd
    import json
    import os
    from pathlib import Path
    from multiprocessing import Pool, cpu_count
    from tqdm import tqdm

    # 加载 Binance symbols
    with open('binance_symbols.json', 'r') as f:
        binance_data = json.load(f)
    available_symbols = list(binance_data.keys())

    # 提取器参数
    extractor_params = {
        'venue': "BINANCE",
        'max_symbols': 5,
        'confidence_params': {
            'base_multiplier': 0.25,
            'trading_context_bonus': 1.15, 
            'max_confidence': 1.0
        },
        'threshold_params': {
            'min_confidence_with_context': 0.1, 
            'min_confidence_without_context': 0.1,
            'high_confidence_threshold': 0.6, 
            'min_match_count': 1
        },
        'available_symbols': available_symbols
    }

    # 创建输出目录
    news_dir = "news_data"
    news_data_filtered_dir = "filter_news"
    Path(news_data_filtered_dir).mkdir(exist_ok=True)

    news_files = sorted([f for f in os.listdir(news_dir) if f.endswith('.csv')])
    
    # 设置并行处理
    num_processes = cpu_count()
    pool_args = [(f, news_dir, news_data_filtered_dir, extractor_params) for f in news_files]

    total_processed = 0
    total_with_symbols = 0

    print(f"开始使用 {num_processes} 个进程处理 {len(news_files)} 个新闻文件...")

    with Pool(processes=num_processes) as pool:
        results = list(tqdm(pool.imap(process_news_file, pool_args), total=len(news_files)))

    for processed_count, with_symbols_count in results:
        total_processed += processed_count
        total_with_symbols += with_symbols_count

    print(f"\n处理完成!")
    if total_processed > 0:
        print(f"总计处理: {total_processed} 条新闻")
        print(f"提取到symbols: {total_with_symbols} 条新闻")
        print(f"过滤率: {(total_with_symbols / total_processed) * 100:.2f}%")
    else:
        print("没有处理任何新闻。")


def test_single_file():
    """测试单个文件的处理"""
    import pandas as pd
    import json

    # 加载 Binance symbols
    with open('binance_symbols.json', 'r') as f:
        binance_data = json.load(f)

    available_symbols = set(binance_data.keys())

    # 创建提取器实例
    extractor = NewsInstrumentExtractor(
        cache=None,
        venue=Venue("BINANCE"),
        max_symbols=5,
        confidence_params={
            'base_multiplier': 0.25,
            'trading_context_bonus': 1.15,
            'max_confidence': 1.0
        },
        threshold_params={
            'min_confidence_with_context': 0.15,
            'min_confidence_without_context': 0.25,
            'high_confidence_threshold': 0.6,
            'min_match_count': 1
        }
    )

    extractor._set_available_symbols(available_symbols)

    # 测试单个文件
    test_file = "news_data/panews_flash_20250101.csv"
    df = pd.read_csv(test_file, encoding='utf-8')

    print(f"原始数据列: {list(df.columns)}")
    print(f"原始数据行数: {len(df)}")

    # 添加 relative_symbols 列
    df['relative_symbols'] = ''

    # 处理前几行作为测试
    for idx in range(min(5, len(df))):
        row = df.iloc[idx]
        title = str(row.get('title', '')).lower()
        content = str(row.get('content', '')).lower()

        print(f"\n处理第 {idx+1} 行:")
        print(f"标题: {row.get('title', '')[:50]}...")

        symbols_with_confidence = extractor.extract_symbols_from_text(title, content)

        if symbols_with_confidence:
            symbols = [symbol for symbol, count, confidence in symbols_with_confidence]
            df.at[idx, 'relative_symbols'] = ','.join(symbols)
            print(f"提取到的symbols: {symbols}")
        else:
            print("未提取到symbols")

    # 检查结果
    print(f"\n处理后数据列: {list(df.columns)}")
    print(f"有symbols的行数: {len(df[df['relative_symbols'] != ''])}")

    # 保存测试结果
    test_output = "test_output.csv"
    df_filtered = df[df['relative_symbols'] != ''].copy()
    df_filtered.to_csv(test_output, index=False, encoding='utf-8-sig')
    print(f"测试结果已保存到: {test_output}")


if __name__ == "__main__":
    import sys

    # if len(sys.argv) > 1 and sys.argv[1] == "test":
    #     # 运行测试
    #     test_single_file()
    # else:
    #     # 运行完整处理
    process_news_data()
    
