#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理 alias_symbols.txt 文件
1. 确保 key 英文全部为小写
2. 确保 value 英文全部为大写
3. 去除重复的元素
4. 保持中文字符不变
5. 保留注释和格式
"""

import re
import json
from collections import OrderedDict

def is_chinese_char(char):
    """判断字符是否为中文"""
    return '\u4e00' <= char <= '\u9fff'

def process_key(key):
    """处理key：英文转小写，中文保持不变"""
    result = ""
    for char in key:
        if is_chinese_char(char):
            result += char
        else:
            result += char.lower()
    return result

def process_value(value):
    """处理value：英文转大写，中文保持不变"""
    result = ""
    for char in value:
        if is_chinese_char(char):
            result += char
        else:
            result += char.upper()
    return result

def parse_alias_file(file_path):
    """解析alias文件，提取键值对和注释"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 存储解析结果
    aliases = OrderedDict()
    comments = []
    current_comment = ""
    
    lines = content.split('\n')
    
    for line in lines:
        line = line.strip()
        
        # 跳过空行和大括号
        if not line or line in ['{', '}']:
            continue
        
        # 处理注释行
        if line.startswith('#'):
            current_comment = line
            comments.append(line)
            continue
        
        # 处理键值对行
        # 匹配格式: "key": "value", 或 "key": "value",#comment
        match = re.match(r'"([^"]+)":\s*"([^"]+)"(?:,?\s*#.*)?', line)
        if match:
            key = match.group(1)
            value = match.group(2)
            
            # 处理key和value
            processed_key = process_key(key)
            processed_value = process_value(value)
            
            # 存储，如果key已存在则跳过（去重）
            if processed_key not in aliases:
                aliases[processed_key] = processed_value
                print(f"添加映射: '{processed_key}' -> '{processed_value}'")
            else:
                print(f"跳过重复key: '{processed_key}' (原值: '{aliases[processed_key]}', 新值: '{processed_value}')")
    
    return aliases, comments

def write_processed_file(aliases, output_path):
    """写入处理后的文件"""
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write('{\n')
        
        # 按类别组织输出
        categories = {
            'major': [],
            'layer2': [],
            'defi': [],
            'ai': [],
            'storage': [],
            'gaming': [],
            'meme': [],
            'stable': [],
            'privacy': [],
            'oracle': [],
            'rwa': [],
            'emerging': [],
            'other': []
        }
        
        # 分类关键词
        category_keywords = {
            'major': ['bitcoin', 'ethereum', 'binance', 'ripple', 'cardano', 'solana', 'dogecoin', 'polkadot', 'chainlink', 'litecoin', 'avalanche', 'polygon', 'tron', 'cosmos', 'near', 'aptos', 'sui', 'internet computer', 'celestia', 'injective', 'sei'],
            'layer2': ['arbitrum', 'optimism', 'starknet', 'immutable', 'loopring', 'base', 'bnb chain'],
            'defi': ['uniswap', 'aave', 'compound', 'makerdao', 'curve', 'sushiswap', 'synthetix', 'yearn', '1inch', 'balancer', 'lido'],
            'ai': ['render', 'fetch', 'singularitynet', 'numerai', 'graph'],
            'storage': ['filecoin', 'storj', 'helium', 'arweave'],
            'gaming': ['sandbox', 'decentraland', 'axie', 'gala', 'enjin', 'flow'],
            'meme': ['shiba', 'floki', 'pepe', 'doge'],
            'stable': ['usd coin', 'dai', 'frax', 'trueusd', 'tether'],
            'privacy': ['zcash', 'dash'],
            'oracle': ['pyth', 'band', 'chainlink'],
            'rwa': ['ondo'],
            'emerging': ['pump.fun', 'fantom', 'ether.fi', 'berachain', 'story', 'kiloex', 'lumoz']
        }
        
        # 分类别名
        for key, value in aliases.items():
            categorized = False
            for category, keywords in category_keywords.items():
                if any(keyword in key.lower() for keyword in keywords):
                    categories[category].append((key, value))
                    categorized = True
                    break
            
            if not categorized:
                categories['other'].append((key, value))
        
        # 写入各类别
        category_titles = {
            'major': '# --- Major Cryptocurrencies & Blockchains ---',
            'layer2': '# --- Layer 2 Solutions ---',
            'defi': '# --- DeFi Protocols ---',
            'ai': '# --- AI & Computing Tokens ---',
            'storage': '# --- Storage & Infrastructure ---',
            'gaming': '# --- Gaming & Metaverse ---',
            'meme': '# --- Meme Coins ---',
            'stable': '# --- Stablecoins ---',
            'privacy': '# --- Privacy Coins ---',
            'oracle': '# --- Oracle & Data ---',
            'rwa': '# --- RWA (Real World Assets) ---',
            'emerging': '# --- Emerging Projects ---',
            'other': '# --- Other Projects ---'
        }
        
        first_category = True
        for category, title in category_titles.items():
            if categories[category]:
                if not first_category:
                    f.write('\n')
                f.write(f'    {title}\n')
                
                for key, value in sorted(categories[category]):
                    f.write(f'    "{key}": "{value}",\n')
                
                first_category = False
        
        f.write('}\n')

def generate_statistics(aliases):
    """生成统计信息"""
    print(f"\n=== 处理统计 ===")
    print(f"总映射数量: {len(aliases)}")
    
    # 统计中文key数量
    chinese_keys = sum(1 for key in aliases.keys() if any(is_chinese_char(c) for c in key))
    print(f"包含中文的key: {chinese_keys}")
    
    # 统计value分布
    value_counts = {}
    for value in aliases.values():
        value_counts[value] = value_counts.get(value, 0) + 1
    
    print(f"不同代币数量: {len(value_counts)}")
    
    # 显示最常见的代币
    print(f"\n最常见的代币映射:")
    sorted_values = sorted(value_counts.items(), key=lambda x: x[1], reverse=True)
    for value, count in sorted_values[:10]:
        print(f"  {value}: {count} 个别名")
    
    return len(aliases), chinese_keys, len(value_counts)

def backup_original_file(input_file):
    """备份原文件"""
    import shutil
    from datetime import datetime

    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_file = f"{input_file}.backup_{timestamp}"
    shutil.copy2(input_file, backup_file)
    print(f"原文件已备份到: {backup_file}")
    return backup_file

def validate_processed_data(aliases):
    """验证处理后的数据"""
    issues = []

    # 检查空key或空value
    for key, value in aliases.items():
        if not key.strip():
            issues.append("发现空的key")
        if not value.strip():
            issues.append(f"key '{key}' 对应的value为空")

    # 检查key是否正确转换为小写
    for key in aliases.keys():
        if any(c.isupper() and not is_chinese_char(c) for c in key):
            issues.append(f"key '{key}' 包含未转换的大写英文字符")

    # 检查value是否正确转换为大写
    for key, value in aliases.items():
        if any(c.islower() and not is_chinese_char(c) for c in value):
            issues.append(f"value '{value}' (key: '{key}') 包含未转换的小写英文字符")

    return issues

def main():
    """主函数"""
    input_file = 'process/alias_symbols.txt'
    output_file = 'process/alias_symbols_processed.txt'

    print("处理 alias_symbols.txt 文件...")
    print("=" * 50)

    try:
        # 备份原文件
        backup_file = backup_original_file(input_file)

        # 解析文件
        aliases, comments = parse_alias_file(input_file)

        # 验证处理后的数据
        issues = validate_processed_data(aliases)
        if issues:
            print(f"\n发现 {len(issues)} 个问题:")
            for issue in issues[:10]:  # 只显示前10个问题
                print(f"  - {issue}")
            if len(issues) > 10:
                print(f"  ... 还有 {len(issues) - 10} 个问题")

        # 生成统计
        total_aliases, chinese_keys, unique_tokens = generate_statistics(aliases)

        # 写入处理后的文件
        write_processed_file(aliases, output_file)

        print(f"\n处理完成!")
        print(f"输入文件: {input_file}")
        print(f"输出文件: {output_file}")
        print(f"备份文件: {backup_file}")
        print(f"处理结果: {total_aliases} 个映射, {chinese_keys} 个中文key, {unique_tokens} 个不同代币")

        # 显示一些示例
        print(f"\n处理示例:")
        sample_count = 0
        for key, value in list(aliases.items())[:5]:
            print(f"  '{key}' -> '{value}'")
            sample_count += 1

        if len(aliases) > 5:
            print(f"  ... 还有 {len(aliases) - 5} 个映射")

        # 询问是否替换原文件
        print(f"\n是否要用处理后的文件替换原文件? (y/n): ", end="")
        try:
            response = input().strip().lower()
            if response in ['y', 'yes', '是']:
                import shutil
                shutil.copy2(output_file, input_file)
                print(f"已用处理后的文件替换原文件: {input_file}")
            else:
                print(f"保持原文件不变，处理结果保存在: {output_file}")
        except:
            print(f"无法获取用户输入，处理结果保存在: {output_file}")

    except Exception as e:
        print(f"处理失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
