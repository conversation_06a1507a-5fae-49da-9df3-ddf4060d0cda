#!/usr/bin/env python3
"""
错误分析系统
深入分析哪些样本总是被分类错误，找出问题根源
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import cross_val_predict, StratifiedKFold
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.preprocessing import LabelEncoder
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter, defaultdict
import re
from typing import List, Dict, Tuple
import warnings
warnings.filterwarnings('ignore')

# 导入专业预处理器
from news_data_preprocessor import NewsDataPreprocessor

class ErrorAnalysisSystem:
    """错误分析系统"""
    
    def __init__(self, data_dir: str = "filter_news"):
        self.data_dir = data_dir
        self.preprocessor = NewsDataPreprocessor(data_dir)
        self.class_names = ['强跌', '弱跌', '横盘', '弱涨', '强涨']
        
        # 高价值关键词
        self.high_value_keywords = {
            'policy_regulatory': [
                'sec', 'etf', '监管', '政策', '法案', '批准', '申请', '禁令',
                'trump', '特朗普', '总统', '政府', '央行', '美联储'
            ],
            'institutional': [
                '机构', '投资', '基金', '银行', '公司', '购买', '持有', '储备',
                'microstrategy', 'tesla', 'blackrock', '灰度'
            ],
            'exchange': [
                '交易所', '上线', '下架', '币安', 'binance', 'coinbase', 
                '合约', '现货', '期货', '杠杆'
            ],
            'technology': [
                '升级', '分叉', '网络', '协议', '更新', '漏洞', '安全',
                '以太坊', 'ethereum', '比特币', 'bitcoin'
            ],
            'market_movement': [
                '突破', '新高', '暴跌', '暴涨', '回调', '支撑', '阻力',
                '万美元', '亿美元', '市值', '流入', '流出'
            ]
        }
        
        # 噪音关键词
        self.noise_keywords = [
            '据悉', '据报道', '消息称', '有消息称', '据了解', '据消息',
            '市场传言', '小道消息', '网传', '爆料', '疑似',
            '分析师认为', '专家表示', '业内人士', '知情人士',
            '或将', '可能', '预计', '预期', '有望', '据说'
        ]
    
    def load_and_prepare_data(self, min_value_score: float = 2.0):
        """加载和准备数据"""
        
        print("📊 加载和准备数据...")
        
        # 1. 使用专业预处理器
        df = self.preprocessor.load_labeled_data()
        df_expanded = self.preprocessor.split_multi_symbol_news(df)
        
        # 2. 关键词筛选
        df_expanded['combined_text'] = df_expanded['title'].fillna('') + ' ' + df_expanded['content'].fillna('')
        df_expanded['value_score'] = df_expanded['combined_text'].apply(self.calculate_news_value_score)
        high_value_df = df_expanded[df_expanded['value_score'] >= min_value_score].copy()
        
        # 3. 创建特征
        X, y, processed_df = self.preprocessor.create_features(high_value_df)
        
        print(f"数据准备完成: {len(processed_df)} 条样本")
        
        return processed_df, X, y
    
    def calculate_news_value_score(self, text: str) -> float:
        """计算新闻价值分数"""
        
        text_lower = text.lower()
        value_score = 0.0
        
        # 高价值关键词加分
        for category, keywords in self.high_value_keywords.items():
            category_score = 0
            for keyword in keywords:
                if keyword.lower() in text_lower:
                    category_score += 1
            
            if category == 'policy_regulatory':
                value_score += category_score * 3.0
            elif category == 'institutional':
                value_score += category_score * 2.5
            elif category == 'exchange':
                value_score += category_score * 2.0
            else:
                value_score += category_score * 1.5
        
        # 噪音关键词减分
        noise_count = sum(1 for noise_word in self.noise_keywords if noise_word in text)
        value_score -= noise_count * 0.5
        
        # 文本长度因子
        text_length = len(text)
        if text_length < 50:
            value_score -= 1.0
        elif text_length > 1000:
            value_score -= 0.5
        
        # 数字密度因子
        numbers = re.findall(r'\d+', text)
        if len(numbers) > 0:
            value_score += min(len(numbers) * 0.2, 1.0)
        
        return max(value_score, 0.0)
    
    def perform_cross_validation_error_analysis(self, processed_df: pd.DataFrame, X, y):
        """执行交叉验证错误分析"""
        
        print("\n🔍 执行交叉验证错误分析...")
        
        # 设置交叉验证
        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
        
        # 训练模型并获取预测
        classifier = RandomForestClassifier(
            n_estimators=200,
            max_depth=15,
            random_state=42,
            class_weight='balanced'
        )
        
        # 交叉验证预测
        y_pred = cross_val_predict(classifier, X, y, cv=cv)
        
        # 添加预测结果到DataFrame
        analysis_df = processed_df.copy()
        analysis_df['true_label'] = y
        analysis_df['pred_label'] = y_pred
        analysis_df['is_correct'] = (y == y_pred)
        analysis_df['true_class_name'] = [self.class_names[i] for i in y]
        analysis_df['pred_class_name'] = [self.class_names[i] for i in y_pred]
        
        print(f"总体准确率: {np.mean(y == y_pred):.4f}")
        
        return analysis_df, y_pred
    
    def analyze_confusion_patterns(self, y_true, y_pred):
        """分析混淆模式"""
        
        print("\n📊 分析混淆模式...")
        
        # 混淆矩阵
        cm = confusion_matrix(y_true, y_pred)
        
        # 可视化混淆矩阵
        plt.figure(figsize=(10, 8))
        sns.heatmap(cm, annot=True, fmt='d', 
                   xticklabels=self.class_names,
                   yticklabels=self.class_names,
                   cmap='Blues')
        plt.title('混淆矩阵')
        plt.ylabel('真实标签')
        plt.xlabel('预测标签')
        plt.tight_layout()
        plt.savefig('confusion_matrix_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 分析最常见的错误类型
        print("\n最常见的分类错误:")
        error_counts = defaultdict(int)
        
        for true_label, pred_label in zip(y_true, y_pred):
            if true_label != pred_label:
                error_type = f"{self.class_names[true_label]} → {self.class_names[pred_label]}"
                error_counts[error_type] += 1
        
        # 按错误数量排序
        sorted_errors = sorted(error_counts.items(), key=lambda x: x[1], reverse=True)
        
        for i, (error_type, count) in enumerate(sorted_errors[:10]):
            percentage = count / len(y_true) * 100
            print(f"  {i+1:2d}. {error_type}: {count} 次 ({percentage:.2f}%)")
        
        return sorted_errors
    
    def analyze_consistently_wrong_samples(self, analysis_df: pd.DataFrame):
        """分析总是被分类错误的样本"""
        
        print("\n🎯 分析总是被分类错误的样本...")
        
        # 找出错误样本
        wrong_samples = analysis_df[~analysis_df['is_correct']].copy()
        
        print(f"错误样本数量: {len(wrong_samples)} / {len(analysis_df)} ({len(wrong_samples)/len(analysis_df)*100:.1f}%)")
        
        # 按错误类型分组分析
        error_analysis = {}
        
        for true_class in range(5):
            for pred_class in range(5):
                if true_class != pred_class:
                    mask = (wrong_samples['true_label'] == true_class) & (wrong_samples['pred_label'] == pred_class)
                    error_samples = wrong_samples[mask]
                    
                    if len(error_samples) > 0:
                        error_type = f"{self.class_names[true_class]} → {self.class_names[pred_class]}"
                        error_analysis[error_type] = {
                            'count': len(error_samples),
                            'samples': error_samples,
                            'percentage': len(error_samples) / len(analysis_df) * 100
                        }
        
        # 分析最严重的错误类型
        sorted_error_types = sorted(error_analysis.items(), key=lambda x: x[1]['count'], reverse=True)
        
        print(f"\n最严重的错误类型分析:")
        for i, (error_type, info) in enumerate(sorted_error_types[:5]):
            print(f"\n{i+1}. {error_type} ({info['count']} 样本, {info['percentage']:.2f}%)")
            
            # 分析这类错误的特征
            samples = info['samples']
            self._analyze_error_type_characteristics(samples, error_type)
    
    def _analyze_error_type_characteristics(self, samples: pd.DataFrame, error_type: str):
        """分析特定错误类型的特征"""
        
        print(f"   特征分析:")
        
        # 1. 文本长度分析
        text_lengths = samples['processed_text'].str.len()
        print(f"   - 平均文本长度: {text_lengths.mean():.0f} 字符")
        print(f"   - 文本长度范围: {text_lengths.min()}-{text_lengths.max()}")
        
        # 2. 价值分数分析
        if 'value_score' in samples.columns:
            print(f"   - 平均价值分数: {samples['value_score'].mean():.2f}")
            print(f"   - 价值分数范围: {samples['value_score'].min():.2f}-{samples['value_score'].max():.2f}")
        
        # 3. 收益率分析
        if 'future_returns' in samples.columns:
            returns = pd.to_numeric(samples['future_returns'], errors='coerce')
            print(f"   - 平均收益率: {returns.mean():.4f}")
            print(f"   - 收益率标准差: {returns.std():.4f}")
        
        # 4. 关键词分析
        all_text = ' '.join(samples['processed_text'].fillna(''))
        
        # 统计高价值关键词出现频率
        keyword_counts = defaultdict(int)
        for category, keywords in self.high_value_keywords.items():
            for keyword in keywords:
                count = all_text.lower().count(keyword.lower())
                if count > 0:
                    keyword_counts[f"{category}:{keyword}"] = count
        
        if keyword_counts:
            print(f"   - 高频关键词:")
            sorted_keywords = sorted(keyword_counts.items(), key=lambda x: x[1], reverse=True)
            for keyword, count in sorted_keywords[:5]:
                print(f"     * {keyword}: {count} 次")
        
        # 5. 显示典型错误样本
        print(f"   - 典型错误样本:")
        for i, (_, sample) in enumerate(samples.head(3).iterrows()):
            title = sample.get('title', '')[:50]
            true_return = sample.get('future_returns', 'N/A')
            print(f"     {i+1}. {title}... (收益率: {true_return})")
    
    def analyze_boundary_cases(self, analysis_df: pd.DataFrame):
        """分析边界案例"""
        
        print("\n🔍 分析边界案例...")
        
        # 找出接近分类边界的样本
        thresholds = [-0.05, -0.015, 0.015, 0.05]
        
        boundary_samples = []
        
        for i, threshold in enumerate(thresholds):
            # 找出收益率接近阈值的样本
            returns = pd.to_numeric(analysis_df['future_returns'], errors='coerce')
            
            # 在阈值附近±0.005的样本
            near_threshold = analysis_df[
                (returns >= threshold - 0.005) & 
                (returns <= threshold + 0.005)
            ].copy()
            
            if len(near_threshold) > 0:
                boundary_samples.append({
                    'threshold': threshold,
                    'samples': near_threshold,
                    'accuracy': np.mean(near_threshold['is_correct'])
                })
        
        print(f"边界案例分析:")
        for boundary in boundary_samples:
            threshold = boundary['threshold']
            accuracy = boundary['accuracy']
            count = len(boundary['samples'])
            
            print(f"  阈值 {threshold:+.3f} 附近: {count} 样本, 准确率 {accuracy:.3f}")
            
            if accuracy < 0.5:  # 准确率低于50%的边界
                print(f"    ⚠️ 该边界区域分类困难")
                
                # 分析这些困难样本的特征
                difficult_samples = boundary['samples'][~boundary['samples']['is_correct']]
                if len(difficult_samples) > 0:
                    print(f"    错误样本特征:")
                    print(f"      - 平均价值分数: {difficult_samples.get('value_score', pd.Series([0])).mean():.2f}")
                    
                    # 显示几个典型的困难样本
                    for j, (_, sample) in enumerate(difficult_samples.head(2).iterrows()):
                        title = sample.get('title', '')[:40]
                        true_return = sample.get('future_returns', 'N/A')
                        true_class = sample.get('true_class_name', 'N/A')
                        pred_class = sample.get('pred_class_name', 'N/A')
                        print(f"      {j+1}. {title}... (收益率: {true_return}, 真实: {true_class}, 预测: {pred_class})")
    
    def generate_improvement_suggestions(self, analysis_df: pd.DataFrame, error_analysis: Dict):
        """生成改进建议"""
        
        print("\n💡 改进建议:")
        
        # 1. 数据质量问题
        wrong_samples = analysis_df[~analysis_df['is_correct']]
        
        # 检查是否有明显的数据质量问题
        if 'value_score' in wrong_samples.columns:
            low_value_errors = wrong_samples[wrong_samples['value_score'] < 3.0]
            if len(low_value_errors) > len(wrong_samples) * 0.3:
                print(f"  1. 数据质量: {len(low_value_errors)} 个错误样本价值分数较低，建议提高筛选阈值")
        
        # 2. 类别不平衡问题
        class_counts = Counter(analysis_df['true_label'])
        min_count = min(class_counts.values())
        max_count = max(class_counts.values())
        imbalance_ratio = max_count / min_count
        
        if imbalance_ratio > 3:
            print(f"  2. 类别不平衡: 比例 {imbalance_ratio:.2f}, 建议使用SMOTE或调整class_weight")
        
        # 3. 特征工程建议
        print(f"  3. 特征工程建议:")
        print(f"     - 添加技术指标特征 (RSI, MACD等)")
        print(f"     - 添加情感分析特征")
        print(f"     - 添加时间特征 (交易时间、周末等)")
        print(f"     - 添加市场环境特征 (VIX, 大盘走势等)")
        
        # 4. 模型建议
        print(f"  4. 模型改进建议:")
        print(f"     - 尝试XGBoost或LightGBM")
        print(f"     - 使用集成学习 (Voting, Stacking)")
        print(f"     - 考虑深度学习模型 (LSTM, Transformer)")
        
        # 5. 阈值优化建议
        boundary_accuracy = []
        thresholds = [-0.05, -0.015, 0.015, 0.05]
        
        for threshold in thresholds:
            returns = pd.to_numeric(analysis_df['future_returns'], errors='coerce')
            near_threshold = analysis_df[
                (returns >= threshold - 0.01) & 
                (returns <= threshold + 0.01)
            ]
            if len(near_threshold) > 0:
                accuracy = np.mean(near_threshold['is_correct'])
                boundary_accuracy.append(accuracy)
        
        if boundary_accuracy and np.mean(boundary_accuracy) < 0.4:
            print(f"  5. 阈值优化: 当前阈值边界准确率较低，建议使用数据驱动的阈值优化")
    
    def run_complete_error_analysis(self):
        """运行完整的错误分析"""
        
        print("🚀 开始完整错误分析")
        print("=" * 80)
        
        # 1. 加载数据
        processed_df, X, y = self.load_and_prepare_data()
        
        # 2. 执行交叉验证错误分析
        analysis_df, y_pred = self.perform_cross_validation_error_analysis(processed_df, X, y)
        
        # 3. 分析混淆模式
        error_patterns = self.analyze_confusion_patterns(y, y_pred)
        
        # 4. 分析总是被分类错误的样本
        self.analyze_consistently_wrong_samples(analysis_df)
        
        # 5. 分析边界案例
        self.analyze_boundary_cases(analysis_df)
        
        # 6. 生成改进建议
        self.generate_improvement_suggestions(analysis_df, error_patterns)
        
        # 7. 保存详细分析结果
        wrong_samples = analysis_df[~analysis_df['is_correct']].copy()
        wrong_samples.to_csv('error_samples_analysis.csv', index=False, encoding='utf-8')
        
        print(f"\n✅ 错误分析完成!")
        print(f"详细错误样本已保存到: error_samples_analysis.csv")
        print(f"混淆矩阵图已保存到: confusion_matrix_analysis.png")
        
        return analysis_df, error_patterns

def main():
    """主函数"""
    
    analyzer = ErrorAnalysisSystem()
    analysis_df, error_patterns = analyzer.run_complete_error_analysis()
    
    print(f"\n🎯 关键发现总结:")
    print(f"- 总样本数: {len(analysis_df)}")
    print(f"- 错误样本数: {len(analysis_df[~analysis_df['is_correct']])}")
    print(f"- 总体准确率: {np.mean(analysis_df['is_correct']):.4f}")
    print(f"- 最常见错误: {error_patterns[0][0]} ({error_patterns[0][1]} 次)")

if __name__ == "__main__":
    main()
