#!/usr/bin/env python3
"""
综合新闻预测策略回测脚本

包含完整的数据加载、工具设置和回测执行
使用1000PEPEUSDT作为测试标的，集成真实的K线数据
"""

import asyncio
import logging
from pathlib import Path
from datetime import datetime, timezone
import pandas as pd
import numpy as np

# Nautilus Trader imports
from nautilus_trader.config import BacktestVenueConfig, BacktestDataConfig, BacktestEngineConfig, BacktestRunConfig
from nautilus_trader.config import ImportableStrategyConfig, LoggingConfig
from nautilus_trader.backtest.node import BacktestNode
from nautilus_trader.model.identifiers import InstrumentId, Venue
from nautilus_trader.model.currencies import USD, USDT
from nautilus_trader.model.enums import AccountType, OmsType, BarAggregation, PriceType
from nautilus_trader.model.objects import Money, Price, Quantity
from nautilus_trader.model.data import Bar, BarType
from nautilus_trader.model.instruments import CryptoPerpetual
from nautilus_trader.core.datetime import dt_to_unix_nanos
from nautilus_trader.persistence.catalog import ParquetDataCatalog

# 导入数据类型
from news_data_type import NewsData

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class ComprehensiveNewsBacktest:
    """综合新闻预测策略回测器"""
    
    def __init__(self, symbol: str = "1000PEPEUSDT"):
        self.symbol = symbol
        self.btc_symbol = "BTCUSDT"
        self.venue = Venue("BINANCE")
        
        # 路径配置
        self.base_path = Path("/root/news_train")
        self.catalog_path = self.base_path / "catalog"
        self.data_path = self.base_path / "data"
        self.models_path = self.base_path / "process" / "models"
        
        # 创建catalog目录
        self.catalog_path.mkdir(exist_ok=True)
        
        # 模型文件路径
        self.tokenizer_path = str(self.models_path / "crypto_tokenizer_small_with_symbols.model")
        self.tfidf_path = str(self.models_path / "real_tfidf.pkl")
        self.model_path = str(self.models_path / "real_rf_model.pkl")
        
    def create_instruments(self):
        """创建交易工具"""
        # PEPE工具
        pepe_instrument = CryptoPerpetual(
            instrument_id=InstrumentId.from_str(f"{self.symbol}.{self.venue}"),
            raw_symbol=self.symbol,
            base_currency=None,  # 对于永续合约可以为None
            quote_currency=USDT,
            settlement_currency=USDT,
            is_inverse=False,
            price_precision=8,
            size_precision=0,
            price_increment=Price.from_str("0.00000001"),
            size_increment=Quantity.from_str("1"),
            margin_init=0.1,
            margin_maint=0.05,
            maker_fee=0.0002,
            taker_fee=0.0004,
            ts_event=0,
            ts_init=0
        )
        
        # BTC工具
        btc_instrument = CryptoPerpetual(
            instrument_id=InstrumentId.from_str(f"{self.btc_symbol}.{self.venue}"),
            raw_symbol=self.btc_symbol,
            base_currency=None,
            quote_currency=USDT,
            settlement_currency=USDT,
            is_inverse=False,
            price_precision=2,
            size_precision=6,
            price_increment=Price.from_str("0.01"),
            size_increment=Quantity.from_str("0.000001"),
            margin_init=0.1,
            margin_maint=0.05,
            maker_fee=0.0002,
            taker_fee=0.0004,
            ts_event=0,
            ts_init=0
        )
        
        return pepe_instrument, btc_instrument
        
    def load_and_convert_bars(self, instrument, symbol: str):
        """加载并转换K线数据"""
        data_file = self.data_path / f"{symbol}_5m.parquet"
        
        if not data_file.exists():
            print(f"⚠️ 数据文件不存在: {data_file}")
            return []
            
        try:
            df = pd.read_parquet(data_file)
            print(f"📊 加载 {symbol} 数据: {len(df)} 条记录")
            
            # 检查数据格式
            required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                print(f"❌ 缺少必要列: {missing_columns}")
                print(f"📋 可用列: {list(df.columns)}")
                return []
                
            # 创建BarType
            bar_type = BarType(
                instrument_id=instrument.id,
                bar_spec=f"5-MINUTE-LAST-EXTERNAL"
            )
            
            bars = []
            
            # 限制数据量以避免内存问题
            df_sample = df.head(1000) if len(df) > 1000 else df
            
            for idx, row in df_sample.iterrows():
                try:
                    # 转换时间戳
                    if isinstance(row['timestamp'], str):
                        ts_event = dt_to_unix_nanos(pd.to_datetime(row['timestamp']))
                    else:
                        ts_event = dt_to_unix_nanos(row['timestamp'])
                    
                    ts_init = ts_event
                    
                    # 创建Bar对象
                    bar = Bar(
                        bar_type=bar_type,
                        open=instrument.make_price(float(row['open'])),
                        high=instrument.make_price(float(row['high'])),
                        low=instrument.make_price(float(row['low'])),
                        close=instrument.make_price(float(row['close'])),
                        volume=instrument.make_qty(float(row['volume'])),
                        ts_event=ts_event,
                        ts_init=ts_init
                    )
                    
                    bars.append(bar)
                    
                except Exception as e:
                    print(f"⚠️ 跳过无效数据行 {idx}: {e}")
                    continue
                    
            print(f"✅ 成功转换 {len(bars)} 个Bar对象")
            return bars
            
        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            return []
            
    def create_sample_news_data(self) -> NewsData:
        """创建示例新闻数据"""
        ts_event = dt_to_unix_nanos(datetime(2024, 1, 15, 10, 0, 0, tzinfo=timezone.utc))
        ts_init = ts_event
        
        news = NewsData(
            instrument_id=InstrumentId.from_str(f"{self.symbol}.{self.venue}"),
            title="PEPE价格突破关键阻力位，市场情绪乐观",
            content="PEPE代币今日突破重要技术阻力位，交易量大幅增加，市场分析师认为这可能是新一轮上涨的开始。技术指标显示强劲的买入信号。",
            time="10:00:00",
            date="2024-01-15",
            url="https://example.com/news/pepe-breakout",
            is_featured=True,
            scraped_at="2024-01-15T10:00:00Z",
            ts_event=ts_event,
            ts_init=ts_init
        )
        
        return news
        
    def setup_catalog_data(self):
        """设置catalog数据"""
        catalog = ParquetDataCatalog(str(self.catalog_path))
        
        # 创建工具
        pepe_instrument, btc_instrument = self.create_instruments()
        
        # 保存工具到catalog
        try:
            catalog.write_data([pepe_instrument, btc_instrument])
            print(f"✅ 保存工具到catalog: {pepe_instrument.id}, {btc_instrument.id}")
        except Exception as e:
            print(f"⚠️ 保存工具失败: {e}")
            
        # 加载并保存K线数据
        pepe_bars = self.load_and_convert_bars(pepe_instrument, self.symbol)
        btc_bars = self.load_and_convert_bars(btc_instrument, self.btc_symbol)
        
        if pepe_bars:
            try:
                catalog.write_data(pepe_bars)
                print(f"✅ 保存PEPE K线数据: {len(pepe_bars)} 条")
            except Exception as e:
                print(f"⚠️ 保存PEPE K线数据失败: {e}")
                
        if btc_bars:
            try:
                catalog.write_data(btc_bars)
                print(f"✅ 保存BTC K线数据: {len(btc_bars)} 条")
            except Exception as e:
                print(f"⚠️ 保存BTC K线数据失败: {e}")
                
        return catalog
        
    def create_strategy_config(self) -> ImportableStrategyConfig:
        """创建策略配置"""
        strategy_config = {
            # 模型路径
            "tokenizer_path": self.tokenizer_path,
            "tfidf_path": self.tfidf_path,
            "model_path": self.model_path,
            
            # 交易工具
            "instrument_str": f"{self.symbol}.{self.venue}",
            "btc_instrument_str": f"{self.btc_symbol}.{self.venue}",
            "bar_str": f"{self.symbol}.{self.venue}-5-MINUTE-LAST-EXTERNAL",
            "btc_bar_str": f"{self.btc_symbol}.{self.venue}-5-MINUTE-LAST-EXTERNAL",
            
            # 风险管理参数
            "stop_loss_pct": 0.02,
            "low_trail_stop_loss_pct": 0.5,
            "trail_stop_loss_pct": 0.3,
            "higher_trail_stop_loss_pct": 0.2,
            "low_trail_profit_threshold": 0.01,
            "first_trail_profit_threshold": 0.02,
            "second_trail_profit_threshold": 0.03,
            
            # 交易金额
            "max_trade_usd": 1000.0,
            "min_trade_usd": 100.0,
            
            # 策略ID
            "strategy_id": "NewsPnlStrategy-001"
        }
        
        return ImportableStrategyConfig(
            strategy_path="process.strategy.news_prediction_strategy:NewsPnlTradingStrategy",
            config_path="process.strategy.news_prediction_strategy:NewsPnlTradingStrategyConfig",
            config=strategy_config
        )
        
    def create_backtest_config(self) -> BacktestRunConfig:
        """创建回测配置"""
        # 场所配置
        venue_config = BacktestVenueConfig(
            name=str(self.venue),
            oms_type=OmsType.HEDGING,
            account_type=AccountType.MARGIN,
            base_currency=USDT,
            starting_balances=[Money(100000, USDT)],
        )
        
        # 数据配置
        data_config = BacktestDataConfig(
            catalog_path=str(self.catalog_path),
        )
        
        # 策略配置
        strategy_config = self.create_strategy_config()
        
        # 引擎配置
        engine_config = BacktestEngineConfig(
            strategies=[strategy_config],
            logging=LoggingConfig(
                log_level="INFO",
                bypass_logging=False,
            ),
        )
        
        # 回测运行配置
        run_config = BacktestRunConfig(
            engine=engine_config,
            venues=[venue_config],
            data=[data_config],
            start="2024-01-01T00:00:00Z",
            end="2024-01-31T23:59:59Z",
        )
        
        return run_config

    async def run_backtest(self):
        """运行综合回测"""
        print("🚀 开始综合新闻预测策略回测...")
        print(f"📈 测试标的: {self.symbol}")
        print(f"🏦 交易所: {self.venue}")

        # 检查必要文件
        required_files = [
            self.tokenizer_path,
            self.tfidf_path,
            self.model_path
        ]

        missing_files = []
        for file_path in required_files:
            if not Path(file_path).exists():
                missing_files.append(file_path)

        if missing_files:
            print("❌ 缺少必要文件:")
            for file in missing_files:
                print(f"   - {file}")
            return False

        print("✅ 所有必要文件检查通过")

        # 设置catalog数据
        print("📁 设置catalog数据...")
        catalog = self.setup_catalog_data()

        # 创建示例新闻数据
        start_news = self.create_sample_news_data()
        print(f"📰 创建示例新闻: {start_news.title[:50]}...")

        # 创建回测配置
        config = self.create_backtest_config()

        # 创建回测节点
        node = BacktestNode(configs=[config])

        try:
            print("⏳ 正在运行回测...")
            await node.run_async()

            print("✅ 回测完成!")

            # 获取回测结果
            engine = node.get_engine()
            if engine:
                print("\n📊 回测结果:")
                account = engine.portfolio.account()
                print(f"账户余额: {account.balance_total()}")
                print(f"总PnL: {account.unrealized_pnl()}")

                # 显示订单统计
                orders = engine.cache.orders()
                print(f"总订单数: {len(orders)}")

                # 显示仓位统计
                positions = engine.cache.positions()
                print(f"总仓位数: {len(positions)}")

                # 显示策略统计
                strategies = engine.cache.strategies()
                print(f"策略数: {len(strategies)}")

            return True

        except Exception as e:
            print(f"❌ 回测失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            await node.dispose()


async def main():
    """主函数"""
    print("=" * 70)
    print("🎯 新闻预测策略综合回测系统")
    print("=" * 70)

    # 创建回测器
    backtest = ComprehensiveNewsBacktest(symbol="1000PEPEUSDT")

    # 运行回测
    success = await backtest.run_backtest()

    if success:
        print("\n🎉 综合回测成功完成!")
        print("💡 回测包含了真实的K线数据和完整的策略逻辑")
    else:
        print("\n💥 回测失败!")
        print("💡 请检查数据文件和模型文件是否存在")

    print("=" * 70)


if __name__ == "__main__":
    asyncio.run(main())
