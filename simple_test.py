import polars as pl
import pandas as pd
from datetime import datetime, timedelta, timezone

# Test loading a labeled file
print("Testing labeled data loading...")
try:
    df = pl.read_parquet("../data/ETHUSDT_5m_relative_labeled.parquet")
    print(f"✅ Loaded {df.height} records")
    print(f"📊 Columns: {df.columns}")
    print(f"📊 Timestamp dtype: {df['timestamp'].dtype}")
    
    # Show first few timestamps
    print("First 5 timestamps:")
    for i, ts in enumerate(df['timestamp'].head(5)):
        print(f"  {i+1}: {ts}")
    
    # Test time filtering
    test_time = datetime(2025, 1, 1, 15, 0, 0, tzinfo=timezone.utc)
    print(f"\n🔍 Testing filter with: {test_time}")
    
    # Try different approaches
    try:
        filtered = df.filter(pl.col("timestamp") >= test_time)
        print(f"✅ Filter worked! Found {filtered.height} records")
        if filtered.height > 0:
            first_match = filtered.sort("timestamp").head(1)
            match_time = first_match['timestamp'][0]
            print(f"📅 First match: {match_time}")
    except Exception as e:
        print(f"❌ Filter failed: {e}")
        
        # Try converting to string first
        try:
            test_time_str = test_time.isoformat()
            filtered = df.filter(pl.col("timestamp") >= pl.lit(test_time_str).str.to_datetime())
            print(f"✅ String conversion worked! Found {filtered.height} records")
        except Exception as e2:
            print(f"❌ String conversion also failed: {e2}")
    
except Exception as e:
    print(f"❌ Failed to load data: {e}")

# Test news data
print("\n" + "="*50)
print("Testing news data loading...")
try:
    news_df = pd.read_csv("../filter_news/panews_flash_20250101.csv")
    print(f"✅ Loaded {len(news_df)} news items")
    
    # Show first non-BTC news
    for idx, row in news_df.iterrows():
        symbols = str(row['relative_symbols']).strip()
        if symbols != 'BTCUSDT' and not pd.isna(symbols):
            print(f"📰 Sample news:")
            print(f"   Title: {row['title'][:60]}...")
            print(f"   Time: {row['date']} {row['time']}")
            print(f"   Symbols: {symbols}")
            break
            
except Exception as e:
    print(f"❌ Failed to load news: {e}")