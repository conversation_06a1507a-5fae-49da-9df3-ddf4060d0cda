#!/usr/bin/env python3
"""
Convert Binance Data to Nautilus Trader Catalog

Converts the downloaded Binance data to Nautilus Trader format and saves to catalog.
"""

import pandas as pd
from pathlib import Path
from typing import List
import sys
import os

# Add the nautilus_trader path
sys.path.append(str(Path(__file__).parent / "nautilus_trader-develop"))

try:
    from nautilus_trader.core.datetime import dt_to_unix_nanos
    from nautilus_trader.model.data import Bar, BarType, BarSpecification
    from nautilus_trader.model.enums import BarAggregation, PriceType
    from nautilus_trader.model.identifiers import InstrumentId
    from nautilus_trader.model.objects import Price, Quantity
    from nautilus_trader.persistence.catalog import ParquetDataCatalog
    from nautilus_trader.test_kit.providers import TestInstrumentProvider
    NAUTILUS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️  Nautilus Trader not available: {e}")
    print("📦 Please install nautilus_trader or use the simple downloader output")
    NAUTILUS_AVAILABLE = False


class BinanceToNautilusConverter:
    """Converts Binance data to Nautilus Trader format"""
    
    def __init__(self, catalog_path: str = "./catalog"):
        self.catalog_path = Path(catalog_path)
        self.catalog_path.mkdir(exist_ok=True)
        if NAUTILUS_AVAILABLE:
            self.catalog = ParquetDataCatalog(str(self.catalog_path))
        
    def load_binance_data(self, parquet_path: str) -> pd.DataFrame:
        """Load Binance data from parquet file"""
        df = pd.read_parquet(parquet_path)
        print(f"📊 Loaded {len(df)} records from {parquet_path}")
        print(f"📅 Date range: {df['timestamp'].min()} to {df['timestamp'].max()}")
        return df
    
    def create_nautilus_bars(self, df: pd.DataFrame, instrument_id: str = "ETHUSDT-PERP.BINANCE") -> List[Bar]:
        """Convert DataFrame to Nautilus Bar objects"""
        if not NAUTILUS_AVAILABLE:
            raise ImportError("Nautilus Trader not available")
            
        bars = []
        
        # 首先尝试从catalog获取instrument定义
        instrument = None
        price_precision = 2  # 默认precision
        size_precision = 3   # 默认precision

        try:
            instruments = self.catalog.instruments()
            for inst in instruments:
                if str(inst.id) == instrument_id:
                    instrument = inst
                    price_precision = inst.price_precision
                    size_precision = inst.size_precision
                    print(f"✅ 从catalog获取instrument定义: price_precision={price_precision}, size_precision={size_precision}")
                    break
        except Exception as e:
            print(f"⚠️ 无法从catalog获取instrument定义: {e}")

        # 如果catalog中没有，使用test provider创建
        if instrument is None:
            print(f"🔧 使用TestInstrumentProvider创建instrument")
            if "ETHUSDT" in instrument_id:
                instrument = TestInstrumentProvider.ethusdt_perp_binance()
            else:
                instrument = TestInstrumentProvider.btcusdt_perp_binance()
            price_precision = instrument.price_precision
            size_precision = instrument.size_precision
        
        # Create bar specification for 5-minute bars
        bar_spec = BarSpecification(
            step=5,
            aggregation=BarAggregation.MINUTE,
            price_type=PriceType.LAST
        )

        # Create bar type
        bar_type = BarType(
            instrument_id=InstrumentId.from_str(instrument_id),
            bar_spec=bar_spec
        )
        
        print(f"🔄 Converting {len(df)} records to Nautilus bars...")
        
        for i, row in df.iterrows():
            # Convert timestamp to nanoseconds
            ts_event = dt_to_unix_nanos(row['timestamp'])
            ts_init = ts_event
            
            try:
                # 使用instrument.make_price()和make_qty()方法创建对象（最佳实践）
                bar = Bar(
                    bar_type=bar_type,
                    open=instrument.make_price(float(row['open'])),
                    high=instrument.make_price(float(row['high'])),
                    low=instrument.make_price(float(row['low'])),
                    close=instrument.make_price(float(row['close'])),
                    volume=instrument.make_qty(float(row['volume'])),
                    ts_event=ts_event,
                    ts_init=ts_init
                )
                bars.append(bar)
                
                if (i + 1) % 10000 == 0:
                    print(f"  ✅ Converted {i + 1} bars...")
                    
            except Exception as e:
                print(f"❌ Error converting row {i}: {e}")
                continue
                
        print(f"✅ Successfully converted {len(bars)} bars")
        return bars, instrument
    
    def save_to_catalog(self, bars: List[Bar], instrument_id: str, instrument=None):
        """Save bars to Nautilus catalog"""
        if not NAUTILUS_AVAILABLE:
            raise ImportError("Nautilus Trader not available")

        print(f"💾 Saving {len(bars)} bars to catalog...")

        try:
            # 如果提供了instrument，先保存instrument定义
            if instrument is not None:
                print(f"💾 Saving instrument definition: {instrument.id}")
                self.catalog.write_data([instrument])

            # Write bars to catalog
            self.catalog.write_data(bars)
            print(f"✅ Successfully saved {len(bars)} bars for {instrument_id} to catalog")
            
            # List what's in the catalog
            print(f"📁 Catalog contents:")
            try:
                instruments = self.catalog.instruments()
                print(f"  📈 Instruments: {len(instruments)}")
                for instrument in instruments:
                    print(f"    - {instrument.id}")
            except Exception as e:
                print(f"⚠️  Could not list catalog contents: {e}")
                
        except Exception as e:
            print(f"❌ Error saving to catalog: {e}")
            raise
    
    def convert_and_save(self, parquet_path: str, instrument_id: str = "ETHUSDT-PERP.BINANCE"):
        """Main method to convert and save data"""
        print(f"🚀 Converting Binance data to Nautilus format")
        print(f"📁 Input: {parquet_path}")
        print(f"🎯 Instrument: {instrument_id}")
        print(f"📂 Catalog: {self.catalog_path}")
        
        # Load data
        df = self.load_binance_data(parquet_path)
        
        if not NAUTILUS_AVAILABLE:
            print("❌ Cannot convert without Nautilus Trader")
            return False
        
        # Convert to Nautilus bars (这个方法会返回bars和instrument)
        result = self.create_nautilus_bars(df, instrument_id)
        if isinstance(result, tuple):
            bars, instrument = result
        else:
            bars = result
            instrument = None

        if not bars:
            print("❌ No bars created")
            return False

        # Save to catalog
        self.save_to_catalog(bars, instrument_id, instrument)
        
        return True


def find_latest_parquet_file(symbol: str = "ETHUSDT", data_dir: str = "./data") -> str:
    """Find the latest parquet file for the specified symbol"""
    data_path = Path(data_dir)

    if not data_path.exists():
        raise FileNotFoundError(f"Data directory not found: {data_dir}")

    # Find all parquet files for the symbol
    parquet_files = list(data_path.glob(f"{symbol}_5m_*.parquet"))

    if not parquet_files:
        raise FileNotFoundError(f"No {symbol} parquet files found in {data_dir}")

    # Sort by modification time and get the latest
    latest_file = max(parquet_files, key=lambda f: f.stat().st_mtime)

    print(f"📁 Found latest file: {latest_file}")
    return str(latest_file)


def main():
    """Main function"""
    print("Binance to Nautilus Trader Converter")
    print("=" * 50)
    
    if not NAUTILUS_AVAILABLE:
        print("❌ Nautilus Trader not available")
        print("💡 To install, run: pip install nautilus_trader")
        return
    
    try:
        # Find the latest ETHUSDT parquet file
        parquet_path = find_latest_parquet_file("ETHUSDT")

        # Create converter
        converter = BinanceToNautilusConverter("/root/nautilus_trader-develop/examples")

        # Convert and save
        success = converter.convert_and_save(parquet_path, "ETHUSDT-PERP.BINANCE")

        if success:
            print("\n✅ Conversion completed successfully!")
            print(f"📁 Data saved to catalog: {converter.catalog_path}")
            print(f"🎯 Instrument: ETHUSDT-PERP.BINANCE")
            print(f"📊 Bar type: 5-minute bars")
        else:
            print("\n❌ Conversion failed")
            
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
