"""
加密货币新闻PnL预测模型训练 - Kaggle单文件版本
使用自定义SentencePiece分词器和改进的BERT模型

运行方式: python pnl_training_kaggle.py
"""

import os
import sys
import json
import re
import time
import logging
import warnings
import argparse
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any

import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, f1_score, roc_auc_score, classification_report
from sklearn.preprocessing import label_binarize

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
from torch.optim import AdamW
from transformers import BertModel, BertConfig, get_cosine_schedule_with_warmup

import sentencepiece as spm
from tqdm import tqdm

# 忽略警告
warnings.filterwarnings('ignore')

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 全局配置
class Config:
    """训练配置"""
    def __init__(self):
        # Kaggle路径
        self.TOKENIZER_DIR = 'models'
        self.DATA_DIR = "news_data_with_pnl_new"
        self.OUTPUT_DIR = '.'
        
        # 分词器配置
        self.TOKENIZER_MODEL = os.path.join(self.TOKENIZER_DIR, 'crypto_tokenizer_large_with_symbols.model')
        self.MAX_LENGTH = 256
        
        # Symbol处理配置
        self.SPLIT_MULTI_SYMBOL_NEWS = True  # 是否拆分多symbol新闻
        self.MAX_SYMBOLS_PER_NEWS = 5  # 每条新闻最多处理的symbol数量
        self.MIN_TEXT_LENGTH = 20  # 最小文本长度
        self.MAX_TEXT_LENGTH = 2000  # 最大文本长度
        self.ENABLE_SYMBOL_PROCESSING = False  # 是否启用symbol处理（调试用）
        self.SYMBOL_PROCESSING_MODE = 'flexible'  # 'strict', 'flexible', 'disabled'
        
        # 排除的symbols（主要是稳定币和低波动性交易对）
        self.EXCLUDED_SYMBOLS = {
            # 稳定币对 - 波动太小
            "USDCUSDT", "BUSDUSDT", "TUSDUSDT", "PAXUSDT", "USDPUSDT",
            "DAIUSDT", "FRAXUSDT", "LUSDUSDT", "USDDUSDT", "FDUSDUSDT",
            # 法币稳定币对
            "USDCUSD", "BUSDUSD", "TUSDUSD", "PAXUSD", "USDPUSD",
            # 小众稳定币
            "USTCUSDT", "USTUSDT",
            # 排除一些指数和特殊交易对
            "AIUSDT", "DEFIUSDT"
        }
        
        # Symbol权重配置 - 主流币给更高权重
        self.SYMBOL_WEIGHTS = {
            "BTCUSDT": 1.5,
            "ETHUSDT": 1.3,
            "BNBUSDT": 1.2,
            "ADAUSDT": 1.1,
            "SOLUSDT": 1.2,
            "XRPUSDT": 1.1,
            "DOTUSDT": 1.1,
            "LINKUSDT": 1.1,
            "LTCUSDT": 1.1,
            "AVAXUSDT": 1.1,
            "MATICUSDT": 1.1,
            "DOGEUSDT": 1.1,
            # 默认权重为1.0
        }
        
        # 最小波动阈值 - 过滤掉变化太小的数据
        self.MIN_VOLATILITY_THRESHOLDS = {
            "1h": 0.2,   # 1小时最小0.2%波动
            "4h": 0.3,   # 4小时最小0.3%波动
            "6h": 0.4,   # 6小时最小0.4%波动
            "12h": 0.5,  # 12小时最小0.5%波动
            "24h": 0.8   # 24小时最小0.8%波动
        }
        
        # 模型配置 - 根据GPU内存调整
        self.HIDDEN_SIZE = 512
        self.NUM_LAYERS = 6
        self.NUM_HEADS = 8
        self.INTERMEDIATE_SIZE = 2048
        
        # 训练配置 - 可通过参数修改
        self.BATCH_SIZE = 16
        self.ACCUMULATION_STEPS = 2
        self.LEARNING_RATE = 4e-5
        self.EMBEDDING_LR_MULTIPLIER = 2.0
        self.NUM_EPOCHS = 8
        self.WARMUP_RATIO = 0.1
        
        # PnL标签配置 - 5分类，支持不同时间框架
        self.NUM_LABELS = 5
        self.TIMEFRAME = '4h'  # 默认时间框架
        
        # 不同时间框架的PnL阈值
        self.PNL_THRESHOLDS_BY_TIMEFRAME = {
            '1h': {'strong_down': -1.5, 'down': -0.5, 'up': 0.5, 'strong_up': 1.5},
            '4h': {'strong_down': -2.0, 'down': -0.5, 'up': 0.5, 'strong_up': 2.0},
            '6h': {'strong_down': -2.5, 'down': -0.8, 'up': 0.8, 'strong_up': 2.5},
            '12h': {'strong_down': -3.0, 'down': -1.0, 'up': 1.0, 'strong_up': 3.0},
            '24h': {'strong_down': -5.0, 'down': -1.5, 'up': 1.5, 'strong_up': 5.0}
        }
        
        # 损失函数配置
        self.USE_FOCAL_LOSS = True
        self.FOCAL_ALPHA = 1.0
        self.FOCAL_GAMMA = 2.0
        self.LABEL_SMOOTHING = 0.1
        
        # 设备配置
        self.DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    @property
    def PNL_THRESHOLDS(self):
        """获取当前时间框架的PnL阈值"""
        return self.PNL_THRESHOLDS_BY_TIMEFRAME.get(self.TIMEFRAME, self.PNL_THRESHOLDS_BY_TIMEFRAME['4h'])
    
    def adjust_pnl_thresholds(self, new_thresholds: dict):
        """调整当前时间框架的PnL阈值"""
        self.PNL_THRESHOLDS_BY_TIMEFRAME[self.TIMEFRAME] = new_thresholds
    
    def update_from_args(self, args):
        """从命令行参数更新配置"""
        if hasattr(args, 'epochs') and args.epochs:
            self.NUM_EPOCHS = args.epochs
        if hasattr(args, 'batch_size') and args.batch_size:
            self.BATCH_SIZE = args.batch_size
        if hasattr(args, 'timeframe') and args.timeframe:
            if args.timeframe in self.PNL_THRESHOLDS_BY_TIMEFRAME:
                self.TIMEFRAME = args.timeframe
            else:
                logger.warning(f"不支持的时间框架: {args.timeframe}, 使用默认值: {self.TIMEFRAME}")
        if hasattr(args, 'learning_rate') and args.learning_rate:
            self.LEARNING_RATE = args.learning_rate
        if hasattr(args, 'hidden_size') and args.hidden_size:
            self.HIDDEN_SIZE = args.hidden_size
        if hasattr(args, 'num_layers') and args.num_layers:
            self.NUM_LAYERS = args.num_layers
    
    def print_config(self):
        """打印当前配置"""
        logger.info("=== 训练配置 ===")
        logger.info(f"时间框架: {self.TIMEFRAME}")
        logger.info(f"PnL阈值: {self.PNL_THRESHOLDS}")
        logger.info(f"训练轮数: {self.NUM_EPOCHS}")
        logger.info(f"批处理大小: {self.BATCH_SIZE}")
        logger.info(f"学习率: {self.LEARNING_RATE}")
        logger.info(f"模型配置: {self.HIDDEN_SIZE}d, {self.NUM_LAYERS}层")
        logger.info("---")
        logger.info("Symbol处理配置:")
        logger.info(f"  处理模式: {self.SYMBOL_PROCESSING_MODE}")
        logger.info(f"  启用Symbol处理: {self.ENABLE_SYMBOL_PROCESSING}")
        logger.info(f"  拆分多symbol新闻: {self.SPLIT_MULTI_SYMBOL_NEWS}")
        logger.info(f"  每条新闻最大symbols: {self.MAX_SYMBOLS_PER_NEWS}")
        logger.info(f"  排除symbols数量: {len(self.EXCLUDED_SYMBOLS)}")
        logger.info(f"  主流币权重数量: {len([w for w in self.SYMBOL_WEIGHTS.values() if w > 1.0])}")
        logger.info(f"  最小波动阈值: {self.MIN_VOLATILITY_THRESHOLDS.get(self.TIMEFRAME, 'N/A')}%")
        logger.info(f"  文本长度范围: {self.MIN_TEXT_LENGTH}-{self.MAX_TEXT_LENGTH}")
        logger.info("---")
        logger.info(f"损失函数: {'Focal Loss' if self.USE_FOCAL_LOSS else 'CrossEntropy'}")
        if self.USE_FOCAL_LOSS:
            logger.info(f"  Alpha: {self.FOCAL_ALPHA}, Gamma: {self.FOCAL_GAMMA}")
        logger.info("---")
        logger.info("评估指标: 使用AUC作为主要评估指标")
        logger.info("  原因: AUC对类别不平衡更鲁棒，在金融预测中更可靠")
        logger.info("  AUC范围: 0.5-1.0，>0.7为良好，>0.8为优秀")
        logger.info("================")

config = Config()

def seed_everything(seed: int = 42):
    """设置随机种子"""
    import random
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False

seed_everything()

# Symbol处理相关函数
def load_binance_symbols():
    """加载Binance交易对列表"""
    try:
        # 尝试从多个可能的路径加载
        symbol_paths = [
            '/kaggle/input/crypto-news/binance_symbols.json',
            '/kaggle/input/tokenizer/binance_symbols.json', 
            'binance_symbols.json'
        ]
        
        for path in symbol_paths:
            if os.path.exists(path):
                with open(path, 'r', encoding='utf-8') as f:
                    symbols_data = json.load(f)
                logger.info(f"从 {path} 加载了 {len(symbols_data)} 个交易对")
                return set(symbols_data.keys())
        
        # 如果没有找到文件，使用预定义的主要交易对
        logger.warning("未找到binance_symbols.json，使用预定义的主要交易对")
        return get_default_symbols()
        
    except Exception as e:
        logger.warning(f"加载Binance交易对失败: {e}，使用默认交易对")
        return get_default_symbols()

def get_default_symbols():
    """获取默认的主要交易对列表"""
    return {
        "BTCUSDT", "ETHUSDT", "BNBUSDT", "ADAUSDT", "XRPUSDT", "SOLUSDT", 
        "DOGEUSDT", "MATICUSDT", "DOTUSDT", "AVAXUSDT", "LTCUSDT", "LINKUSDT",
        "ATOMUSDT", "UNIUSDT", "FILUSDT", "TRXUSDT", "ETCUSDT", "XLMUSDT",
        "VETUSDT", "ICPUSDT", "FTMUSDT", "THETAUSDT", "ALGOUSDT", "AXSUSDT",
        "SANDUSDT", "MANAUSDT", "KSMUSDT", "WAVESUSDT", "CHZUSDT", "FLOWUSDT"
    }

def extract_symbols_from_text(text: str, valid_symbols: set) -> List[str]:
    """从文本中提取有效的交易对symbols"""
    if not text:
        return []
    
    text = text.upper()
    found_symbols = []
    
    # 按长度排序，优先匹配较长的symbol（避免BTC匹配到BTCUSDT）
    sorted_symbols = sorted(valid_symbols, key=len, reverse=True)
    
    for symbol in sorted_symbols:
        if symbol in text and symbol not in found_symbols:
            # 确保这是一个完整的词，而不是其他词的一部分
            import re
            pattern = r'\b' + re.escape(symbol) + r'\b'
            if re.search(pattern, text):
                found_symbols.append(symbol)
    
    return found_symbols

def should_filter_symbol(symbol: str, pnl_change: float, timeframe: str) -> bool:
    """判断是否应该过滤掉这个symbol的数据"""
    # 检查是否在排除列表中
    if symbol.upper() in config.EXCLUDED_SYMBOLS:
        return True
    
    # 检查波动性是否足够
    min_volatility = config.MIN_VOLATILITY_THRESHOLDS.get(timeframe, 0.3)
    if abs(pnl_change) < min_volatility:
        return True
    
    return False

def get_symbol_weight(symbol: str) -> float:
    """获取symbol的权重"""
    return config.SYMBOL_WEIGHTS.get(symbol.upper(), 1.0)

def split_multi_symbol_news(row: pd.Series, valid_symbols: set) -> List[Dict]:
    """将包含多个symbol的新闻拆分为多条记录"""
    title = str(row.get('title', ''))
    content = str(row.get('content', ''))
    combined_text = f"{title} {content}"
    
    # 提取所有相关的symbols
    symbols = extract_symbols_from_text(combined_text, valid_symbols)
    
    # 限制每条新闻的symbol数量
    if len(symbols) > config.MAX_SYMBOLS_PER_NEWS:
        symbols = symbols[:config.MAX_SYMBOLS_PER_NEWS]
    
    # 如果没有找到任何symbol，尝试从原始数据中获取
    if not symbols and 'symbol' in row and pd.notna(row['symbol']):
        original_symbol = str(row['symbol']).upper()
        if original_symbol in valid_symbols:
            symbols = [original_symbol]
    
    results = []
    for symbol in symbols:
        # 检查是否应该过滤这个symbol
        pnl_change = row.get('pnl_change', 0)
        if should_filter_symbol(symbol, pnl_change, config.TIMEFRAME):
            continue
        
        # 创建新的记录
        new_row = row.copy()
        new_row['symbol'] = symbol
        new_row['symbol_weight'] = get_symbol_weight(symbol)
        results.append(new_row.to_dict())
    
    return results

def process_news_symbols(data: pd.DataFrame, valid_symbols: set) -> pd.DataFrame:
    """处理新闻中的symbols：提取、拆分、过滤"""
    logger.info("开始处理新闻symbols...")
    
    processed_rows = []
    
    for idx, row in data.iterrows():
        if config.SPLIT_MULTI_SYMBOL_NEWS:
            # 拆分多symbol新闻
            split_rows = split_multi_symbol_news(row, valid_symbols)
            processed_rows.extend(split_rows)
        else:
            # 不拆分，只提取第一个symbol
            title = str(row.get('title', ''))
            content = str(row.get('content', ''))
            combined_text = f"{title} {content}"
            symbols = extract_symbols_from_text(combined_text, valid_symbols)
            
            if symbols:
                symbol = symbols[0]  # 只取第一个
                pnl_change = row.get('pnl_change', 0)
                
                if not should_filter_symbol(symbol, pnl_change, config.TIMEFRAME):
                    new_row = row.copy()
                    new_row['symbol'] = symbol
                    new_row['symbol_weight'] = get_symbol_weight(symbol)
                    processed_rows.append(new_row.to_dict())
    
    if not processed_rows:
        logger.warning("没有找到任何有效的symbol数据!")
        return data  # 返回原始数据
    
    processed_df = pd.DataFrame(processed_rows)
    
    logger.info(f"Symbol处理完成: {len(data)} -> {len(processed_df)} 条记录")
    
    # 统计symbol分布
    if 'symbol' in processed_df.columns:
        symbol_counts = processed_df['symbol'].value_counts()
        logger.info(f"Symbol分布 (前10): ")
        for symbol, count in symbol_counts.head(10).items():
            weight = get_symbol_weight(symbol)
            logger.info(f"  {symbol}: {count} 条 (权重: {weight})")
    
    return processed_df

class CustomTokenizer:
    """自定义SentencePiece分词器"""
    
    def __init__(self, model_path: str):
        """初始化分词器"""
        self.sp = spm.SentencePieceProcessor()
        self.sp.load(model_path)
        self.vocab_size = self.sp.vocab_size()
        
        # 特殊token ID
        self.pad_id = 0
        self.unk_id = 1
        self.cls_id = 2
        self.sep_id = 3
        
        logger.info(f"自定义分词器加载成功，词汇量: {self.vocab_size}")
    
    def encode(self, text: str, max_length: int = 256) -> Dict[str, torch.Tensor]:
        """编码文本"""
        text = str(text).strip()
        if not text:
            text = "[UNK]"
        
        # 编码为ID
        token_ids = self.sp.encode_as_ids(text)
        
        # 添加特殊token [CLS] text [SEP]
        token_ids = [self.cls_id] + token_ids + [self.sep_id]
        
        # 截断
        if len(token_ids) > max_length:
            token_ids = token_ids[:max_length-1] + [self.sep_id]
        
        # 创建attention mask
        attention_mask = [1] * len(token_ids)
        
        # 填充到max_length
        while len(token_ids) < max_length:
            token_ids.append(self.pad_id)
            attention_mask.append(0)
        
        return {
            'input_ids': torch.tensor(token_ids, dtype=torch.long),
            'attention_mask': torch.tensor(attention_mask, dtype=torch.long)
        }

class FocalLoss(nn.Module):
    """Focal Loss实现"""
    
    def __init__(self, alpha=1.0, gamma=2.0, reduction='mean'):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
        
    def forward(self, inputs, targets):
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss

class NewsDataset(Dataset):
    """新闻数据集"""
    
    def __init__(self, dataframe: pd.DataFrame, tokenizer: CustomTokenizer, max_length: int = 256):
        self.df = dataframe.reset_index(drop=True)
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def __len__(self):
        return len(self.df)
    
    def __getitem__(self, idx):
        row = self.df.iloc[idx]
        
        # 组合文本 - 如果有symbol信息，也包含进去
        text_parts = [str(row['title']), str(row['content'])]
        
        # 如果有symbol信息，添加到文本中增强上下文
        if 'symbol' in row and pd.notna(row['symbol']):
            symbol = str(row['symbol'])
            # 将symbol添加到文本开头，帮助模型理解上下文
            text_parts.insert(0, f"[{symbol}]")
        
        text = " ".join(text_parts)
        
        # 编码
        encoding = self.tokenizer.encode(text, self.max_length)
        
        # 获取symbol权重（如果有的话）
        sample_weight = 1.0
        if 'symbol_weight' in row and pd.notna(row['symbol_weight']):
            sample_weight = float(row['symbol_weight'])
        
        return {
            'input_ids': encoding['input_ids'],
            'attention_mask': encoding['attention_mask'],
            'labels': torch.tensor(row['label'], dtype=torch.long),
            'sample_weight': torch.tensor(sample_weight, dtype=torch.float)
        }

class CustomBertClassifier(nn.Module):
    """自定义BERT分类器"""
    
    def __init__(self, custom_vocab_size: int, num_labels: int = 5):
        super().__init__()
        
        self.num_labels = num_labels
        self.custom_vocab_size = custom_vocab_size
        
        # BERT配置
        bert_config = BertConfig(
            vocab_size=custom_vocab_size,
            hidden_size=config.HIDDEN_SIZE,
            num_hidden_layers=config.NUM_LAYERS,
            num_attention_heads=config.NUM_HEADS,
            intermediate_size=config.INTERMEDIATE_SIZE,
            hidden_dropout_prob=0.1,
            attention_probs_dropout_prob=0.1,
            max_position_embeddings=512,
            type_vocab_size=2,
            pad_token_id=0
        )
        
        # 创建BERT模型
        self.bert = BertModel(bert_config)
        
        # 分类头
        self.dropout = nn.Dropout(0.1)
        self.classifier = nn.Linear(config.HIDDEN_SIZE, num_labels)
        
        # 初始化权重
        self._init_weights()
        
        logger.info(f"自定义BERT模型初始化完成")
        logger.info(f"词汇量: {custom_vocab_size}, 隐藏层大小: {config.HIDDEN_SIZE}")
    
    def _init_weights(self):
        """初始化权重"""
        # 初始化分类头
        nn.init.xavier_uniform_(self.classifier.weight)
        nn.init.constant_(self.classifier.bias, 0)
    
    def forward(self, input_ids: torch.Tensor, attention_mask: torch.Tensor) -> torch.Tensor:
        """前向传播"""
        outputs = self.bert(
            input_ids=input_ids,
            attention_mask=attention_mask,
            return_dict=True
        )
        
        pooled_output = outputs.pooler_output
        pooled_output = self.dropout(pooled_output)
        logits = self.classifier(pooled_output)
        
        return logits
    
    def get_trainable_parameters(self) -> Dict[str, Any]:
        """获取可训练参数统计"""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        return {
            'total_params': total_params,
            'trainable_params': trainable_params,
            'trainable_ratio': trainable_params / total_params
        }

def parse_pnl_rate(pnl_string: str, timeframe: str = None) -> float:
    """解析PnL百分比字符串，支持多时间框架格式"""
    if pd.isna(pnl_string) or not pnl_string.strip():
        return 0.0
    
    try:
        pnl_str = str(pnl_string).strip()
        
        # 如果包含逗号，说明是多时间框架格式: "1h:+0.04%,4h:+0.01%,6h:-0.13%..."
        if ',' in pnl_str and ':' in pnl_str:
            # 解析多时间框架格式
            timeframe_to_use = timeframe or config.TIMEFRAME
            parts = pnl_str.split(',')
            
            for part in parts:
                if ':' in part:
                    tf, value = part.split(':', 1)
                    tf = tf.strip()
                    value = value.strip()
                    
                    if tf == timeframe_to_use:
                        # 移除+号和%号
                        value = value.replace('+', '').replace('%', '')
                        return float(value)
            
            # 如果没找到对应时间框架，使用第一个
            logger.warning(f"未找到时间框架 {timeframe_to_use}，使用第一个可用的")
            first_part = parts[0]
            if ':' in first_part:
                _, value = first_part.split(':', 1)
                value = value.strip().replace('+', '').replace('%', '')
                return float(value)
        else:
            # 简单格式，直接解析
            pnl_str = pnl_str.replace('+', '').replace('%', '')
            return float(pnl_str)
            
    except (ValueError, TypeError) as e:
        logger.debug(f"解析PnL失败: {pnl_string}, 错误: {e}")
        return 0.0
    
    return 0.0

def pnl_to_label(pnl_change: float) -> int:
    """将PnL变化转换为标签"""
    thresholds = config.PNL_THRESHOLDS
    
    if pnl_change <= thresholds["strong_down"]:
        return 0  # 强烈下跌
    elif pnl_change <= thresholds["down"]:
        return 1  # 下跌
    elif pnl_change >= thresholds["strong_up"]:
        return 4  # 强烈上涨
    elif pnl_change >= thresholds["up"]:
        return 3  # 上涨
    else:
        return 2  # 持平

def load_and_process_data() -> pd.DataFrame:
    """加载和处理数据"""
    logger.info("开始加载数据...")
    
    # 查找所有CSV文件
    csv_files = []
    for file in os.listdir(config.DATA_DIR):
        if file.endswith('.csv'):
            csv_files.append(os.path.join(config.DATA_DIR, file))
    
    if not csv_files:
        raise FileNotFoundError(f"在 {config.DATA_DIR} 中未找到CSV文件")
    
    logger.info(f"找到 {len(csv_files)} 个CSV文件")
    
    # 读取所有CSV文件
    dataframes = []
    for file in csv_files:
        try:
            df = pd.read_csv(file)
            dataframes.append(df)
            logger.info(f"读取文件: {file}, 行数: {len(df)}")
            # 调试：查看列名
            logger.info(f"文件列: {list(df.columns)}")
        except Exception as e:
            logger.warning(f"读取文件 {file} 失败: {e}")
    
    if not dataframes:
        raise ValueError("没有成功读取任何CSV文件")
    
    # 合并数据
    data = pd.concat(dataframes, ignore_index=True)
    logger.info(f"合并后总行数: {len(data)}")
    
    # 数据预处理
    logger.info("开始数据预处理...")
    
    # 检查必要列
    required_columns = ['title', 'content', 'pnl_rate']
    missing_columns = [col for col in required_columns if col not in data.columns]
    if missing_columns:
        raise ValueError(f"缺少必要列: {missing_columns}")
    
    # 调试：查看数据样本
    logger.info("=== 数据样本调试 ===")
    sample_row = data.iloc[0] if len(data) > 0 else None
    if sample_row is not None:
        logger.info(f"样本标题: {sample_row.get('title', 'N/A')[:100]}...")
        logger.info(f"样本内容: {sample_row.get('content', 'N/A')[:100]}...")
        logger.info(f"样本PnL率: {sample_row.get('pnl_rate', 'N/A')}")
        if 'symbol' in sample_row:
            logger.info(f"样本Symbol: {sample_row.get('symbol', 'N/A')}")
    
    # 清理数据 - 添加文本长度过滤
    data = data.dropna(subset=['title', 'content', 'pnl_rate'])
    data = data[data['title'].str.len() >= config.MIN_TEXT_LENGTH]
    data = data[data['content'].str.len() >= config.MIN_TEXT_LENGTH]
    
    # 限制文本最大长度
    data = data[data['title'].str.len() <= config.MAX_TEXT_LENGTH]
    data = data[data['content'].str.len() <= config.MAX_TEXT_LENGTH]
    
    logger.info(f"文本长度过滤后: {len(data)} 条记录")
    
    # 解析PnL并生成标签
    data['pnl_change'] = data['pnl_rate'].apply(lambda x: parse_pnl_rate(x, config.TIMEFRAME))
    data['label'] = data['pnl_change'].apply(pnl_to_label)
    
    # 调试：查看PnL分布
    logger.info("=== PnL分布调试 ===")
    pnl_stats = data['pnl_change'].describe()
    logger.info(f"PnL统计: \n{pnl_stats}")
    
    # 统计不同PnL范围的数量
    strong_down = len(data[data['pnl_change'] <= config.PNL_THRESHOLDS['strong_down']])
    down = len(data[(data['pnl_change'] > config.PNL_THRESHOLDS['strong_down']) & 
                    (data['pnl_change'] <= config.PNL_THRESHOLDS['down'])])
    up = len(data[(data['pnl_change'] >= config.PNL_THRESHOLDS['up']) & 
                  (data['pnl_change'] < config.PNL_THRESHOLDS['strong_up'])])
    strong_up = len(data[data['pnl_change'] >= config.PNL_THRESHOLDS['strong_up']])
    stable = len(data[(data['pnl_change'] > config.PNL_THRESHOLDS['down']) & 
                      (data['pnl_change'] < config.PNL_THRESHOLDS['up'])])
    
    logger.info(f"PnL阈值分布:")
    logger.info(f"  强烈下跌 (<= {config.PNL_THRESHOLDS['strong_down']}%): {strong_down}")
    logger.info(f"  下跌 ({config.PNL_THRESHOLDS['strong_down']}% ~ {config.PNL_THRESHOLDS['down']}%): {down}")
    logger.info(f"  持平 ({config.PNL_THRESHOLDS['down']}% ~ {config.PNL_THRESHOLDS['up']}%): {stable}")
    logger.info(f"  上涨 ({config.PNL_THRESHOLDS['up']}% ~ {config.PNL_THRESHOLDS['strong_up']}%): {up}")
    logger.info(f"  强烈上涨 (>= {config.PNL_THRESHOLDS['strong_up']}%): {strong_up}")
    
    # 过滤无效数据
    data = data[data['pnl_change'].notna()]
    data = data[data['label'].notna()]
    
    logger.info(f"PnL数据清理后: {len(data)} 条记录")
    
    # 如果所有数据都是同一标签，调整阈值或跳过symbol处理
    unique_labels = data['label'].nunique()
    if unique_labels == 1:
        logger.warning(f"⚠️  所有数据都是同一标签: {data['label'].iloc[0]}，这会导致训练问题!")
        logger.warning("建议调整PnL阈值或检查数据质量")
        
        # 尝试调整阈值
        logger.info("尝试使用更宽松的阈值...")
        new_thresholds = {
            'strong_down': -1.0, 'down': -0.2, 'up': 0.2, 'strong_up': 1.0
        }
        config.adjust_pnl_thresholds(new_thresholds)
        data['label'] = data['pnl_change'].apply(pnl_to_label)
        unique_labels_new = data['label'].nunique()
        logger.info(f"调整阈值后标签种类: {unique_labels_new}")
        logger.info(f"新阈值: {config.PNL_THRESHOLDS}")
    
    # 加载有效的symbols列表
    valid_symbols = load_binance_symbols()
    logger.info(f"加载了 {len(valid_symbols)} 个有效交易对")
    
    # Symbol处理
    original_len = len(data)
    symbol_processed = False
    
    if config.SYMBOL_PROCESSING_MODE == 'disabled' or not config.ENABLE_SYMBOL_PROCESSING:
        logger.info("跳过Symbol处理（已禁用）")
    else:
        logger.info(f"开始Symbol处理（模式: {config.SYMBOL_PROCESSING_MODE}）...")
        if config.SPLIT_MULTI_SYMBOL_NEWS:
            processed_data = process_news_symbols(data, valid_symbols)
            
            # 检查处理后的数据量
            reduction_ratio = (original_len - len(processed_data)) / original_len
            logger.info(f"Symbol处理结果: {original_len} -> {len(processed_data)} 条记录 (减少 {reduction_ratio:.1%})")
            
            # 如果数据减少太多，根据模式决定是否使用
            if config.SYMBOL_PROCESSING_MODE == 'strict':
                # 严格模式：总是使用Symbol处理结果
                data = processed_data
                symbol_processed = True
                logger.info("✅ 使用Symbol处理结果（严格模式）")
            elif config.SYMBOL_PROCESSING_MODE == 'flexible':
                # 灵活模式：如果数据减少超过90%或少于100条，则不使用
                if len(processed_data) < 100 or reduction_ratio > 0.9:
                    logger.warning(f"⚠️  Symbol处理后数据过少 ({len(processed_data)}条) 或减少过多 ({reduction_ratio:.1%})")
                    logger.info("🔄 使用原始数据（灵活模式回退）")
                    symbol_processed = False
                else:
                    data = processed_data
                    symbol_processed = True
                    logger.info("✅ 使用Symbol处理结果（灵活模式）")
            
            if len(data) == 0:
                logger.error("❌ Symbol处理后没有剩余数据!")
                logger.info("🔄 强制回退到原始数据")
                # 重新加载原始数据
                data = pd.concat(dataframes, ignore_index=True)
                data = data.dropna(subset=['title', 'content', 'pnl_rate'])
                # 重新应用文本长度过滤
                data = data[data['title'].str.len() >= config.MIN_TEXT_LENGTH]
                data = data[data['content'].str.len() >= config.MIN_TEXT_LENGTH]
                data = data[data['title'].str.len() <= config.MAX_TEXT_LENGTH]
                data = data[data['content'].str.len() <= config.MAX_TEXT_LENGTH]
                # 重新处理PnL
                data['pnl_change'] = data['pnl_rate'].apply(lambda x: parse_pnl_rate(x, config.TIMEFRAME))
                data['label'] = data['pnl_change'].apply(pnl_to_label)
                data = data[data['pnl_change'].notna()]
                data = data[data['label'].notna()]
                symbol_processed = False
                logger.info(f"强制回退模式数据量: {len(data)} 条记录")
        else:
            logger.info("跳过多symbol新闻拆分")
    
    if not symbol_processed:
        logger.info("使用原始数据进行训练")
    
    # 统计排除的symbols
    excluded_count = 0
    if 'symbol' in data.columns:
        for symbol in data['symbol'].unique():
            if symbol in config.EXCLUDED_SYMBOLS:
                excluded_count += len(data[data['symbol'] == symbol])
    
    if excluded_count > 0:
        logger.info(f"排除了 {excluded_count} 条稳定币相关记录")
    
    logger.info(f"Symbol处理完成，最终样本数: {len(data)}")
    
    # 统计最终标签分布
    logger.info("最终标签分布:")
    label_counts = data['label'].value_counts().sort_index()
    for label, count in label_counts.items():
        percentage = count / len(data) * 100
        logger.info(f"  标签 {label}: {count} ({percentage:.1f}%)")
    
    # 检查数据平衡性
    if len(label_counts) > 1:
        max_count = label_counts.max()
        min_count = label_counts.min()
        imbalance_ratio = max_count / min_count
        if imbalance_ratio > 10:
            logger.warning(f"⚠️  数据严重不平衡! 最大/最小比例: {imbalance_ratio:.1f}")
    
    # 统计时间框架相关信息
    logger.info(f"当前时间框架: {config.TIMEFRAME}")
    logger.info(f"PnL阈值: {config.PNL_THRESHOLDS}")
    logger.info(f"最小波动阈值: {config.MIN_VOLATILITY_THRESHOLDS.get(config.TIMEFRAME, 'N/A')}")
    
    return data

def create_data_loaders(data: pd.DataFrame, tokenizer: CustomTokenizer) -> Tuple[DataLoader, DataLoader, DataLoader]:
    """创建数据加载器"""
    logger.info("创建数据加载器...")
    
    # 先划分训练+验证 和 测试集
    train_val_data, test_data = train_test_split(
        data, 
        test_size=0.2, 
        stratify=data['label'], 
        random_state=42
    )
    
    # 再划分训练和验证集
    train_data, val_data = train_test_split(
        train_val_data, 
        test_size=0.1875,  # 0.15 / 0.8 = 0.1875 (保持验证集为总数据的15%)
        stratify=train_val_data['label'], 
        random_state=42
    )
    
    logger.info(f"训练集大小: {len(train_data)} ({len(train_data)/len(data)*100:.1f}%)")
    logger.info(f"验证集大小: {len(val_data)} ({len(val_data)/len(data)*100:.1f}%)")
    logger.info(f"测试集大小: {len(test_data)} ({len(test_data)/len(data)*100:.1f}%)")
    
    # 创建数据集
    train_dataset = NewsDataset(train_data, tokenizer, config.MAX_LENGTH)
    val_dataset = NewsDataset(val_data, tokenizer, config.MAX_LENGTH)
    test_dataset = NewsDataset(test_data, tokenizer, config.MAX_LENGTH)
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=config.BATCH_SIZE,
        shuffle=True,
        num_workers=2,
        pin_memory=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config.BATCH_SIZE,
        shuffle=False,
        num_workers=2,
        pin_memory=True
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=config.BATCH_SIZE,
        shuffle=False,
        num_workers=2,
        pin_memory=True
    )
    
    return train_loader, val_loader, test_loader

def train_epoch(model: nn.Module, train_loader: DataLoader, optimizer, scheduler, 
                criterion, epoch: int, scaler=None) -> Tuple[float, float]:
    """训练一个epoch"""
    model.train()
    total_loss = 0.0
    total_correct = 0
    total_samples = 0
    
    progress_bar = tqdm(train_loader, desc=f'Epoch {epoch}')
    
    for batch_idx, batch in enumerate(progress_bar):
        input_ids = batch['input_ids'].to(config.DEVICE, non_blocking=True)
        attention_mask = batch['attention_mask'].to(config.DEVICE, non_blocking=True)
        labels = batch['labels'].to(config.DEVICE, non_blocking=True)
        
        if scaler is not None:
            with torch.cuda.amp.autocast():
                logits = model(input_ids, attention_mask)
                loss = criterion(logits, labels) / config.ACCUMULATION_STEPS
            
            scaler.scale(loss).backward()
            
            if (batch_idx + 1) % config.ACCUMULATION_STEPS == 0:
                scaler.unscale_(optimizer)
                torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
                scaler.step(optimizer)
                scaler.update()
                optimizer.zero_grad()
                scheduler.step()
        else:
            logits = model(input_ids, attention_mask)
            loss = criterion(logits, labels) / config.ACCUMULATION_STEPS
            loss.backward()
            
            if (batch_idx + 1) % config.ACCUMULATION_STEPS == 0:
                torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
                optimizer.step()
                optimizer.zero_grad()
                scheduler.step()
        
        # 统计
        total_loss += loss.item() * config.ACCUMULATION_STEPS
        predictions = torch.argmax(logits, dim=1)
        total_correct += (predictions == labels).sum().item()
        total_samples += labels.size(0)
        
        # 更新进度条
        current_lr = optimizer.param_groups[0]['lr']
        progress_bar.set_postfix({
            'loss': f'{loss.item() * config.ACCUMULATION_STEPS:.4f}',
            'acc': f'{total_correct/total_samples:.4f}',
            'lr': f'{current_lr:.2e}'
        })
    
    avg_loss = total_loss / len(train_loader)
    accuracy = total_correct / total_samples
    
    return avg_loss, accuracy

def evaluate(model: nn.Module, val_loader: DataLoader, criterion) -> Tuple[float, float, float, float]:
    """评估模型"""
    model.eval()
    total_loss = 0.0
    all_predictions = []
    all_labels = []
    all_probabilities = []
    
    with torch.no_grad():
        for batch in tqdm(val_loader, desc='Evaluating'):
            input_ids = batch['input_ids'].to(config.DEVICE, non_blocking=True)
            attention_mask = batch['attention_mask'].to(config.DEVICE, non_blocking=True)
            labels = batch['labels'].to(config.DEVICE, non_blocking=True)
            
            logits = model(input_ids, attention_mask)
            loss = criterion(logits, labels)
            
            total_loss += loss.item()
            
            # 获取预测和概率
            probabilities = F.softmax(logits, dim=1)
            predictions = torch.argmax(logits, dim=1)
            
            all_predictions.extend(predictions.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
            all_probabilities.extend(probabilities.cpu().numpy())
    
    # 计算基础指标
    avg_loss = total_loss / len(val_loader)
    accuracy = accuracy_score(all_labels, all_predictions)
    f1 = f1_score(all_labels, all_predictions, average='weighted')
    
    # 计算AUC - 多分类使用One-vs-Rest方式
    try:
        # 检查是否有足够的类别来计算AUC
        unique_labels = list(set(all_labels))
        if len(unique_labels) > 1:
            # 将标签二值化用于多分类AUC计算
            y_binary = label_binarize(all_labels, classes=range(config.NUM_LABELS))
            y_scores = np.array(all_probabilities)
            
            # 如果只有2个类别，需要特殊处理
            if len(unique_labels) == 2:
                # 对于二分类，使用其中一个类别的概率
                class_idx = unique_labels[1] if 1 in unique_labels else unique_labels[0]
                auc_score = roc_auc_score(
                    [1 if label == class_idx else 0 for label in all_labels],
                    [prob[class_idx] for prob in all_probabilities]
                )
            else:
                # 多分类AUC (macro-average)
                auc_score = roc_auc_score(y_binary, y_scores, average='macro', multi_class='ovr')
        else:
            logger.warning("只有一个类别，无法计算AUC")
            auc_score = 0.0
    except Exception as e:
        logger.warning(f"计算AUC时出错: {e}")
        auc_score = 0.0
    
    return avg_loss, accuracy, f1, auc_score

def test_saved_model(model_path: str, tokenizer: CustomTokenizer, test_loader: DataLoader) -> Dict[str, Any]:
    """测试保存的模型"""
    logger.info("=== 开始测试保存的模型 ===")
    
    try:
        # 加载保存的模型
        checkpoint = torch.load(model_path, map_location=config.DEVICE)
        
        # 重新创建模型
        model = CustomBertClassifier(tokenizer.vocab_size, config.NUM_LABELS)
        model.load_state_dict(checkpoint['model_state_dict'])
        model = model.to(config.DEVICE)
        model.eval()
        
        logger.info(f"成功加载模型: {model_path}")
        logger.info(f"模型训练时的准确率: {checkpoint.get('val_acc', 'N/A')}")
        logger.info(f"模型训练时的F1分数: {checkpoint.get('val_f1', 'N/A')}")
        
        # 测试模型
        all_predictions = []
        all_labels = []
        all_probabilities = []
        
        with torch.no_grad():
            for batch in tqdm(test_loader, desc='Testing saved model'):
                input_ids = batch['input_ids'].to(config.DEVICE, non_blocking=True)
                attention_mask = batch['attention_mask'].to(config.DEVICE, non_blocking=True)
                labels = batch['labels'].to(config.DEVICE, non_blocking=True)
                
                logits = model(input_ids, attention_mask)
                probabilities = F.softmax(logits, dim=1)
                predictions = torch.argmax(logits, dim=1)
                
                all_predictions.extend(predictions.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
                all_probabilities.extend(probabilities.cpu().numpy())
        
        # 计算测试指标
        test_accuracy = accuracy_score(all_labels, all_predictions)
        test_f1 = f1_score(all_labels, all_predictions, average='weighted')
        
        # 计算测试AUC
        try:
            unique_labels = list(set(all_labels))
            if len(unique_labels) > 1:
                y_binary = label_binarize(all_labels, classes=range(config.NUM_LABELS))
                y_scores = np.array(all_probabilities)
                
                if len(unique_labels) == 2:
                    class_idx = unique_labels[1] if 1 in unique_labels else unique_labels[0]
                    test_auc = roc_auc_score(
                        [1 if label == class_idx else 0 for label in all_labels],
                        [prob[class_idx] for prob in all_probabilities]
                    )
                else:
                    test_auc = roc_auc_score(y_binary, y_scores, average='macro', multi_class='ovr')
            else:
                logger.warning("测试集只有一个类别，无法计算AUC")
                test_auc = 0.0
        except Exception as e:
            logger.warning(f"计算测试AUC时出错: {e}")
            test_auc = 0.0
        
        # 分类报告
        from sklearn.metrics import classification_report, confusion_matrix
        
        # 获取实际存在的类别
        unique_labels = sorted(list(set(all_labels + all_predictions)))
        target_names = ["强烈下跌", "下跌", "持平", "上涨", "强烈上涨"]
        
        # 只使用实际存在的类别名称
        actual_target_names = [target_names[i] for i in unique_labels if i < len(target_names)]
        
        class_report = classification_report(all_labels, all_predictions, 
                                           labels=unique_labels,
                                           target_names=actual_target_names, 
                                           zero_division=0)
        
        confusion_mat = confusion_matrix(all_labels, all_predictions)
        
        # 计算每个类别的置信度统计
        confidence_stats = {}
        for i in range(config.NUM_LABELS):
            class_indices = [j for j, label in enumerate(all_labels) if label == i]
            if class_indices:
                class_confidences = [all_probabilities[j][i] for j in class_indices]
                confidence_stats[i] = {
                    'mean_confidence': np.mean(class_confidences),
                    'std_confidence': np.std(class_confidences),
                    'min_confidence': np.min(class_confidences),
                    'max_confidence': np.max(class_confidences)
                }
        
        # 输出测试结果
        logger.info("=== 模型测试结果 ===")
        logger.info(f"测试准确率: {test_accuracy:.4f}")
        logger.info(f"测试F1分数: {test_f1:.4f}")
        logger.info(f"测试AUC: {test_auc:.4f}")
        logger.info(f"分类报告:\n{class_report}")
        logger.info(f"混淆矩阵:\n{confusion_mat}")
        
        logger.info("各类别置信度统计:")
        for label, stats in confidence_stats.items():
            logger.info(f"  标签 {label}: 平均置信度={stats['mean_confidence']:.3f}, "
                       f"标准差={stats['std_confidence']:.3f}")
        
        # 检测过拟合
        training_acc = checkpoint.get('val_acc', 0)
        training_auc = checkpoint.get('val_auc', 0)
        
        if training_acc > 0:
            acc_diff = training_acc - test_accuracy
            if acc_diff > 0.1:
                logger.warning(f"⚠️  可能存在过拟合! 训练准确率({training_acc:.4f}) - 测试准确率({test_accuracy:.4f}) = {acc_diff:.4f}")
            else:
                logger.info(f"✅ 模型泛化良好! 准确率差异: {acc_diff:.4f}")
        
        if training_auc > 0:
            auc_diff = training_auc - test_auc
            if auc_diff > 0.1:
                logger.warning(f"⚠️  AUC过拟合! 训练AUC({training_auc:.4f}) - 测试AUC({test_auc:.4f}) = {auc_diff:.4f}")
            else:
                logger.info(f"✅ AUC泛化良好! AUC差异: {auc_diff:.4f}")
        
        # 检测异常结果
        if test_accuracy > 0.95:
            logger.warning("⚠️  测试准确率过高(>95%)，可能存在数据泄露或标签问题!")
        
        if test_auc > 0.99:
            logger.warning("⚠️  测试AUC过高(>99%)，可能存在数据泄露或标签问题!")
        
        if len(set(all_predictions)) == 1:
            logger.error("❌ 模型只预测一个类别，存在严重问题!")
        
        return {
            'test_accuracy': test_accuracy,
            'test_f1': test_f1,
            'test_auc': test_auc,
            'classification_report': class_report,
            'confusion_matrix': confusion_mat,
            'confidence_stats': confidence_stats,
            'predictions': all_predictions,
            'labels': all_labels,
            'probabilities': all_probabilities
        }
        
    except Exception as e:
        logger.error(f"测试模型时出错: {e}")
        return {}

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='加密货币新闻PnL预测模型训练')
    
    # 训练参数
    parser.add_argument('--epochs', type=int, default=None, help='训练轮数 (默认: 8)')
    parser.add_argument('--batch_size', type=int, default=None, help='批处理大小 (默认: 16)')
    parser.add_argument('--timeframe', type=str, default=None, 
                        choices=['1h', '4h', '6h', '12h', '24h'], 
                        help='时间框架 (默认: 4h)')
    parser.add_argument('--learning_rate', type=float, default=None, help='学习率 (默认: 4e-5)')
    
    # 模型参数
    parser.add_argument('--hidden_size', type=int, default=None, 
                        choices=[256, 512, 768], help='隐藏层大小 (默认: 512)')
    parser.add_argument('--num_layers', type=int, default=None, 
                        choices=[4, 6, 8, 12], help='BERT层数 (默认: 6)')
    
    return parser.parse_args()

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()
    
    # 更新配置
    config.update_from_args(args)
    
    logger.info("=== 加密货币新闻PnL预测模型训练开始 ===")
    logger.info(f"使用设备: {config.DEVICE}")
    
    # 打印配置
    config.print_config()
    
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        logger.info(f"GPU: {gpu_name}, 内存: {gpu_memory:.1f}GB")
    
    # 加载分词器
    logger.info("加载自定义分词器...")
    tokenizer = CustomTokenizer(config.TOKENIZER_MODEL)
    
    # 加载和处理数据
    data = load_and_process_data()
    train_loader, val_loader, test_loader = create_data_loaders(data, tokenizer)
    
    # 创建模型
    logger.info("创建模型...")
    model = CustomBertClassifier(tokenizer.vocab_size, config.NUM_LABELS)
    model = model.to(config.DEVICE)
    
    # 打印模型参数统计
    param_stats = model.get_trainable_parameters()
    logger.info(f"模型参数: 总计{param_stats['total_params']:,}, "
                f"可训练{param_stats['trainable_params']:,} "
                f"({param_stats['trainable_ratio']:.1%})")
    
    # 设置优化器 - 分层学习率
    bert_params = []
    embedding_params = []
    classifier_params = []
    
    for name, param in model.named_parameters():
        if param.requires_grad:
            if 'classifier' in name:
                classifier_params.append(param)
            elif 'embeddings' in name:
                embedding_params.append(param)
            else:
                bert_params.append(param)
    
    bert_lr = config.LEARNING_RATE * 0.3
    embedding_lr = config.LEARNING_RATE * config.EMBEDDING_LR_MULTIPLIER
    classifier_lr = config.LEARNING_RATE
    
    optimizer = AdamW([
        {'params': bert_params, 'lr': bert_lr, 'weight_decay': 0.01},
        {'params': embedding_params, 'lr': embedding_lr, 'weight_decay': 0.01},
        {'params': classifier_params, 'lr': classifier_lr, 'weight_decay': 0.01}
    ])
    
    logger.info(f"学习率设置: BERT={bert_lr:.2e}, Embedding={embedding_lr:.2e}, 分类器={classifier_lr:.2e}")
    
    # 学习率调度器
    total_steps = len(train_loader) * config.NUM_EPOCHS // config.ACCUMULATION_STEPS
    warmup_steps = int(config.WARMUP_RATIO * total_steps)
    
    scheduler = get_cosine_schedule_with_warmup(
        optimizer,
        num_warmup_steps=warmup_steps,
        num_training_steps=total_steps
    )
    
    logger.info(f"总训练步数: {total_steps}, 预热步数: {warmup_steps}")
    
    # 损失函数
    if config.USE_FOCAL_LOSS:
        criterion = FocalLoss(alpha=config.FOCAL_ALPHA, gamma=config.FOCAL_GAMMA)
        logger.info(f"使用Focal Loss: alpha={config.FOCAL_ALPHA}, gamma={config.FOCAL_GAMMA}")
    else:
        criterion = nn.CrossEntropyLoss()
        logger.info("使用交叉熵损失")
    
    # 混合精度训练
    scaler = torch.cuda.amp.GradScaler() if torch.cuda.is_available() else None
    
    # 训练循环
    logger.info("开始训练...")
    best_val_acc = 0.0
    best_val_f1 = 0.0
    best_val_auc = 0.0
    
    for epoch in range(1, config.NUM_EPOCHS + 1):
        epoch_start = time.time()
        
        # 训练
        train_loss, train_acc = train_epoch(
            model, train_loader, optimizer, scheduler, criterion, epoch, scaler
        )
        
        # 验证
        val_loss, val_acc, val_f1, val_auc = evaluate(model, val_loader, criterion)
        
        epoch_time = time.time() - epoch_start
        
        logger.info(
            f"Epoch {epoch}/{config.NUM_EPOCHS} - "
            f"训练损失: {train_loss:.4f}, 训练准确率: {train_acc:.4f}, "
            f"验证损失: {val_loss:.4f}, 验证准确率: {val_acc:.4f}, "
            f"验证F1: {val_f1:.4f}, 验证AUC: {val_auc:.4f}, 时间: {epoch_time:.2f}s"
        )
        
        # 保存最佳模型 - 优先使用AUC作为评估指标
        if val_auc > best_val_auc:
            best_val_acc = val_acc
            best_val_f1 = val_f1
            best_val_auc = val_auc
            
            # 保存模型
            model_save_path = os.path.join(config.OUTPUT_DIR, 'best_model.pt')
            torch.save({
                'model_state_dict': model.state_dict(),
                'tokenizer_vocab_size': tokenizer.vocab_size,
                'val_acc': val_acc,
                'val_f1': val_f1,
                'val_auc': val_auc,
                'epoch': epoch,
                'config': {
                    'hidden_size': config.HIDDEN_SIZE,
                    'num_layers': config.NUM_LAYERS,
                    'num_heads': config.NUM_HEADS,
                    'num_labels': config.NUM_LABELS
                }
            }, model_save_path)
            
            logger.info(f"✅ 保存最佳模型: 验证AUC={val_auc:.4f}, 准确率={val_acc:.4f}, F1={val_f1:.4f}")
    
    # 训练完成
    logger.info("=== 训练完成 ===")
    logger.info(f"最佳验证准确率: {best_val_acc:.4f}")
    logger.info(f"最佳验证F1分数: {best_val_f1:.4f}")
    logger.info(f"最佳验证AUC: {best_val_auc:.4f}")
    model_save_path = os.path.join(config.OUTPUT_DIR, 'best_model.pt')
    logger.info(f"模型保存路径: {model_save_path}")
    
    # 测试保存的模型
    test_results = test_saved_model(model_save_path, tokenizer, test_loader)
    
    # 保存配置
    config_save_path = os.path.join(config.OUTPUT_DIR, 'training_config.json')
    with open(config_save_path, 'w', encoding='utf-8') as f:
        json.dump({
            'model_config': {
                'hidden_size': config.HIDDEN_SIZE,
                'num_layers': config.NUM_LAYERS,
                'num_heads': config.NUM_HEADS,
                'vocab_size': tokenizer.vocab_size,
                'num_labels': config.NUM_LABELS
            },
            'training_config': {
                'batch_size': config.BATCH_SIZE,
                'learning_rate': config.LEARNING_RATE,
                'num_epochs': config.NUM_EPOCHS,
                'timeframe': config.TIMEFRAME,
                'use_focal_loss': config.USE_FOCAL_LOSS
            },
            'results': {
                'best_val_acc': best_val_acc,
                'best_val_f1': best_val_f1,
                'best_val_auc': best_val_auc,
                'test_acc': test_results.get('test_accuracy', 0),
                'test_f1': test_results.get('test_f1', 0),
                'test_auc': test_results.get('test_auc', 0)
            }
        }, f, indent=2, ensure_ascii=False)
    
    logger.info(f"配置保存路径: {config_save_path}")
    
    return {
        'best_val_acc': best_val_acc,
        'best_val_f1': best_val_f1,
        'best_val_auc': best_val_auc,
        'test_results': test_results,
        'model_path': model_save_path,
        'config_path': config_save_path
    }

def train_with_params(epochs=None, batch_size=None, timeframe=None, learning_rate=None, 
                     hidden_size=None, num_layers=None):
    """
    便捷训练函数 - 可直接在Jupyter/Kaggle中调用
    
    Args:
        epochs: 训练轮数
        batch_size: 批处理大小  
        timeframe: 时间框架 ('1h', '4h', '6h', '12h', '24h')
        learning_rate: 学习率
        hidden_size: 隐藏层大小 (256, 512, 768)
        num_layers: BERT层数 (4, 6, 8, 12)
    
    Example:
        # 训练20轮，批处理64，1小时时间框架
        train_with_params(epochs=20, batch_size=64, timeframe='1h')
    """
    # 创建模拟的args对象
    class Args:
        def __init__(self):
            self.epochs = epochs
            self.batch_size = batch_size
            self.timeframe = timeframe
            self.learning_rate = learning_rate
            self.hidden_size = hidden_size
            self.num_layers = num_layers
    
    args = Args()
    
    # 更新配置
    config.update_from_args(args)
    
    logger.info("=== 加密货币新闻PnL预测模型训练开始 ===")
    logger.info(f"使用设备: {config.DEVICE}")
    
    # 打印配置
    config.print_config()
    
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        logger.info(f"GPU: {gpu_name}, 内存: {gpu_memory:.1f}GB")
    
    # 加载分词器
    logger.info("加载自定义分词器...")
    tokenizer = CustomTokenizer(config.TOKENIZER_MODEL)
    
    # 加载和处理数据
    data = load_and_process_data()
    train_loader, val_loader, test_loader = create_data_loaders(data, tokenizer)
    
    # 创建模型
    logger.info("创建模型...")
    model = CustomBertClassifier(tokenizer.vocab_size, config.NUM_LABELS)
    model = model.to(config.DEVICE)
    
    # 打印模型参数统计
    param_stats = model.get_trainable_parameters()
    logger.info(f"模型参数: 总计{param_stats['total_params']:,}, "
                f"可训练{param_stats['trainable_params']:,} "
                f"({param_stats['trainable_ratio']:.1%})")
    
    # 设置优化器 - 分层学习率
    bert_params = []
    embedding_params = []
    classifier_params = []
    
    for name, param in model.named_parameters():
        if param.requires_grad:
            if 'classifier' in name:
                classifier_params.append(param)
            elif 'embeddings' in name:
                embedding_params.append(param)
            else:
                bert_params.append(param)
    
    bert_lr = config.LEARNING_RATE * 0.3
    embedding_lr = config.LEARNING_RATE * config.EMBEDDING_LR_MULTIPLIER
    classifier_lr = config.LEARNING_RATE
    
    optimizer = AdamW([
        {'params': bert_params, 'lr': bert_lr, 'weight_decay': 0.01},
        {'params': embedding_params, 'lr': embedding_lr, 'weight_decay': 0.01},
        {'params': classifier_params, 'lr': classifier_lr, 'weight_decay': 0.01}
    ])
    
    logger.info(f"学习率设置: BERT={bert_lr:.2e}, Embedding={embedding_lr:.2e}, 分类器={classifier_lr:.2e}")
    
    # 学习率调度器
    total_steps = len(train_loader) * config.NUM_EPOCHS // config.ACCUMULATION_STEPS
    warmup_steps = int(config.WARMUP_RATIO * total_steps)
    
    scheduler = get_cosine_schedule_with_warmup(
        optimizer,
        num_warmup_steps=warmup_steps,
        num_training_steps=total_steps
    )
    
    logger.info(f"总训练步数: {total_steps}, 预热步数: {warmup_steps}")
    
    # 损失函数
    if config.USE_FOCAL_LOSS:
        criterion = FocalLoss(alpha=config.FOCAL_ALPHA, gamma=config.FOCAL_GAMMA)
        logger.info(f"使用Focal Loss: alpha={config.FOCAL_ALPHA}, gamma={config.FOCAL_GAMMA}")
    else:
        criterion = nn.CrossEntropyLoss()
        logger.info("使用交叉熵损失")
    
    # 混合精度训练
    scaler = torch.cuda.amp.GradScaler() if torch.cuda.is_available() else None
    
    # 训练循环
    logger.info("开始训练...")
    best_val_acc = 0.0
    best_val_f1 = 0.0
    best_val_auc = 0.0

    for epoch in range(1, config.NUM_EPOCHS + 1):
        epoch_start = time.time()
        
        # 训练
        train_loss, train_acc = train_epoch(
            model, train_loader, optimizer, scheduler, criterion, epoch, scaler
        )
        
        # 验证
        val_loss, val_acc, val_f1, val_auc = evaluate(model, val_loader, criterion)
        
        epoch_time = time.time() - epoch_start
        
        logger.info(
            f"Epoch {epoch}/{config.NUM_EPOCHS} - "
            f"训练损失: {train_loss:.4f}, 训练准确率: {train_acc:.4f}, "
            f"验证损失: {val_loss:.4f}, 验证准确率: {val_acc:.4f}, "
            f"验证F1: {val_f1:.4f}, 验证AUC: {val_auc:.4f}, 时间: {epoch_time:.2f}s"
        )
        
        # 保存最佳模型 - 优先使用AUC作为评估指标
        if val_auc > best_val_auc:
            best_val_acc = val_acc
            best_val_f1 = val_f1
            best_val_auc = val_auc
            
            # 保存模型
            model_save_path = os.path.join(config.OUTPUT_DIR, 'best_model.pt')
            torch.save({
                'model_state_dict': model.state_dict(),
                'tokenizer_vocab_size': tokenizer.vocab_size,
                'val_acc': val_acc,
                'val_f1': val_f1,
                'val_auc': val_auc,
                'epoch': epoch,
                'config': {
                    'hidden_size': config.HIDDEN_SIZE,
                    'num_layers': config.NUM_LAYERS,
                    'num_heads': config.NUM_HEADS,
                    'num_labels': config.NUM_LABELS
                }
            }, model_save_path)
            
            logger.info(f"✅ 保存最佳模型: 验证AUC={val_auc:.4f}, 准确率={val_acc:.4f}, F1={val_f1:.4f}")
    
    # 训练完成
    logger.info("=== 训练完成 ===")
    logger.info(f"最佳验证准确率: {best_val_acc:.4f}")
    logger.info(f"最佳验证F1分数: {best_val_f1:.4f}")
    logger.info(f"最佳验证AUC: {best_val_auc:.4f}")
    model_save_path = os.path.join(config.OUTPUT_DIR, 'best_model.pt')
    logger.info(f"模型保存路径: {model_save_path}")
    
    # 测试保存的模型
    test_results = test_saved_model(model_save_path, tokenizer, test_loader)
    
    # 保存配置
    config_save_path = os.path.join(config.OUTPUT_DIR, 'training_config.json')
    with open(config_save_path, 'w', encoding='utf-8') as f:
        json.dump({
            'model_config': {
                'hidden_size': config.HIDDEN_SIZE,
                'num_layers': config.NUM_LAYERS,
                'num_heads': config.NUM_HEADS,
                'vocab_size': tokenizer.vocab_size,
                'num_labels': config.NUM_LABELS
            },
            'training_config': {
                'batch_size': config.BATCH_SIZE,
                'learning_rate': config.LEARNING_RATE,
                'num_epochs': config.NUM_EPOCHS,
                'timeframe': config.TIMEFRAME,
                'use_focal_loss': config.USE_FOCAL_LOSS
            },
            'results': {
                'best_val_acc': best_val_acc,
                'best_val_f1': best_val_f1,
                'best_val_auc': best_val_auc,
                'test_acc': test_results.get('test_accuracy', 0),
                'test_f1': test_results.get('test_f1', 0),
                'test_auc': test_results.get('test_auc', 0)
            }
        }, f, indent=2, ensure_ascii=False)
    
    logger.info(f"配置保存路径: {config_save_path}")
    
    return {
        'best_val_acc': best_val_acc,
        'best_val_f1': best_val_f1,
        'best_val_auc': best_val_auc,
        'test_results': test_results,
        'model_path': model_save_path,
        'config_path': config_save_path
    }

if __name__ == "__main__":
    results = train_with_params(epochs=20, batch_size=128, timeframe='1h')

# ===== 使用示例 =====
"""
在Kaggle Notebook中的使用方法:

方法1: 直接调用便捷函数 (推荐)
```python
# 运行您要求的配置: 20轮，批处理64，1小时时间框架
results = train_with_params(epochs=20, batch_size=64, timeframe='1h')
print(f"训练完成! 最佳准确率: {results['best_val_acc']:.4f}")
```

方法2: 使用命令行参数
```python
import sys
sys.argv = ['pnl_training_kaggle.py', '--epochs', '20', '--batch_size', '64', '--timeframe', '1h']
main()
```

方法3: 修改配置类 (不推荐)
```python
config.NUM_EPOCHS = 20
config.BATCH_SIZE = 64
config.TIMEFRAME = '1h'
main()
```

支持的参数:
- epochs: 训练轮数 (默认: 8)
- batch_size: 批处理大小 (默认: 16) 
- timeframe: 时间框架 ('1h', '4h', '6h', '12h', '24h')
- learning_rate: 学习率 (默认: 4e-5)
- hidden_size: 隐藏层大小 (256, 512, 768)
- num_layers: BERT层数 (4, 6, 8, 12)

注意: 
- 如果GPU内存不足，减少batch_size和hidden_size
- 1h时间框架使用更严格的PnL阈值 (±1.5%)
- 建议batch_size为8的倍数以获得最佳性能
""" 