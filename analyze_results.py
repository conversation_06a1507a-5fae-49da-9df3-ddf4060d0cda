import pandas as pd
import numpy as np
from datetime import datetime

def analyze_training_data(csv_file="/root/news_train/news_training_samples.csv"):
    """
    Analyze the generated training data
    """
    print("=== Training Data Analysis ===")
    
    try:
        # Load the data
        df = pd.read_csv(csv_file)
        print(f"✅ Loaded {len(df)} training samples")
        
        # Basic statistics
        print(f"\n📊 Basic Statistics:")
        print(f"   Total samples: {len(df)}")
        print(f"   Unique symbols: {df['symbol'].nunique()}")
        print(f"   Date range: {df['news_time_utc0'].min()} to {df['news_time_utc0'].max()}")
        
        # Symbol distribution
        print(f"\n📈 Top 10 symbols by sample count:")
        symbol_counts = df['symbol'].value_counts().head(10)
        for symbol, count in symbol_counts.items():
            print(f"     {symbol}: {count} samples")
        
        # Label statistics (excluding null values)
        valid_labels = df.dropna(subset=['label'])
        if len(valid_labels) > 0:
            print(f"\n🏷️  Label Statistics:")
            print(f"   Valid labels: {len(valid_labels)}/{len(df)} ({len(valid_labels)/len(df)*100:.1f}%)")
            print(f"   Mean label: {valid_labels['label'].mean():.6f}")
            print(f"   Std label: {valid_labels['label'].std():.6f}")
            print(f"   Min label: {valid_labels['label'].min():.6f}")
            print(f"   Max label: {valid_labels['label'].max():.6f}")
        
        # Technical indicators statistics
        print(f"\n📊 Technical Indicators (sample statistics):")
        for col in ['natr', 'mfi', 'rsi', 'btc_natr', 'btc_mfi', 'btc_rsi']:
            if col in df.columns:
                valid_data = df.dropna(subset=[col])
                if len(valid_data) > 0:
                    print(f"   {col}: mean={valid_data[col].mean():.4f}, std={valid_data[col].std():.4f}")
        
        # Time analysis
        print(f"\n⏰ Time Analysis:")
        df['news_hour'] = pd.to_datetime(df['news_time_utc0']).dt.hour
        hourly_dist = df['news_hour'].value_counts().sort_index()
        print(f"   Most active hours (UTC):")
        for hour, count in hourly_dist.head(5).items():
            print(f"     {hour:02d}:00: {count} samples")
        
        # Sample some news titles
        print(f"\n📰 Sample News Titles:")
        for i, title in enumerate(df['news_title'].head(5)):
            print(f"   {i+1}. {title[:80]}...")
        
        # Check for missing data
        print(f"\n❓ Missing Data Analysis:")
        missing_counts = df.isnull().sum()
        for col, count in missing_counts.items():
            if count > 0:
                print(f"   {col}: {count}/{len(df)} ({count/len(df)*100:.1f}%) missing")
        
        return df
        
    except Exception as e:
        print(f"❌ Error analyzing data: {e}")
        return None

def main():
    """
    Main analysis function
    """
    print("🔍 Analyzing News-Label Training Data")
    print("=" * 50)
    
    df = analyze_training_data()
    
    if df is not None:
        print("\n✅ Analysis completed successfully!")
        print(f"📁 Data saved at: /root/news_train/news_training_samples.csv")
        print(f"📊 Ready for machine learning model training!")
    else:
        print("\n❌ Analysis failed!")

if __name__ == "__main__":
    main()