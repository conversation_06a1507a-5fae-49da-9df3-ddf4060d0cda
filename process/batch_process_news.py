#!/usr/bin/env python3
"""
批量处理新闻文件生成训练样本

使用方法：
python process/batch_process_news.py

功能：
- 批量处理filter_news文件夹下的所有新闻文件
- 生成完整的训练样本数据集
- 支持指定日期范围处理

作者：AI Assistant
日期：2025-07-26
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from create_news_training_samples import (
    get_available_symbols, 
    process_news_file, 
    save_samples_to_csv
)
import glob
from datetime import datetime


def process_date_range(start_date=None, end_date=None):
    """
    处理指定日期范围的新闻文件
    Args:
        start_date: 开始日期，格式 "20250301"
        end_date: 结束日期，格式 "20250331"
    """
    print("=== 批量新闻训练样本生成器 ===")
    
    # 获取可用的symbols
    available_symbols = get_available_symbols()
    print(f"📋 可用symbols: {len(available_symbols)}")
    
    # 构建文件模式
    if start_date and end_date:
        print(f"📅 处理日期范围: {start_date} - {end_date}")
        # 这里简化处理，实际可以更精确地过滤日期
        file_pattern = "filter_news/panews_flash_*.csv"
    else:
        file_pattern = "filter_news/panews_flash_*.csv"
    
    # 获取所有新闻文件
    news_files = glob.glob(file_pattern)
    news_files.sort()
    
    # 如果指定了日期范围，进行过滤
    if start_date or end_date:
        filtered_files = []
        for file_path in news_files:
            filename = os.path.basename(file_path)
            # 提取日期部分，例如从 "panews_flash_20250301.csv" 提取 "20250301"
            if "panews_flash_" in filename:
                date_str = filename.replace("panews_flash_", "").replace(".csv", "")
                if len(date_str) == 8:  # YYYYMMDD格式
                    if start_date and date_str < start_date:
                        continue
                    if end_date and date_str > end_date:
                        continue
                    filtered_files.append(file_path)
        news_files = filtered_files
    
    if not news_files:
        print(f"❌ 没有找到匹配的文件")
        return
    
    print(f"📁 找到 {len(news_files)} 个新闻文件")
    
    all_samples = []
    processed_files = 0
    
    for i, news_file in enumerate(news_files, 1):
        print(f"\n[{i}/{len(news_files)}] 处理文件: {os.path.basename(news_file)}")
        
        try:
            samples = process_news_file(news_file, available_symbols)
            if samples:
                all_samples.extend(samples)
                processed_files += 1
                print(f"✅ 生成 {len(samples)} 个样本")
            else:
                print("⚠️  没有生成样本")
        except Exception as e:
            print(f"❌ 处理文件错误: {e}")
            continue
    
    if all_samples:
        # 生成输出文件名
        if start_date and end_date:
            output_file = f"process/news_training_samples_{start_date}_{end_date}.csv"
        elif start_date:
            output_file = f"process/news_training_samples_from_{start_date}.csv"
        else:
            output_file = "process/news_training_samples_all.csv"
        
        save_samples_to_csv(all_samples, output_file)
        
        print(f"\n🎉 批量处理完成:")
        print(f"📁 成功处理文件数: {processed_files}/{len(news_files)}")
        print(f"📊 总样本数: {len(all_samples)}")
        print(f"💾 输出文件: {output_file}")
        
        # 显示symbol分布
        symbol_counts = {}
        for sample in all_samples:
            symbol = sample.get('symbol', 'Unknown')
            symbol_counts[symbol] = symbol_counts.get(symbol, 0) + 1
        
        print(f"\n📈 Top 10 symbols样本数:")
        sorted_symbols = sorted(symbol_counts.items(), key=lambda x: x[1], reverse=True)
        for symbol, count in sorted_symbols[:10]:
            print(f"  {symbol}: {count}")
            
    else:
        print("❌ 没有生成任何样本")


def main():
    """
    主函数
    """
    import argparse
    
    parser = argparse.ArgumentParser(description='批量处理新闻文件生成训练样本')
    parser.add_argument('--start-date', type=str, help='开始日期 (YYYYMMDD格式)')
    parser.add_argument('--end-date', type=str, help='结束日期 (YYYYMMDD格式)')
    parser.add_argument('--sample', action='store_true', help='仅处理少量文件作为示例')
    
    args = parser.parse_args()
    
    if args.sample:
        # 仅处理3月份的几个文件作为示例
        print("🧪 示例模式：处理3月份前几个文件")
        process_date_range("20250301", "20250305")
    else:
        process_date_range(args.start_date, args.end_date)


if __name__ == "__main__":
    main()
