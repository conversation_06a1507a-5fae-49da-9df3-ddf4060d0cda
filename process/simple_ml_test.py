#!/usr/bin/env python3
"""
简化的机器学习测试脚本
"""

import pandas as pd
import numpy as np

def main():
    print("=== 简化ML测试 ===")
    
    try:
        # 1. 测试数据加载
        print("1. 加载数据...")
        df = pd.read_csv('news_training_samples_complete_all.csv')
        print(f"   数据形状: {df.shape}")
        
        # 2. 检查数据
        print("2. 检查数据...")
        print(f"   标题列样本: {df['news_title'].iloc[0][:50]}...")
        print(f"   标签列统计: min={df['label'].min():.4f}, max={df['label'].max():.4f}")
        print(f"   非空标签数量: {df['label'].notna().sum()}")
        
        # 3. 测试文本处理器
        print("3. 测试文本处理器...")
        try:
            from crypto_text_processor import CryptoNewsTextProcessor
            processor = CryptoNewsTextProcessor()
            
            sample_text = df['news_title'].iloc[0]
            processed = processor.process(sample_text)
            print(f"   原文: {sample_text}")
            print(f"   处理后: {processed}")
            
        except Exception as e:
            print(f"   文本处理器错误: {e}")
        
        # 4. 测试sklearn
        print("4. 测试sklearn...")
        try:
            from sklearn.feature_extraction.text import TfidfVectorizer
            from sklearn.ensemble import RandomForestClassifier
            from sklearn.model_selection import train_test_split
            
            # 简单的特征提取和分类测试
            texts = df['news_title'].fillna('').head(100)
            labels = df['label'].head(100)
            
            # 移除NaN
            valid_idx = labels.notna()
            texts = texts[valid_idx]
            labels = labels[valid_idx]
            
            if len(texts) > 10:
                # 创建简单的分类标签（正负）
                binary_labels = (labels > labels.median()).astype(int)
                
                # TF-IDF
                vectorizer = TfidfVectorizer(max_features=100)
                X = vectorizer.fit_transform(texts)
                
                # 分割数据
                X_train, X_test, y_train, y_test = train_test_split(
                    X, binary_labels, test_size=0.3, random_state=42
                )
                
                # 训练模型
                model = RandomForestClassifier(n_estimators=10, random_state=42)
                model.fit(X_train, y_train)
                
                # 预测
                accuracy = model.score(X_test, y_test)
                print(f"   简单分类准确率: {accuracy:.4f}")
                
            else:
                print("   数据太少，跳过分类测试")
                
        except Exception as e:
            print(f"   sklearn错误: {e}")
            import traceback
            traceback.print_exc()
        
        # 5. 创建收益率分类
        print("5. 创建收益率分类...")
        valid_labels = df['label'].dropna()
        if len(valid_labels) > 0:
            quantiles = np.percentile(valid_labels, [20, 40, 60, 80])
            print(f"   分位数: {quantiles}")
            
            def categorize_return(ret):
                if ret <= quantiles[0]:
                    return 0  # 大跌
                elif ret <= quantiles[1]:
                    return 1  # 小跌
                elif ret <= quantiles[2]:
                    return 2  # 平稳
                elif ret <= quantiles[3]:
                    return 3  # 小涨
                else:
                    return 4  # 大涨
            
            categories = valid_labels.apply(categorize_return)
            category_counts = categories.value_counts().sort_index()
            category_names = ['大跌', '小跌', '平稳', '小涨', '大涨']
            
            print("   分类分布:")
            for i, count in category_counts.items():
                print(f"     {i} ({category_names[i]}): {count} 条")
        
        print("\n✅ 所有测试完成！")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
