import polars as pl
import numpy as np
from datetime import datetime, timedelta, timezone
import os
import glob
from pathlib import Path
import requests
import pandas as pd
import time

def average_true_range_expr(
        period, 
        high_col='high',
        low_col='low',
        close_col='close',
        ma_type='simple', 
        use_previous=True, 
        value_floor=0
):
    if use_previous:
        tr = pl.max_horizontal(
            pl.col(high_col) - pl.col(low_col),
            pl.col(high_col) - pl.col(close_col).shift(),
            pl.col(close_col).shift() - pl.col(low_col)
        ).abs()
    else:
        tr = pl.col(high_col) - pl.col(low_col)
    
    if ma_type.lower() == 'simple':
        atr = tr.rolling_mean(period)
    elif ma_type.lower() == 'exponential':
        atr = tr.ewm_mean(span=period)
    else:
        raise ValueError("Unsupported moving average type")
    
    return pl.max_horizontal(atr, pl.lit(value_floor))


def rsi_expr(col_name: str, period: int) -> pl.Expr:
    """
    返回一个Polars表达式，用于计算相对强弱指数（RSI）。

    Parameters:
    col_name (str): 需要计算RSI的列名。
    period (int): RSI的周期。

    Returns:
    pl.Expr: 计算RSI的Polars表达式。
    """
    # 计算价格变化
    price_change = pl.col(col_name).diff()

    # 分离gain和loss
    gain = price_change.clip(lower_bound=0)
    loss = -price_change.clip(upper_bound=0)

   
    # 计算average_gain和average_loss
    average_gain = gain.ewm_mean(span=period, adjust=False)
    average_loss = loss.ewm_mean(span=period, adjust=False)

    # 处理average_loss为零的情况
    rs = pl.when(average_loss == 0).then(0).otherwise(average_gain / average_loss)

    # 计算RSI
    rsi = 1.0 - (1.0 / (1.0 + rs))
    rsi = rsi.fill_null(strategy='forward')  # 前几个值可能是NaN，向前填充

    return rsi



def mfi_expr(high: pl.Expr, low: pl.Expr, close: pl.Expr, volume: pl.Expr, period: int = 14, alias='mfi') -> pl.Expr:
    """
    Calculate Money Flow Index using Polars expressions.
    
    Parameters:
    -----------
    high : pl.Expr
        Expression for the high price.
    low : pl.Expr
        Expression for the low price.
    close : pl.Expr
        Expression for the close price.
    volume : pl.Expr
        Expression for the volume.
    period : int, optional
        The rolling window period for the indicator (default is 14).
    
    Returns:
    --------
    pl.Expr
        An expression for calculating the MFI values.
    """
    
    typical_price = ((high + low + close) / 3).alias('typical_price')
    raw_money_flow = (typical_price * volume).alias('raw_money_flow')
    price_change = typical_price.diff().alias('price_change')
    
    positive_flow = pl.when(price_change > 0).then(raw_money_flow).otherwise(0).alias('positive_flow')
    
    negative_flow = pl.when(price_change < 0).then(raw_money_flow).otherwise(0).alias('negative_flow')
    
    positive_flow_sum = positive_flow.rolling_sum(period).alias('positive_flow_sum')
    negative_flow_sum = negative_flow.rolling_sum(period).alias('negative_flow_sum')
    
    mfi = (100. - (100. / (1. + positive_flow_sum / negative_flow_sum))).alias(alias)
    
    return mfi
    
def calc_change_since_pivot(current,last_pivot):
    if(last_pivot == 0): last_pivot = 1 ** (-100) # avoid division by 0
    perc_change_since_pivot = (current - last_pivot) / abs(last_pivot)
    return perc_change_since_pivot


def calc_relative_change_since_pivot(
    current_A,
    current_B,
    last_pivot_A,
    last_pivot_B
):
    A_change_since_pivot = calc_change_since_pivot(current_A, last_pivot_A)
    B_change_since_pivot = calc_change_since_pivot(current_B, last_pivot_B)
    return A_change_since_pivot - B_change_since_pivot 

def get_zigzag(idx, row, taip=None):
    return {
        "timestamp": row[0],
        "A_value":row[1],
        "B_value":row[2],
        "value": row[3],
        "idx":idx,
        "type": taip,
    }

def create_label(
    df,
    threshold = 0.01,
    cut_label = False 
):
    zigzags = []
    for idx,item in enumerate(df.select(["timestamp","A_value","B_value","relative_value"]).iter_rows()):
        is_starting = (idx == 0)
        if is_starting:
            zigzags.append(get_zigzag(idx,item)) #idx,row,  tap 
            continue  

        is_first_line = (len(zigzags) == 1) 
        if is_first_line:
            perc_change_since_pivot = calc_relative_change_since_pivot(item[1],item[2],zigzags[-1]["A_value"],zigzags[-1]["B_value"])
            if abs(perc_change_since_pivot) >= threshold:
                if perc_change_since_pivot > 0:
                    zigzags.append(get_zigzag(idx, item,"Peak"))
                    zigzags[0]["type"] = "Through"
                else:
                    zigzags.append(get_zigzag(idx, item, "Trough"))
                    zigzags[0]["type"] = "Peak" 
            continue 
        is_through = zigzags[-2]["value"] > zigzags[-1]["value"]
        is_ending = (idx == df.shape[0] - 1)
        last_pivot = float(zigzags[-1]["value"])
        # based on last pivot type, look for reversal or continuation
        if(is_through):
            perc_change_since_pivot = calc_relative_change_since_pivot(item[1],item[2],zigzags[-1]["A_value"],zigzags[-1]["B_value"])
            is_reversing = (perc_change_since_pivot >= threshold) or is_ending
            is_continuing = item[1] <= last_pivot
            if (is_continuing): 
                zigzags[-1] = get_zigzag(idx,item, "Trough")
            elif (is_reversing): 
                zigzags.append(get_zigzag(idx,item, "Peak"))
        else:
            perc_change_since_pivot = calc_relative_change_since_pivot(item[1],item[2],zigzags[-1]["A_value"],zigzags[-1]["B_value"])
            is_reversing = (perc_change_since_pivot <= -threshold) or is_ending
            is_continuing = item[1] >= last_pivot
            if(is_continuing): 
                zigzags[-1] = get_zigzag(idx,item, "Peak")
            elif (is_reversing): 
                zigzags.append(get_zigzag(idx,item, "Trough"))

    zigzags = pl.DataFrame(zigzags)
    
    # Ensure datetime precision matches between dataframes
    # Cast zigzag datetime to match the main dataframe datetime precision
    main_dt_dtype = df.select("timestamp").dtypes[0]
    zigzags = zigzags.with_columns([
        pl.col("timestamp").cast(main_dt_dtype)
    ])
    
    zigzags = zigzags.select([
        pl.all(),
        pl.col("timestamp").shift(-1).alias("event_ends"),
        pl.col("A_value").shift(-1).alias("prevext_A_value"),
        pl.col("B_value").shift(-1).alias("prevext_B_value")
    ])
    if zigzags.shape[0] < 2:
        print(f"⚠️  Insufficient zigzag points: {zigzags.shape[0]}, need at least 2")
        return None 
    df = df.join(zigzags, on = "timestamp", how = "left")
    df = df.select(
        [pl.col(item).fill_null(strategy = "forward") if item in ["prevext_A_value","prevext_B_value","event_ends"] else pl.col(item) for item in df.columns]
    )
    df = df.select(
        [pl.all(), ((pl.col("prevext_A_value")/pl.col("A_value")) - (pl.col("prevext_B_value")/pl.col("B_value"))).alias("label")]
    )
    correct_label = [] 
    event_ends = [] 

    ## drop the front data because zigzag is meanless on these data 
    df = df.filter((pl.col("timestamp")>=zigzags[1,"timestamp"]))

    df = df.select(pl.all().exclude(['value', 'type', 'idx', 'A_value', 'B_value', 'prevext_A_value', 'prevext_B_value']))
    df = df.with_columns( [pl.col("timestamp").alias("event_starts")])
    if cut_label:
        label_array = df[:,"label"].to_numpy()
        df = df.select([
            pl.all().exclude("label"),
            (pl.when(pl.col("label")>label_array.mean() +5.0*label_array.std())
             .then(label_array.mean() +5.0*label_array.std())
             .otherwise(pl.col("label"))).alias("label"),
        ])
        df = df.select([
            pl.all().exclude("label"),
            (pl.when(pl.col("label")< label_array.mean() - 5.0*label_array.std())
             .then(label_array.mean() - 5.0*label_array.std())
             .otherwise(pl.col("label"))).alias("label"),
        ])
    df = df.select(
            [pl.all(), pl.arange(0, pl.count()).alias("count_index")]
        )

    return df 

def convert_utc8_to_utc0(time_str, date_str):
    """
    Convert UTC+8 time to UTC+0
    Args:
        time_str: Time string like "23:45"
        date_str: Date string like "1月1日"
    Returns:
        UTC datetime
    """
    try:
        # Parse Chinese date format
        month_day = date_str.replace("月", "-").replace("日", "")
        if "-" in month_day:
            month, day = month_day.split("-")
            # Assume current year (2025 based on the data)
            year = 2025
            
            # Create datetime in UTC+8
            dt_utc8 = datetime(year, int(month), int(day))
            
            # Parse time
            if ":" in time_str:
                hour, minute = time_str.split(":")
                dt_utc8 = dt_utc8.replace(hour=int(hour), minute=int(minute))
            
            # Convert UTC+8 to UTC+0 (subtract 8 hours)
            dt_utc0 = dt_utc8 - timedelta(hours=8)
            
            return dt_utc0
    except Exception as e:
        print(f"Error converting time {time_str} {date_str}: {e}")
        return None



def create_relative_label_for_symbol(symbol, threshold=0.03, cut_label=True):
    """
    Create relative labels for a single symbol against BTC

    Args:
        symbol: Symbol name (e.g., "ETHUSDT")
        threshold: Zigzag threshold for relative price movements
        cut_label: Whether to cut extreme labels

    Returns:
        DataFrame with relative labels, or None if failed
    """
    print(f"🔄 Computing relative labels for {symbol}")

    # Load symbol data
    symbol_file = f"data/{symbol}_5m.parquet"
    if not os.path.exists(symbol_file):
        print(f"❌ Symbol data not found: {symbol_file}")
        return None

    # Load BTC data
    btc_file = "data/BTCUSDT_5m.parquet"
    if not os.path.exists(btc_file):
        print(f"❌ BTC data not found: {btc_file}")
        return None

    try:
        # Load data
        symbol_df = pl.read_parquet(symbol_file)
        btc_df = pl.read_parquet(btc_file)

        print(f"📊 Loaded {symbol_df.shape[0]} records for {symbol}")
        print(f"📊 Loaded {btc_df.shape[0]} records for BTC")

        # Join BTC data
        btc_features = btc_df.select([
            pl.col("timestamp"),
            pl.col("high").alias("btc_high"),
            pl.col("low").alias("btc_low"),
            pl.col("close").alias("btc_close"),
            pl.col("volume").alias("btc_volume")
        ])
        
        relative_df = symbol_df.join(btc_features, on="timestamp", how="inner")

        # Create relative price series
        select_df = relative_df.with_columns([
            pl.col("timestamp"),
            pl.col("close").alias("A_value"),
            pl.col("btc_close").alias("B_value"),
            (pl.col("close") / pl.col("btc_close")).log().alias("relative_value")
        ])

        # Define classify period for technical indicators
        classify_period = 14

        # Compute technical indicators before creating labels
        print(f"🧮 Computing technical indicators (NATR, MFI, RSI) with period={classify_period}")
        relative_df = relative_df.with_columns([
            # Symbol indicators
            (average_true_range_expr(period=classify_period) / pl.col('close')).alias('natr'),
            mfi_expr(pl.col('high'), pl.col('low'), pl.col('close'), pl.col('volume'), period=classify_period, alias='mfi'),
            rsi_expr('close', period=classify_period).alias('rsi'),

            # BTC indicators for comparison
            (average_true_range_expr(period=classify_period, high_col='btc_high', low_col='btc_low', close_col='btc_close') / pl.col('btc_close')).alias('btc_natr'),
            mfi_expr(pl.col('btc_high'), pl.col('btc_low'), pl.col('btc_close'), pl.col('btc_volume'), period=classify_period, alias='btc_mfi'),
            rsi_expr('btc_close', period=classify_period).alias('btc_rsi')
        ])

        print(f"✅ Technical indicators computed successfully")
        # # Use create_label function to calculate relative returns
        # print(f"🧮 Computing relative labels for {symbol} with threshold={threshold}")
        labeled_df = create_label(
            select_df,
            threshold=threshold,
            cut_label=cut_label
        )
        print(labeled_df)

        if labeled_df is None or labeled_df.shape[0] == 0:
            print(f"❌ Label computation failed for {symbol}")
            return None

        # # Merge back with original data
        result_df = relative_df.join(
            labeled_df.select(["timestamp", "label", "event_starts", "event_ends"]),
            left_on="timestamp",
            right_on="timestamp",
            how="left"
        )
    
        #compute techno indicators

        print(f"✅ Successfully computed relative labels for {symbol}")
        print(f"📊 Generated {result_df.shape[0]} records with relative labels")


        valid_labels = result_df.filter(pl.col("label").is_not_null())
        if valid_labels.shape[0] > 0:
            label_stats = valid_labels.select([
                pl.col("label").mean().alias("mean_label"),
                pl.col("label").std().alias("std_label"),
                pl.col("label").min().alias("min_label"),
                pl.col("label").max().alias("max_label"),
                pl.col("label").count().alias("valid_count")
            ])
            print(f"📈 Label statistics: {label_stats.to_dicts()[0]}")

        return result_df

    except Exception as e:
        print(f"❌ Error computing relative labels for {symbol}: {e}")
        import traceback
        traceback.print_exc()
        return None

def save_relative_labels(symbol, labeled_df):
    """
    Save relative labeled data to file
    """
    if labeled_df is None:
        return False

    output_file = f"data/{symbol}_5m_relative_labeled.parquet"
    try:
        labeled_df.write_parquet(output_file)
        print(f" Saved relative labeled data to {output_file}")
        return True
    except Exception as e:
        print(f"❌ Error saving relative labeled data for {symbol}: {e}")
        return False

def compute_all_relative_labels(threshold=0.03):
    """
    Compute relative labels for all available symbols against BTC
    """
    print("=== Computing relative labels for all symbols against BTC ===")

    # Find all price data files (exclude BTC itself)
    price_files = glob.glob("data/*_5m.parquet")
    symbols = [os.path.basename(f).replace("_5m.parquet", "") for f in price_files]
    symbols = [s for s in symbols if s != "BTCUSDT"]  # Exclude BTC itself

    print(f"📋 Found {len(symbols)} symbols to process (excluding BTC)")

    success_count = 0
    failed_symbols = []

    for i, symbol in enumerate(symbols, 1):
        print(f"[{i}/{len(symbols)}] Processing {symbol}...")

        labeled_df = create_relative_label_for_symbol(symbol, threshold=threshold)
        if labeled_df is not None:
            if save_relative_labels(symbol, labeled_df):
                success_count += 1
            else:
                failed_symbols.append(symbol)
        else:
            failed_symbols.append(symbol)

    print(f"Summary:")
    print(f"✅ Successfully processed: {success_count}/{len(symbols)} symbols")
    if failed_symbols:
        print(f"❌ Failed symbols: {failed_symbols}")

    return success_count, failed_symbols


def test_for_relative_label_compute():
    """
    Test function to compute relative labels for a specific symbol
    """
    symbol = "1000PEPEUSDT"
    threshold = 0.03

    print(f"Testing relative label computation for {symbol} with threshold={threshold}")
    labeled_df = create_relative_label_for_symbol(symbol, threshold=threshold)
    #labeled_df.write_parquet('test.parquet')
    print(labeled_df[450])
    if labeled_df is not None:
        print(f"✅ Successfully computed relative labels for {symbol}")
        print(f"📊 Generated {labeled_df.shape[0]} records with relative labels")
    else:
        print(f"❌ Failed to compute relative labels for {symbol}")



def main():
    """
    Main function - compute relative labels for all symbols
    """
    print("Starting relative labeling process...")
    print("🎯 Computing relative labels against BTC for all symbols...")

    # Compute relative labels with default threshold
    success_count, failed_symbols = compute_all_relative_labels(threshold=0.03)

    if success_count > 0:
        print(f"🎉 Successfully computed relative labels for {success_count} symbols!")
        print("📁 Files saved with pattern: data/SYMBOL_5m_relative_labeled.parquet")
    else:
        print("❌ No symbols were successfully processed")


if __name__ == "__main__":
    #test_for_relative_label_compute()
    main()
