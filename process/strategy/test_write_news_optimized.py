#!/usr/bin/env python
"""
测试优化后的write_news_to_catalog.py功能
"""

import sys
import tempfile
import shutil
from pathlib import Path
import pandas as pd
import logging

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from write_news_to_catalog import (
    split_news_by_symbols, 
    validate_timestamp_ns,
    process_csv_file
)
from news_data_type import NewsData
from nautilus_trader.persistence.catalog import ParquetDataCatalog

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_validate_timestamp_ns():
    """测试时间戳验证函数"""
    print("测试时间戳验证...")
    
    # 测试有效时间戳 (2025年1月1日)
    valid_ts = 1735689600000000000  # 2025-01-01 00:00:00 UTC in nanoseconds
    assert validate_timestamp_ns(valid_ts), "有效时间戳应该通过验证"
    
    # 测试无效时间戳
    assert not validate_timestamp_ns(-1), "负数时间戳应该失败"
    assert not validate_timestamp_ns(0), "零时间戳应该失败"
    assert not validate_timestamp_ns(1000000000000000000), "过早时间戳应该失败"
    
    print("✓ 时间戳验证测试通过")

def test_split_news_by_symbols():
    """测试新闻拆分函数"""
    print("测试新闻拆分...")
    
    # 创建测试数据
    test_row = pd.Series({
        'title': '测试新闻标题',
        'content': '测试新闻内容',
        'time': '10:00',
        'date': '1月1日',
        'url': 'https://test.com',
        'is_featured': True,
        'scraped_at': '2025-01-01 10:00:00',
        'matched_symbols': 'BTC-PERP.BINANCE,ETH-PERP.BINANCE',
        'timestamp_ns': 1735689600000000000
    })
    
    # 测试拆分
    news_records = split_news_by_symbols(test_row)
    
    assert len(news_records) == 2, f"应该拆分为2条记录，实际得到{len(news_records)}条"
    assert news_records[0]['ts_event'] == 1735689600000000000, "时间戳应该正确"
    assert news_records[0]['ts_init'] == 1735689600000000000, "时间戳应该正确"
    
    print("✓ 新闻拆分测试通过")

def test_process_sample_file():
    """测试处理示例文件"""
    print("测试处理示例文件...")
    
    # 检查示例文件是否存在
    sample_file = Path("../filter_news/panews_flash_20250101_filtered.csv")
    if not sample_file.exists():
        print(f"⚠ 示例文件不存在: {sample_file}")
        return
    
    # 创建临时catalog目录
    with tempfile.TemporaryDirectory() as temp_dir:
        catalog_path = Path(temp_dir) / "catalog"
        catalog_path.mkdir()
        
        # 创建catalog
        catalog = ParquetDataCatalog(str(catalog_path))
        
        # 处理文件
        try:
            record_count = process_csv_file(sample_file, catalog)
            print(f"✓ 成功处理了 {record_count} 条记录")
            
            if record_count > 0:
                # 验证数据是否写入catalog
                try:
                    data = catalog.read(NewsData)
                    print(f"✓ Catalog中包含 {len(data)} 条NewsData记录")
                except Exception as e:
                    print(f"⚠ 读取catalog数据时出错: {e}")
                    
        except Exception as e:
            print(f"✗ 处理文件时出错: {e}")

def main():
    """运行所有测试"""
    print("开始测试优化后的write_news_to_catalog功能...\n")
    
    try:
        test_validate_timestamp_ns()
        test_split_news_by_symbols()
        test_process_sample_file()
        
        print("\n✓ 所有测试完成")
        
    except Exception as e:
        print(f"\n✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
