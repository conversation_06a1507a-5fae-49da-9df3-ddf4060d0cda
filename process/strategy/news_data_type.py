import pyarrow as pa
from nautilus_trader.core.data import Data
from nautilus_trader.model.identifiers import InstrumentId
from nautilus_trader.serialization.base import register_serializable_type
from nautilus_trader.serialization.arrow.serializer import register_arrow

class NewsData(Data):
    def __init__(
        self,
        instrument_id: InstrumentId,
        title: str,
        content: str,
        time: str,
        date: str,
        url: str,
        is_featured: bool,
        scraped_at: str,
        ts_event: int,
        ts_init: int,
        **kwargs
    ):
        self.instrument_id = instrument_id
        self.title = title
        self.content = content
        self.time = time
        self.date = date
        self.url = url
        self.is_featured = is_featured
        self.scraped_at = scraped_at
        self._ts_event = ts_event
        self._ts_init = ts_init

    def __repr__(self) -> str:
        return (
            f"{type(self).__name__}("
            f"instrument_id={self.instrument_id}, "
            f"title='{self.title[:50]}...', "
            f"ts_event={self.ts_event}, "
            f"ts_init={self.ts_init})"
        )

    @property
    def ts_event(self) -> int:
        return self._ts_event

    @property
    def ts_init(self) -> int:
        return self._ts_init

    @staticmethod
    def to_dict(obj: "NewsData") -> dict:
        return {
            "instrument_id": obj.instrument_id.value,
            "title": obj.title,
            "content": obj.content,
            "time": obj.time,
            "date": obj.date,
            "url": obj.url,
            "is_featured": obj.is_featured,
            "scraped_at": obj.scraped_at,
            "ts_event": obj.ts_event,
            "ts_init": obj.ts_init,
        }

    @classmethod
    def from_dict(cls, values: dict) -> "NewsData":
        instrument_id = InstrumentId.from_str(values["instrument_id"])
        return cls(
            instrument_id=instrument_id,
            title=values["title"],
            content=values["content"],
            time=values["time"],
            date=values["date"],
            url=values["url"],
            is_featured=values["is_featured"],
            scraped_at=values["scraped_at"],
            ts_event=values["ts_event"],
            ts_init=values["ts_init"],
        )

    @classmethod
    def schema(cls):
        fields = [
            pa.field("instrument_id", pa.string(), False),
            pa.field("title", pa.string(), False),
            pa.field("content", pa.string(), False),
            pa.field("time", pa.string(), False),
            pa.field("date", pa.string(), False),
            pa.field("url", pa.string(), False),
            pa.field("is_featured", pa.bool_(), False),
            pa.field("scraped_at", pa.string(), False),
            pa.field("ts_event", pa.uint64(), False),
            pa.field("ts_init", pa.uint64(), False),
        ]
        return pa.schema(fields)

    @staticmethod
    def to_catalog(obj: "NewsData"):
        return pa.RecordBatch.from_pylist([NewsData.to_dict(obj)], schema=NewsData.schema())

    @classmethod
    def from_catalog(cls, table):
        return [NewsData.from_dict(d) for d in table.to_pylist()]

# 注册序列化
register_serializable_type(NewsData, NewsData.to_dict, NewsData.from_dict)
register_arrow(NewsData, NewsData.schema(), NewsData.to_catalog, NewsData.from_catalog) 