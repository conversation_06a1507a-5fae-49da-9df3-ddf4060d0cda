"""
加密货币新闻文本轻量处理器
实现基础清洗、标准化处理、特殊处理和噪声过滤
"""

import re
import unicodedata
import json
import os
from typing import Dict, Set, Tuple, List, Optional

class CryptoNewsTextProcessor:
    """加密货币新闻文本轻量处理器"""
    
    def __init__(self):
        self.stop_words = self._load_stop_words()
    
    def _load_stop_words(self) -> Set[str]:
        """加载停用词"""
        return {
            # 中文停用词
            "据悉", "据报道", "消息称", "有消息称", "据了解", "据消息", "据称",
            "消息人士称", "知情人士透露", "市场消息", "行业消息", "内部人士称",
            "最新消息", "最新报道", "最新动态", "最新进展", "最新情况","的"
            
            # 英文引用停用词
            "according to", "reportedly", "it is reported", "sources say",
            "insider said", "as per", "as reported by", "as mentioned by",
            "as stated by", "as noted by", "as indicated by", "as suggested by",
            
            # 常见英文停用词
            "the", "a", "an", "and", "or", "but", "if", "because", "as", "until", "while",
            "of", "at", "by", "for", "with", "about", "against", "between", "into", "through",
            "during", "before", "after", "above", "below", "to", "from", "up", "down", "in",
            "out", "on", "off", "over", "under", "again", "further", "then", "once", "here",
            "there", "when", "where", "why", "how", "all", "any", "both", "each", "few",
            "more", "most", "other", "some", "such", "no", "nor", "not", "only", "own",
            "same", "so", "than", "too", "very", "s", "t", "can", "will", "just", "don",
            "should", "now", "d", "ll", "m", "o", "re", "ve", "y", "ain", "aren", "couldn",
            "didn", "doesn", "hadn", "hasn", "haven", "isn", "ma", "mightn", "mustn", "needn",
            "shan", "shouldn", "wasn", "weren", "won", "wouldn", "i", "me", "my", "myself",
            "we", "our", "ours", "ourselves", "you", "your", "yours", "yourself", "yourselves",
            "he", "him", "his", "himself", "she", "her", "hers", "herself", "it", "its", "itself",
            "they", "them", "their", "theirs", "themselves", "what", "which", "who", "whom",
            "this", "that", "these", "those", "am", "is", "are", "was", "were", "be", "been",
            "being", "have", "has", "had", "having", "do", "does", "did", "doing", "would",
            "could", "should", "shall", "might", "must"
        }
    
    def basic_clean(self, text: str) -> str:
        """基础清洗"""
        if not text:
            return ""
        
        # 移除HTML标签
        text = re.sub(r'<[^>]+>', ' ', text)
        
        # 处理空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 移除URL
        text = re.sub(r'https?://\S+|www\.\S+', ' ', text)
        
        # 移除邮箱
        text = re.sub(r'\S+@\S+', ' ', text)
        
        # 移除特殊字符，但保留中文、英文、数字和所有标点符号
        # 包括: .,;:!?()[]{}'"-%$/\|@#^&*_+=<>~`
        text = re.sub(r'[^\u4e00-\u9fff\w\s.,;:!?()[\]{}\'\"\/\\\-\+\=\$\%\^\&\*\@\#\_\<\>\~\`]', ' ', text)
        
        return text.strip()
    
    def normalize_text(self, text: str) -> str:
        """文本标准化"""
        if not text:
            return ""
        
        # 统一为NFKC形式（全角转半角等）
        text = unicodedata.normalize('NFKC', text)
        
        # 英文统一为小写
        # 使用正则表达式只转换英文字符，保留中文和其他字符不变
        def lowercase_english(match):
            return match.group(0).lower()
        
        text = re.sub(r'[a-zA-Z]+', lowercase_english, text)
        
        # 数字标准化（移除千位分隔符）
        text = re.sub(r'(\d),(\d)', r'\1\2', text)
        
        # 标点符号标准化
        text = text.replace('，', ', ')
        text = text.replace('。', '. ')
        text = text.replace('；', '; ')
        text = text.replace('：', ': ')
        text = text.replace('"', '"')
        text = text.replace('"', '"')
        text = text.replace(''', "'")
        text = text.replace(''', "'")
        
        return text.strip()

    
    def remove_noise(self, text: str) -> str:
        """移除噪声"""
        # 移除停用词，但保留标点符号
        for word in self.stop_words:
            # 使用空格替换停用词，保留标点符号
            text = re.sub(r'\b' + re.escape(word) + r'\b', ' ', text, flags=re.IGNORECASE)
            # 移除多余的空格
            text = re.sub(r'\s+', ' ', text)
        
        # 处理重复内容
        text = re.sub(r'(.{20,}?)\1+', r'\1', text)
        
        return text

    
    def process(self, text: str) -> str:
        """完整的文本处理流程"""
        if not text:
            return ""
        
        # 1. 基础清洗
        text = self.basic_clean(text)
        
        # 2. 标准化处理
        text = self.normalize_text(text)
    
        # 3. 移除噪声
        text = self.remove_noise(text)

        # 4. 最终清理
        text = text.strip()
        text = re.sub(r'\s+', ' ', text)
        
        return text

    def process_batch(self, texts: List[str]) -> List[str]:
        """批量处理文本"""
        return [self.process(text) for text in texts]


# 测试代码
if __name__ == "__main__":
    processor = CryptoNewsTextProcessor()
    
    test_texts = [
        "据报道，比特币(BTC)价格在过去24小时内上涨了5.2%，达到$42,500，创下近期新高。",
        "ETH和SOL分别上涨3.5%和7.8%，市场情绪普遍看好。",
        "Binance交易所宣布将上线新的代币交易对，包括XRP/USDT和ADA/USDT。",
        "According to sources, Bitcoin mining difficulty increased by 4.5% last week.",
        "有消息称，某DeFi协议遭受闪电贷攻击，损失约500万美元。"
    ]
    
    print("原始文本:")
    for i, text in enumerate(test_texts):
        print(f"{i+1}. {text}")
    
    print("\n处理后文本:")
    processed_texts = processor.process_batch(test_texts)
    for i, text in enumerate(processed_texts):
        print(f"{i+1}. {text}")