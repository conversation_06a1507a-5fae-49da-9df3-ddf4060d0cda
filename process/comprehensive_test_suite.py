#!/usr/bin/env python3
"""
全面测试套件：确保 news_instrument_extractor_v2.py 与 news_symbol_matcher.py 逻辑完全一致
"""

import json
import os
import sys
from typing import Dict, List, Tuple, Set
import re

# 添加路径
sys.path.append(os.path.dirname(__file__))

def load_test_data():
    """加载测试数据"""
    try:
        # 加载币安符号
        with open('binance_symbols.json', 'r', encoding='utf-8') as f:
            binance_symbols = json.load(f)
    except FileNotFoundError:
        print("Warning: binance_symbols.json not found")
        binance_symbols = {}
    
    try:
        # 加载别名
        with open('alias_symbols_processed.txt', 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
            cleaned_lines = []
            for line in lines:
                if line.strip().startswith('#'):
                    continue
                if '#' in line:
                    line = line.split('#')[0].rstrip()
                cleaned_lines.append(line)
            cleaned_content = '\n'.join(cleaned_lines)
            crypto_aliases = json.loads(cleaned_content)
    except FileNotFoundError:
        print("Warning: alias_symbols_processed.txt not found")
        crypto_aliases = {}
    
    return binance_symbols, crypto_aliases

def create_test_cases():
    """创建全面的测试用例"""
    test_cases = [
        # 基础测试用例
        {
            'title': 'BTC价格突破50000美元',
            'content': '比特币(BTC)今日价格突破50000美元大关，创下历史新高。',
            'expected_symbols': ['btc', '比特币'],
            'description': '基础BTC测试'
        },
        {
            'title': 'Ethereum升级完成，ETH代币价格上涨',
            'content': '以太坊网络升级顺利完成，ETH代币价格应声上涨。',
            'expected_symbols': ['ethereum', 'eth'],
            'description': '基础ETH测试'
        },
        
        # 短语优先级测试
        {
            'title': 'Portal Ventures投资新项目',
            'content': 'Portal Ventures宣布投资一个新的DeFi项目。',
            'expected_symbols': [],  # Portal应该被识别为短语，不应该单独匹配
            'description': '短语优先级测试 - Portal Ventures'
        },
        {
            'title': 'Binance Coin价格上涨',
            'content': 'Binance Coin (BNB) 价格今日上涨5%。',
            'expected_symbols': ['binance coin', 'bnb'],
            'description': '短语优先级测试 - Binance Coin'
        },
        
        # 特殊模式测试
        {
            'title': 'BTC token空投活动开始',
            'content': 'BTC token空投活动正式开始，持有者可获得奖励。',
            'expected_symbols': ['btc'],
            'description': '特殊模式测试 - BTC token'
        },
        {
            'title': 'ETHUSDT交易对分析',
            'content': 'ETHUSDT交易对技术分析显示上涨趋势。',
            'expected_symbols': ['eth'],
            'description': '特殊模式测试 - ETHUSDT'
        },
        {
            'title': 'BTC U本位合约上线',
            'content': 'BTC U本位永续合约正式上线交易。',
            'expected_symbols': ['btc'],
            'description': '特殊模式测试 - U本位'
        },
        
        # 中文关键词测试
        {
            'title': '狗狗币空投活动，DOGE持有者受益',
            'content': '狗狗币(DOGE)空投活动开始，DOGE持有者将获得免费代币。',
            'expected_symbols': ['狗狗币', 'doge'],
            'description': '中文关键词测试'
        },
        {
            'title': '以太坊网络升级，以太币价格波动',
            'content': '以太坊网络完成重大升级，以太币价格出现波动。',
            'expected_symbols': ['以太坊', '以太币'],
            'description': '中文关键词测试 - 以太坊'
        },
        
        # 边界情况测试
        {
            'title': 'AI项目获得投资',
            'content': 'AI相关项目获得大额投资，市场关注度提升。',
            'expected_symbols': [],  # AI在黑名单中
            'description': '黑名单测试 - AI'
        },
        {
            'title': 'Portal代币价格分析',
            'content': 'Portal代币价格技术分析，显示上涨潜力。',
            'expected_symbols': ['portal'],
            'description': '单词匹配测试 - Portal'
        },
        
        # 复杂场景测试
        {
            'title': 'BTC、ETH、DOGE三大主流币种分析',
            'content': '比特币、以太坊、狗狗币三大主流加密货币价格分析。',
            'expected_symbols': ['btc', 'eth', 'doge', '比特币', '以太坊', '狗狗币'],
            'description': '多符号测试'
        },
        {
            'title': 'Chainlink Oracle网络升级',
            'content': 'Chainlink Oracle网络完成重大升级，LINK代币价格上涨。',
            'expected_symbols': ['chainlink', 'link'],
            'description': '复杂匹配测试'
        },
        
        # 位置冲突测试
        {
            'title': 'Bitcoin Cash价格分析',
            'content': 'Bitcoin Cash (BCH) 价格技术分析报告。',
            'expected_symbols': ['bitcoin cash', 'bch'],
            'description': '位置冲突测试 - Bitcoin Cash'
        }
    ]
    
    return test_cases

def test_symbol_extraction_consistency():
    """测试符号提取的一致性"""
    print("开始全面测试符号提取逻辑一致性...")
    print("=" * 60)
    
    # 加载测试数据
    binance_symbols, crypto_aliases = load_test_data()
    print(f"加载的币安符号数量: {len(binance_symbols)}")
    print(f"加载的别名数量: {len(crypto_aliases)}")
    
    # 创建测试用例
    test_cases = create_test_cases()
    print(f"测试用例数量: {len(test_cases)}")
    
    # 导入两个提取器
    try:
        from news_symbol_matcher import NewsSymbolMatcher
        from news_instrument_extractor_v2 import NewsInstrumentExtractor
        from nautilus_trader.model.identifiers import Venue
        
        # 创建实例
        matcher = NewsSymbolMatcher('binance_symbols.json', 'alias_symbols_processed.txt')
        
        extractor = NewsInstrumentExtractor(
            cache=None,
            venue=Venue("BINANCE"),
            max_symbols=3,
            binance_symbols_path='binance_symbols.json',
            crypto_aliases_path='alias_symbols_processed.txt'
        )
        extractor._set_available_symbols(set(binance_symbols.keys()))
        
        print("\n两个提取器创建成功")
        
    except ImportError as e:
        print(f"导入错误: {e}")
        return False
    
    # 运行测试
    passed_tests = 0
    failed_tests = 0
    
    print("\n开始逐个测试用例...")
    print("-" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {test_case['description']}")
        print(f"标题: {test_case['title']}")
        
        # 使用 news_symbol_matcher 提取
        matcher_symbols = matcher.extract_symbols_from_title(test_case['title'])
        matcher_keywords = set(matcher_symbols.keys())
        
        # 使用 news_instrument_extractor_v2 提取
        extractor_symbols = extractor.extract_symbols_from_original_title(test_case['title'])
        extractor_keywords = set(extractor_symbols.keys())
        
        print(f"Matcher提取: {sorted(matcher_keywords)}")
        print(f"Extractor提取: {sorted(extractor_keywords)}")
        
        # 比较结果
        if matcher_keywords == extractor_keywords:
            print("✅ 测试通过 - 提取结果一致")
            passed_tests += 1
        else:
            print("❌ 测试失败 - 提取结果不一致")
            print(f"  差异: Matcher独有: {matcher_keywords - extractor_keywords}")
            print(f"       Extractor独有: {extractor_keywords - matcher_keywords}")
            failed_tests += 1
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结:")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {failed_tests}")
    print(f"总测试数: {len(test_cases)}")
    print(f"通过率: {passed_tests/len(test_cases)*100:.1f}%")
    
    return failed_tests == 0

if __name__ == "__main__":
    success = test_symbol_extraction_consistency()
    sys.exit(0 if success else 1)
