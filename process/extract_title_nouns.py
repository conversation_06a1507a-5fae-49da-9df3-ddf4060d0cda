#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新闻标题英文名词提取器 - 简化版
只从标题中提取英文名词，每行一个样本
"""

import os
import re
import csv
import glob
import pandas as pd
from collections import Counter
from typing import List, Tuple

class TitleNounExtractor:
    def __init__(self):
        # 常见停用词
        self.stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have',
            'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
            'may', 'might', 'can', 'must', 'shall', 'this', 'that', 'these', 'those',
            'from', 'up', 'down', 'out', 'off', 'over', 'under', 'again', 'further',
            'then', 'once', 'here', 'there', 'when', 'where', 'why', 'how', 'all',
            'any', 'both', 'each', 'few', 'more', 'most', 'other', 'some', 'such',
            'no', 'nor', 'not', 'only', 'own', 'same', 'so', 'than', 'too', 'very'
        }
        
        # 需要过滤的常见词
        self.filter_words = {
            'USD', 'CNY', 'RMB', 'GMT', 'UTC', 'AM', 'PM', 'Inc', 'Ltd', 'LLC', 
            'Corp', 'Co', 'Holdings', 'Capital', 'Investment', 'Investments', 
            'Fund', 'Funds', 'Management', 'Trading', 'Exchange', 'Platform', 
            'Technologies', 'Technology', 'Tech', 'Digital', 'Crypto', 'Blockchain',
            'DeFi', 'NFT', 'DAO', 'DEX', 'CEX', 'API', 'SDK', 'UI', 'UX', 'CEO',
            'CTO', 'CFO', 'CBO', 'COO', 'VP', 'SVP', 'EVP', 'President', 'Chairman'
        }
    
    def extract_english_entities(self, text: str) -> List[str]:
        """从中英文混合文本中提取英文实体，优先保留完整短语"""
        if not text:
            return []
        
        entities = []
        used_positions = set()  # 记录已经被提取的文本位置，避免重复
        
        # 1. 提取代币符号 $VIRTUAL -> virtual
        for match in re.finditer(r'\$([A-Z]+)', text):
            entities.append(match.group(1).lower())
            used_positions.update(range(match.start(), match.end()))
        
        # 2. 提取括号内的英文内容 (BIO Protocol) -> bio protocol
        for match in re.finditer(r'\(([^)]*)\)', text):
            english_in_brackets = self._extract_english_from_text(match.group(1))
            entities.extend(english_in_brackets)
            used_positions.update(range(match.start(), match.end()))
        
        # 3. 提取域名 Gate.io -> gate.io
        for match in re.finditer(r'([A-Za-z][A-Za-z0-9]*\.[a-z]{2,})', text):
            if not any(pos in used_positions for pos in range(match.start(), match.end())):
                entities.append(match.group(1).lower())
                used_positions.update(range(match.start(), match.end()))
        
        # 4. 优先提取连续的英文单词组合（2-4个词）
        # 匹配被中文或标点分隔的英文词组：Twitter Spaces, Yahoo Finance, Pantera Capital
        multi_word_pattern = r'([A-Z][a-zA-Z0-9]*(?:\s+[A-Z][a-zA-Z0-9]*){1,3})(?=[\s\u4e00-\u9fff：，。！？；]|$)'
        for match in re.finditer(multi_word_pattern, text):
            if not any(pos in used_positions for pos in range(match.start(), match.end())):
                entities.append(match.group(1).lower())
                used_positions.update(range(match.start(), match.end()))
        
        # 5. 提取单个英文单词（但跳过已经在短语中的部分）
        single_word_patterns = [
            r'([a-z]+[0-9]+[a-z]*)',  # ai16z, web3, 2fa等
            r'([A-Z]{2,})',           # BTC, ETH, USDT, CEO等全大写
            r'([A-Z][a-z]+)',         # Binance, Ethereum等首字母大写
            r'([A-Z][a-z]*[A-Z][a-z]*)', # iPhone, OKX等混合大小写
            r'([a-z]+[A-Z][a-z]*)',   # iPhone等小写开头混合
        ]
        
        for pattern in single_word_patterns:
            for match in re.finditer(pattern, text):
                # 只有当这个位置没有被多词短语占用时才提取
                if not any(pos in used_positions for pos in range(match.start(), match.end())):
                    entities.append(match.group(1).lower())
                    used_positions.update(range(match.start(), match.end()))
        
        return entities
    
    def _extract_english_from_text(self, text: str) -> List[str]:
        """从文本片段中提取英文单词和短语"""
        english_words = []
        
        # 提取英文单词
        words = re.findall(r'[A-Za-z][A-Za-z0-9]*', text)
        english_words.extend([word.lower() for word in words])
        
        # 提取英文短语（2-3个连续单词）
        phrases = re.findall(r'([A-Za-z][A-Za-z0-9]*(?:\s+[A-Za-z][A-Za-z0-9]*){1,2})', text)
        english_words.extend([phrase.lower() for phrase in phrases])
        
        return english_words
    
    def clean_entities(self, entities: List[str]) -> List[str]:
        """清理实体列表"""
        cleaned = []
        
        for entity in entities:
            entity = entity.strip()
            
            # 跳过空字符串和过短的词（至少3个字符，除非是常见缩写）
            if not entity:
                continue
            
            # 对于单个词，至少要3个字符，除非是常见的缩写
            common_abbreviations = {'ai', 'ui', 'ux', 'ip', 'id', 'os', 'db', 'api', 'sdk', 'ceo', 'cto', 'cfo'}
            if len(entity) < 3 and entity not in common_abbreviations:
                continue
            
            # 跳过纯数字
            if entity.isdigit():
                continue
            
            # 跳过停用词（现在实体都是小写，直接比较）
            if entity in self.stop_words:
                continue
            
            # 跳过一些无意义的短词
            meaningless_words = {'bi', 'ok', 'vi', 'ua', 'rt', 'cn', 'bc', 'er', 'le', 'se', 'te', 're'}
            if entity in meaningless_words:
                continue
            
            # 跳过纯符号
            if not any(c.isalnum() for c in entity):
                continue
            
            cleaned.append(entity)
        
        return list(set(cleaned))  # 去重
    
    def process_news_files(self, news_data_dir: str) -> List[Tuple[str, List[str]]]:
        """处理所有新闻文件，返回(标题, 提取的名词)的列表"""
        results = []
        
        csv_files = glob.glob(os.path.join(news_data_dir, "*.csv"))
        print(f"找到 {len(csv_files)} 个CSV文件")
        
        for i, csv_file in enumerate(csv_files):
            try:
                df = pd.read_csv(csv_file)
                
                if 'title' not in df.columns:
                    continue
                
                for title in df['title'].dropna():
                    title_str = str(title).strip()
                    if not title_str:
                        continue
                    
                    # 提取英文实体
                    entities = self.extract_english_entities(title_str)
                    cleaned = self.clean_entities(entities)
                    
                    # 只保存有提取结果的标题
                    if cleaned:
                        results.append((title_str, cleaned))
                
                if (i + 1) % 20 == 0:
                    print(f"已处理 {i + 1}/{len(csv_files)} 个文件")
                    
            except Exception as e:
                print(f"处理 {os.path.basename(csv_file)} 出错: {e}")
                continue
        
        return results
    
    def save_results(self, results: List[Tuple[str, List[str]]]):
        """保存结果"""
        # 创建输出目录
        output_dir = "output"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 保存提取结果CSV - 每行一个样本
        csv_path = os.path.join(output_dir, 'title_noun_extraction.csv')
        with open(csv_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['title', 'extracted_nouns'])
            
            for title, nouns in results:
                # 用分号分隔多个名词
                nouns_str = '; '.join(nouns)
                writer.writerow([title, nouns_str])
        
        # 统计所有名词的频次
        all_nouns = []
        for _, nouns in results:
            all_nouns.extend(nouns)
        
        counts = Counter(all_nouns)
        
        # 保存频次统计TXT
        txt_path = os.path.join(output_dir, 'noun_frequency_ranking.txt')
        with open(txt_path, 'w', encoding='utf-8') as f:
            f.write("英文名词频次统计（从高到低）\n")
            f.write("=" * 50 + "\n\n")
            
            for i, (word, count) in enumerate(counts.most_common(), 1):
                f.write(f"{i:4d}. {word:<25} : {count:>6d}\n")
        
        print(f"\n结果已保存:")
        print(f"CSV: {csv_path}")
        print(f"TXT: {txt_path}")
        print(f"成功提取样本数: {len(results)}")
        print(f"不同名词数: {len(set(all_nouns))}")
        print(f"总词频: {len(all_nouns)}")
        
        return counts

def main():
    print("开始从新闻标题中提取英文名词...")
    
    # 路径设置
    news_data_dir = "../news_data"
    
    if not os.path.exists(news_data_dir):
        print(f"错误: 找不到目录 {news_data_dir}")
        return
    
    # 创建提取器并处理
    extractor = TitleNounExtractor()
    results = extractor.process_news_files(news_data_dir)
    
    if not results:
        print("没有提取到任何有效样本")
        return
    
    # 保存结果
    counts = extractor.save_results(results)
    
    # 显示前20个样本
    print(f"\n前20个提取样本:")
    print("-" * 80)
    for i, (title, nouns) in enumerate(results[:20], 1):
        nouns_str = ', '.join(nouns)
        print(f"{i:2d}. {title[:50]}{'...' if len(title) > 50 else ''}")
        print(f"    提取: {nouns_str}")
        print()
    
    # 显示前20个最常见名词
    print(f"前20个最常见的英文名词:")
    print("-" * 40)
    for i, (word, count) in enumerate(counts.most_common(20), 1):
        print(f"{i:2d}. {word:<15} : {count:>4d}")

if __name__ == "__main__":
    main()