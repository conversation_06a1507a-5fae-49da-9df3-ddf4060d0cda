#!/usr/bin/env python3
"""
快速方法测试
"""

import pandas as pd
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score
import os


def load_data():
    """加载数据"""
    print("=== 加载数据 ===")
    
    df = pd.read_csv('news_training_samples_complete_all.csv')
    df = df[df['label'] != 'TBD'].copy()
    df = df.dropna(subset=['news_title', 'news_content', 'label'])
    
    numeric_columns = ['label', 'natr', 'mfi', 'rsi', 'btc_natr', 'btc_mfi', 'btc_rsi']
    for col in numeric_columns:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    df = df.dropna(subset=numeric_columns)
    
    # 创建分类
    returns = df['label'].values
    quantiles = np.percentile(returns, [20, 40, 60, 80])
    
    def categorize_return(ret):
        if ret <= quantiles[0]:
            return 0
        elif ret <= quantiles[1]:
            return 1
        elif ret <= quantiles[2]:
            return 2
        elif ret <= quantiles[3]:
            return 3
        else:
            return 4
    
    df['return_category'] = df['label'].apply(categorize_return)
    df['combined_text'] = df['news_title'].fillna('') + ' ' + df['news_content'].fillna('')
    
    # 取样本
    sample_df = df.sample(min(1000, len(df)), random_state=42)
    
    print(f"使用 {len(sample_df)} 条样本")
    print(f"分类分布: {sample_df['return_category'].value_counts().sort_index().to_dict()}")
    
    return sample_df


def test_method(name, X_text, technical_features, y):
    """测试单个方法"""
    print(f"\n=== 测试 {name} ===")
    
    # 合并特征
    if hasattr(X_text, 'toarray'):
        X_text = X_text.toarray()
    
    X = np.hstack([X_text, technical_features])
    
    # 训练测试
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
    
    model = RandomForestClassifier(n_estimators=50, random_state=42)
    model.fit(X_train, y_train)
    
    accuracy = model.score(X_test, y_test)
    print(f"准确率: {accuracy:.4f}")
    
    return accuracy


def main():
    """主函数"""
    print("🚀 快速方法测试")
    
    # 加载数据
    df = load_data()
    texts = df['combined_text'].values
    y = df['return_category'].values
    technical_features = df[['natr', 'mfi', 'rsi', 'btc_natr', 'btc_mfi', 'btc_rsi']].values
    
    results = {}
    
    # 1. 默认TF-IDF
    print("\n1. 默认TF-IDF")
    vectorizer = TfidfVectorizer(max_features=500, min_df=2, max_df=0.8)
    X_tfidf = vectorizer.fit_transform(texts)
    results['default_tfidf'] = test_method('默认TF-IDF', X_tfidf, technical_features, y)
    
    # 2. jieba分词
    try:
        import jieba
        print("\n2. jieba分词")
        
        def jieba_tokenize(text):
            return ' '.join(jieba.cut(text))
        
        tokenized_texts = [jieba_tokenize(text) for text in texts]
        vectorizer = TfidfVectorizer(max_features=500, min_df=2, max_df=0.8)
        X_jieba = vectorizer.fit_transform(tokenized_texts)
        results['jieba'] = test_method('jieba分词', X_jieba, technical_features, y)
        
    except ImportError:
        print("jieba未安装")
    
    # 3. 自训练SentencePiece
    try:
        import sentencepiece as spm
        
        model_path = "models/crypto_tokenizer_simple.model"
        if os.path.exists(model_path):
            print("\n3. 自训练SentencePiece")
            
            sp = spm.SentencePieceProcessor()
            sp.load(model_path)
            
            def sp_tokenize(text):
                return ' '.join(sp.encode_as_pieces(text))
            
            tokenized_texts = [sp_tokenize(text) for text in texts]
            vectorizer = TfidfVectorizer(max_features=500, min_df=2, max_df=0.8)
            X_sp = vectorizer.fit_transform(tokenized_texts)
            results['sentencepiece'] = test_method('自训练SentencePiece', X_sp, technical_features, y)
        else:
            print("SentencePiece模型不存在")
            
    except ImportError:
        print("sentencepiece未安装")
    
    # 4. Word2Vec
    try:
        from gensim.models import Word2Vec
        from gensim.utils import simple_preprocess
        
        print("\n4. Word2Vec")
        
        sentences = [simple_preprocess(text) for text in texts]
        model = Word2Vec(sentences=sentences, vector_size=50, window=5, min_count=2, workers=2, epochs=5)
        
        def get_doc_vector(words):
            vectors = []
            for word in words:
                if word in model.wv:
                    vectors.append(model.wv[word])
            
            if vectors:
                return np.mean(vectors, axis=0)
            else:
                return np.zeros(model.vector_size)
        
        X_w2v = np.array([get_doc_vector(simple_preprocess(text)) for text in texts])
        results['word2vec'] = test_method('Word2Vec', X_w2v, technical_features, y)
        
    except ImportError:
        print("gensim未安装")
    
    # 5. 简化GloVe (SVD)
    try:
        from sklearn.feature_extraction.text import CountVectorizer
        from sklearn.decomposition import TruncatedSVD
        
        print("\n5. 简化GloVe (SVD)")
        
        vectorizer = CountVectorizer(max_features=500, min_df=2, max_df=0.8)
        X_counts = vectorizer.fit_transform(texts)
        
        svd = TruncatedSVD(n_components=50, random_state=42)
        X_glove = svd.fit_transform(X_counts)
        results['glove_svd'] = test_method('简化GloVe', X_glove, technical_features, y)
        
    except Exception as e:
        print(f"GloVe失败: {e}")
    
    # 6. 注意力机制 (简化)
    try:
        print("\n6. 简化注意力机制")
        
        vectorizer = TfidfVectorizer(max_features=500, min_df=2, max_df=0.8)
        X_tfidf = vectorizer.fit_transform(texts)
        tfidf_array = X_tfidf.toarray()
        
        # 简单的注意力权重
        attention_weights = np.exp(tfidf_array) / (np.sum(np.exp(tfidf_array), axis=1, keepdims=True) + 1e-8)
        X_attention = tfidf_array * attention_weights
        
        from sklearn.decomposition import PCA
        pca = PCA(n_components=50, random_state=42)
        X_attention = pca.fit_transform(X_attention)
        
        results['attention'] = test_method('简化注意力', X_attention, technical_features, y)
        
    except Exception as e:
        print(f"注意力机制失败: {e}")
    
    # 总结结果
    print("\n" + "="*50)
    print("🏆 结果总结")
    print("="*50)
    
    if results:
        sorted_results = sorted(results.items(), key=lambda x: x[1], reverse=True)
        
        print("📊 方法排名:")
        for i, (method, accuracy) in enumerate(sorted_results, 1):
            print(f"  {i}. {method}: {accuracy:.4f}")
        
        best_method, best_score = sorted_results[0]
        print(f"\n🥇 最佳方法: {best_method} (准确率: {best_score:.4f})")
        
        # 与传统方法对比
        baseline = results.get('default_tfidf', 0)
        print(f"\n📈 相比传统TF-IDF的提升:")
        for method, accuracy in sorted_results:
            if method != 'default_tfidf':
                improvement = accuracy - baseline
                print(f"  {method}: {improvement:+.4f} ({improvement/baseline*100:+.1f}%)")
    else:
        print("❌ 没有成功的测试结果")


if __name__ == "__main__":
    main()
