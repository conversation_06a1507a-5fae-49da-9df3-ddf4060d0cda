"""
使用SentencePiece训练自定义分词器
专门针对新闻的中英混合文本
优化版本 - 支持数据预处理、增量训练和性能评估
"""

import os
import re
import pandas as pd
from typing import List, Dict, Optional, Tuple
import sentencepiece as spm
import logging
import json
from pathlib import Path
import time
from collections import Counter
import multiprocessing as mp
from functools import partial
import numpy as np

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TokenizerConfig:
    """分词器配置类"""

    # 预定义配置 - 针对18K+新闻数据优化
    CONFIGS = {
        'small': {
            'vocab_size': 4000,
            'model_type': 'unigram',
            'character_coverage': 0.995,
            'description': '小型配置，快速训练，适合实验'
        },
        'medium': {
            'vocab_size': 8000,
            'model_type': 'unigram',
            'character_coverage': 0.9995,
            'description': '中型配置，推荐用于18K新闻数据'
        },
        'large': {
            'vocab_size': 16000,
            'model_type': 'unigram',
            'character_coverage': 0.9999,
            'description': '大型配置，最佳质量，适合生产环境'
        },
        'xlarge': {
            'vocab_size': 32000,
            'model_type': 'unigram',
            'character_coverage': 0.9999,
            'description': '超大配置，最高质量，需要更多计算资源'
        },
        'bpe_medium': {
            'vocab_size': 8000,
            'model_type': 'bpe',
            'character_coverage': 0.9995,
            'description': 'BPE模型中型配置，适合对比测试'
        },
        'bpe_large': {
            'vocab_size': 16000,
            'model_type': 'bpe',
            'character_coverage': 0.9999,
            'description': 'BPE模型大型配置'
        }
    }

    @classmethod
    def get_config(cls, config_name: str) -> Dict:
        """获取预定义配置"""
        if config_name not in cls.CONFIGS:
            available = ', '.join(cls.CONFIGS.keys())
            raise ValueError(f"未知配置: {config_name}. 可用配置: {available}")
        return cls.CONFIGS[config_name].copy()

    @classmethod
    def list_configs(cls):
        """列出所有可用配置"""
        print("可用的分词器配置:")
        for name, config in cls.CONFIGS.items():
            print(f"  {name}: {config['description']}")
            print(f"    词汇表大小: {config['vocab_size']}")
            print(f"    模型类型: {config['model_type']}")
            print(f"    字符覆盖率: {config['character_coverage']}")
            print()

class TokenizerTrainer:
    """优化的分词器训练类"""

    def __init__(
        self,
        vocab_size: int = 2500,
        model_type: str = 'unigram',
        character_coverage: float = 0.995,
        max_sentence_length: int = 4192
    ):
        """
        初始化分词器训练器

        Args:
            vocab_size: 词汇表大小
            model_type: 模型类型 ('unigram', 'bpe', 'word', 'char')
            character_coverage: 字符覆盖率
            max_sentence_length: 最大句子长度
        """
        self.vocab_size = vocab_size
        self.model_type = model_type
        self.character_coverage = character_coverage
        self.max_sentence_length = max_sentence_length

        # 创建必要的目录
        self.data_dir = Path('data')
        self.models_dir = Path('models')
        self.data_dir.mkdir(exist_ok=True)
        self.models_dir.mkdir(exist_ok=True)

        logger.info(f"初始化分词器训练器 - 词汇表大小: {vocab_size}, 模型类型: {model_type}")

    def load_symbol_aliases(self) -> Dict[str, str]:
        """加载符号别名映射"""
        try:
            with open('symbol_aliases.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.warning("symbol_aliases.json not found. Using default aliases.")
            return {}
        except json.JSONDecodeError:
            logger.warning("Error decoding symbol_aliases.json. Using default aliases.")
            return {}

    def analyze_symbol_frequency(self, training_data_file: str) -> Dict[str, int]:
        """分析训练数据中符号的频率"""
        logger.info("分析训练数据中的符号频率...")

        # 获取所有候选符号
        all_symbols = self._get_all_candidate_symbols()

        # 统计符号频率
        symbol_freq = Counter()

        try:
            with open(training_data_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue

                    # 统计每个符号在当前行中的出现次数
                    for symbol in all_symbols:
                        # 使用正则表达式进行更精确的匹配
                        import re
                        # 对于英文符号，确保是完整单词
                        if re.match(r'^[A-Za-z0-9-]+$', symbol):
                            pattern = r'\b' + re.escape(symbol) + r'\b'
                            matches = len(re.findall(pattern, line, re.IGNORECASE))
                        else:
                            # 对于中文符号，直接计数
                            matches = line.count(symbol)

                        if matches > 0:
                            symbol_freq[symbol] += matches

                    # 每处理1000行显示进度
                    if line_num % 1000 == 0:
                        logger.debug(f"已处理 {line_num} 行...")

        except Exception as e:
            logger.error(f"分析符号频率时出错: {e}")
            return {}

        logger.info(f"符号频率分析完成，找到 {len(symbol_freq)} 个有效符号")
        return dict(symbol_freq)

    def _normalize_base_token(self, base_asset: str) -> str:
        """标准化基础代币名称，移除特殊前缀"""
        # Check for 1000000 prefix
        if base_asset.startswith('1000000'):
            return base_asset[7:]  # Remove '1000000' prefix
        # Check for 1000 prefix
        elif base_asset.startswith('1000'):
            return base_asset[4:]  # Remove '1000' prefix
        # Check for 1M prefix
        elif base_asset.startswith('1M'):
            return base_asset[2:]  # Remove '1M' prefix
        else:
            return base_asset

    def _get_all_candidate_symbols(self) -> List[str]:
        """获取所有候选符号"""
        # 加载币安符号
        binance_symbols = []
        try:
            with open('binance_symbols.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
                for symbol_full in data.keys():
                    if symbol_full.endswith('USDT'):
                        # 去除USDT后缀
                        symbol = symbol_full.replace('USDT', '')
                        # 标准化基础代币名称（移除特殊前缀）
                        normalized_symbol = self._normalize_base_token(symbol)
                        # 转换为小写
                        normalized_symbol = normalized_symbol.lower()
                        if len(normalized_symbol) > 1:  # 排除单字符符号
                            binance_symbols.append(normalized_symbol)
                logger.info(f"加载了 {len(binance_symbols)} 个币安符号")
        except FileNotFoundError:
            logger.warning("binance_symbols.json not found. Skipping Binance symbols.")
        except json.JSONDecodeError:
            logger.warning("Error decoding binance_symbols.json. Skipping Binance symbols.")

        # 加载符号别名（只使用crypto_aliases，避免symbol_aliases中的误匹配）
        try:
            with open('crypto_aliases.json', 'r', encoding='utf-8') as f:
                crypto_aliases = json.load(f)
                # 只取键（符号名称），并过滤掉包含空格的
                alias_symbols = [key for key in crypto_aliases.keys() if ' ' not in key and len(key) > 1]
                logger.info(f"加载了 {len(alias_symbols)} 个加密货币别名")
        except FileNotFoundError:
            logger.warning("crypto_aliases.json not found. Skipping crypto aliases.")
            alias_symbols = []
        except json.JSONDecodeError:
            logger.warning("Error decoding crypto_aliases.json. Skipping crypto aliases.")
            alias_symbols = []

        # 扩展的英文加密货币术语
        english_terms = [
            # 基础概念
            "Bitcoin", "Ethereum", "Blockchain", "Cryptocurrency", "DeFi", "NFT", "Web3",
            "Altcoin", "Stablecoin", "Token", "Coin", "Wallet", "Exchange", "Mining",
            "Staking", "Node", "Fork", "Consensus", "Hash", "Address", "Protocol",

            # 技术术语
            "SmartContract", "DApp", "DAO", "Layer1", "Layer2", "Sidechain", "Bridge",
            "Oracle", "Validator", "Proof-of-Work", "Proof-of-Stake", "Sharding",
            "Rollup", "ZK-Rollup", "Optimistic", "Atomic-Swap", "Cross-Chain",
            "Mainnet", "Testnet", "Consensus", "Byzantine", "Merkle", "IPFS",

            # 交易术语
            "HODL", "FOMO", "FUD", "ATH", "ATL", "Bull-Market", "Bear-Market",
            "Pump", "Dump", "Whale", "Rug-Pull", "Liquidity", "Slippage", "AMM",
            "DEX", "CEX", "Market-Cap", "Volume", "Volatility", "Arbitrage",
            "Orderbook", "Spread", "Leverage", "Margin", "Futures", "Options",

            # DeFi术语
            "Yield-Farming", "Liquidity-Mining", "Flash-Loan", "Impermanent-Loss",
            "TVL", "APY", "APR", "Collateral", "Lending", "Borrowing", "Governance-Token",
            "Uniswap", "Compound", "Aave", "MakerDAO", "Curve", "Balancer", "SushiSwap",
            "PancakeSwap", "Synthetix", "Yearn", "Convex", "Frax", "Lido",

            # NFT术语
            "Non-Fungible-Token", "Minting", "Metadata", "Collection", "Rarity",
            "Floor-Price", "Opensea", "Royalties", "PFP", "Utility-NFT", "GameFi",
            "Play-to-Earn", "Metaverse", "Avatar", "Digital-Art", "Collectible",

            # 技术分析
            "MACD", "RSI", "Moving-Average", "Support", "Resistance", "Trend",
            "Candlestick", "Volume-Profile", "Fibonacci", "Bollinger-Bands",
            "Stochastic", "Williams", "Ichimoku", "Elliott-Wave", "Divergence",

            # 安全术语
            "Private-Key", "Public-Key", "Seed-Phrase", "Cold-Storage", "Hot-Wallet",
            "Multi-Sig", "KYC", "AML", "Phishing", "Scam", "Honeypot", "Reentrancy",
            "Smart-Contract-Audit", "Bug-Bounty", "Exploit", "Vulnerability",

            # 主流币种和项目
            "BTC", "ETH", "BNB", "ADA", "SOL", "DOT", "AVAX", "MATIC", "LINK",
            "UNI", "USDT", "USDC", "DAI", "BUSD", "DOGE", "SHIB", "LTC", "XRP",
            "ATOM", "NEAR", "FTM", "ALGO", "VET", "ICP", "FLOW", "MANA", "SAND",
            "AXS", "ENJ", "CHZ", "BAT", "ZEC", "DASH", "XMR", "ETC", "BCH",

            # 交易所和平台
            "Binance", "Coinbase", "Kraken", "Bybit", "OKX", "Kucoin", "FTX",
            "Huobi", "Gate", "Bitfinex", "Gemini", "Crypto-com", "Bitget",
            "MEXC", "Bitrue", "Phemex", "Deribit", "BitMEX",

            # 新兴概念
            "AI-Crypto", "RWA", "DePin", "SocialFi", "LSDfi", "Restaking",
            "Modular-Blockchain", "Account-Abstraction", "Intent", "MEV",
            "Liquid-Staking", "Validator-Set", "Slashing", "Jailing"
        ]

        # 获取所有中文术语
        chinese_terms = self._get_all_chinese_terms()

        # 获取关键英文术语
        critical_english_terms = [
            "bitcoin", "ethereum", "blockchain", "cryptocurrency", "defi", "nft", "web3",
            "altcoin", "stablecoin", "token", "coin", "wallet", "exchange", "mining",
            "staking", "node", "fork", "consensus", "hash", "address", "protocol",
            "smartcontract", "dapp", "dao", "layer1", "layer2", "sidechain", "bridge",
            "oracle", "validator", "proof-of-work", "proof-of-stake", "sharding",
            "rollup", "zk-rollup", "optimistic", "atomic-swap", "cross-chain",
            "mainnet", "testnet", "consensus", "byzantine", "merkle", "ipfs",
            "hodl", "fomo", "fud", "ath", "atl", "bull-market", "bear-market",
            "pump", "dump", "whale", "rug-pull", "liquidity", "slippage", "amm",
            "dex", "cex", "market-cap", "volume", "volatility", "arbitrage",
            "orderbook", "spread", "leverage", "margin", "futures", "options",
            "yield-farming", "liquidity-mining", "flash-loan", "impermanent-loss",
            "tvl", "apy", "apr", "collateral", "lending", "borrowing", "governance-token",
            "uniswap", "compound", "aave", "makerdao", "curve", "balancer", "sushiswap",
            "pancakeswap", "synthetix", "yearn", "convex", "frax", "lido",
            "btc", "eth", "bnb", "ada", "sol", "dot", "avax", "matic", "link",
            "uni", "usdt", "usdc", "dai", "busd", "doge", "shib", "ltc", "xrp",
            "atom", "near", "ftm", "algo", "vet", "icp", "flow", "mana", "sand",
            "axs", "enj", "chz", "bat", "zec", "dash", "xmr", "etc", "bch",
            "binance", "coinbase", "kraken", "bybit", "okx", "kucoin", "ftx",
            "huobi", "gate", "bitfinex", "gemini", "crypto-com", "bitget"
        ]

        # 将英文术语转换为小写
        english_terms_lower = [term.lower() for term in english_terms]

        # 将符号别名也转换为小写（如果是英文的话）
        alias_symbols_lower = []
        for alias in alias_symbols:
            # 检查是否包含英文字符
            if re.search(r'[a-zA-Z]', alias):
                alias_symbols_lower.append(alias.lower())
            else:
                alias_symbols_lower.append(alias)

        # 合并所有符号并去重
        all_symbols = list(set(binance_symbols + alias_symbols_lower + english_terms_lower + critical_english_terms + chinese_terms))

        # 记录各类符号的数量
        logger.info(f"符号统计: 币安符号={len(binance_symbols)}, 别名符号={len(alias_symbols_lower)}, "
                   f"英文术语={len(english_terms_lower)}, 关键英文术语={len(critical_english_terms)}, "
                   f"中文术语={len(chinese_terms)}")

        return sorted(all_symbols)

    def _get_all_chinese_terms(self) -> List[str]:
        """获取所有中文加密货币术语"""
        return [
            # 基础概念
            "比特币", "以太坊", "区块链", "加密货币", "数字货币", "代币", "通证",
            "钱包", "交易所", "挖矿", "矿工", "节点", "分叉", "共识", "哈希", "协议",

            # 技术术语
            "智能合约", "去中心化应用", "去中心化金融", "去中心化自治组织",
            "侧链", "跨链", "预言机", "验证者", "工作量证明", "权益证明", "分片",
            "二层网络", "原子交换", "零知识证明", "隐私币", "稳定币", "主网", "测试网",

            # 交易术语
            "囤币", "恐慌", "错失恐惧", "历史新高", "历史新低", "牛市", "熊市",
            "拉盘", "砸盘", "巨鲸", "跑路", "流动性", "滑点", "做市商", "杠杆",
            "去中心化交易所", "中心化交易所", "市值", "交易量", "波动性", "套利",
            "订单簿", "价差", "保证金", "期货", "期权", "合约",

            # 中文俚语和网络用语
            "大饼", "二饼", "山寨币", "空气币", "传销币", "韭菜", "割韭菜",
            "庄家", "散户", "梭哈", "抄底", "逃顶", "套牢", "解套", "止损",
            "建仓", "平仓", "爆仓", "强平", "插针", "瀑布", "横盘", "放量",
            "拉升", "回调", "震荡", "突破", "跳水", "反弹", "洗盘", "出货",

            # DeFi术语
            "流动性挖矿", "收益农场", "质押", "借贷", "闪电贷", "无常损失",
            "锁仓价值", "年化收益", "抵押品", "治理代币", "去中心化金融",
            "自动做市商", "流动性池", "收益聚合器", "合成资产", "算法稳定币",

            # NFT和元宇宙术语
            "非同质化代币", "数字藏品", "铸造", "地板价", "稀有度", "版税",
            "头像项目", "实用型NFT", "元宇宙", "链游", "边玩边赚", "数字艺术",
            "虚拟土地", "游戏道具", "社交代币", "创作者经济",

            # 技术分析
            "移动平均线", "相对强弱指标", "支撑位", "阻力位", "趋势线",
            "K线", "成交量", "技术指标", "黄金交叉", "死亡交叉", "背离",
            "布林带", "随机指标", "威廉指标", "一目均衡表", "艾略特波浪",

            # 安全术语
            "私钥", "公钥", "助记词", "冷钱包", "热钱包", "多重签名",
            "实名认证", "反洗钱", "钓鱼", "诈骗", "蜜罐", "假币", "重入攻击",
            "智能合约审计", "漏洞赏金", "安全漏洞", "黑客攻击",

            # 主流币种中文名
            "比特币", "以太坊", "币安币", "艾达币", "索拉纳", "波卡", "雪崩",
            "马蹄", "链环", "柚子", "泰达币", "美元币", "戴", "狗狗币",
            "柴犬币", "莱特币", "瑞波币", "波场", "艾欧塔", "门罗币", "以太经典",
            "比特币现金", "达世币", "大零币", "宇宙币", "近协议", "幻影币",

            # 交易所中文名
            "币安", "欧易", "火币", "芝麻开门", "库币", "抹茶", "币赢",
            "比特儿", "中币", "满币", "虎符", "鲸交所", "WBF",

            # 新兴概念中文
            "人工智能币", "现实世界资产", "去中心化物理基础设施", "社交金融",
            "流动性质押衍生品", "重质押", "模块化区块链", "账户抽象", "意图交易",
            "最大可提取价值", "流动性质押", "验证者集合", "削减", "监禁"
        ]

    def get_critical_terms(self) -> List[str]:
        """获取关键术语列表（用于术语保护，包含中英文）"""
        # 定义关键中文术语（高频且重要的术语）
        critical_chinese_terms = [
            "比特币", "以太坊", "区块链", "加密货币", "数字货币", "去中心化",
            "智能合约", "去中心化金融", "稳定币", "流动性", "治理代币",
            "预言机", "零知识证明", "二层网络", "跨链", "侧链",
            "去中心化交易所", "中心化交易所", "自动做市商", "流动性挖矿",
            "收益农场", "质押", "借贷", "闪电贷", "无常损失", "锁仓价值",
            "支撑位", "阻力位", "趋势线", "移动平均线", "相对强弱指标",
            "成交量", "技术指标", "黄金交叉", "死亡交叉", "背离",
            "牛市", "熊市", "横盘", "突破", "回调", "震荡", "放量",
            "拉升", "跳水", "反弹", "洗盘", "出货", "建仓", "平仓"
        ]

        # 定义关键英文术语（转换为小写）
        critical_english_terms = [
            "btc", "eth", "defi", "nft", "uniswap", "amm", "binance", "usdt",
            "ethereum", "polygon", "chainlink", "avalanche", "arbitrum",
            "optimistic", "makerdao", "tvl", "layer2", "rollup"
        ]

        # 合并中英文关键术语
        return critical_chinese_terms + critical_english_terms

    def get_critical_chinese_terms(self) -> List[str]:
        """获取关键中文术语列表（保持向后兼容）"""
        return [term for term in self.get_critical_terms() if not re.search(r'[a-zA-Z]', term)]

    def preprocess_training_data_with_term_protection(self, input_file: str, output_file: str) -> str:
        """预处理训练数据，保护关键术语不被分割"""
        logger.info("开始预处理训练数据以保护关键术语...")
        
        critical_terms = self.get_critical_terms()
        
        # 创建术语到占位符的映射
        term_to_placeholder = {}
        placeholder_to_term = {}
        
        for i, term in enumerate(critical_terms):
            placeholder = f"__TERM_{i:04d}__"
            term_to_placeholder[term] = placeholder
            placeholder_to_term[placeholder] = term
        
        # 保存映射关系
        mapping_file = self.data_dir / 'term_placeholder_mapping.json'
        with open(mapping_file, 'w', encoding='utf-8') as f:
            json.dump({
                'term_to_placeholder': term_to_placeholder,
                'placeholder_to_term': placeholder_to_term
            }, f, ensure_ascii=False, indent=2)
        
        logger.info(f"创建了 {len(critical_terms)} 个术语占位符映射")
        
        # 处理训练数据
        processed_lines = []
        term_usage_count = Counter()
        
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue
                    
                    # 替换关键术语为占位符
                    processed_line = line
                    for term, placeholder in term_to_placeholder.items():
                        if term in processed_line:
                            count = processed_line.count(term)
                            term_usage_count[term] += count
                            processed_line = processed_line.replace(term, placeholder)
                    
                    processed_lines.append(processed_line)
                    
                    if line_num % 1000 == 0:
                        logger.debug(f"已处理 {line_num} 行...")
        
        except Exception as e:
            logger.error(f"预处理训练数据时出错: {e}")
            return input_file
        
        # 保存预处理后的数据
        with open(output_file, 'w', encoding='utf-8') as f:
            for line in processed_lines:
                f.write(line + '\n')
        
        # 统计术语使用情况
        logger.info("关键术语使用统计:")
        for term, count in term_usage_count.most_common(20):
            logger.info(f"  {term}: {count} 次")
        
        logger.info(f"预处理完成，保存到: {output_file}")
        return output_file

    def get_user_defined_symbols(self) -> str:
        """获取用户定义的符号列表，包含关键术语保护"""
        logger.info("开始获取用户定义符号...")

        # 获取关键中文术语
        critical_terms = self.get_critical_chinese_terms()
        logger.info(f"添加 {len(critical_terms)} 个关键中文术语")

        # 获取所有候选符号
        candidate_symbols = self._get_all_candidate_symbols()
        logger.info(f"获取 {len(candidate_symbols)} 个候选符号")

        # 合并关键术语和候选符号，去重
        all_symbols = list(set(critical_terms + candidate_symbols))

        # 过滤符号：移除包含空格、特殊字符或过长的符号
        filtered_symbols = []
        for symbol in all_symbols:
            # 检查符号是否符合SentencePiece要求
            if self._is_valid_user_symbol(symbol):
                filtered_symbols.append(symbol)
            else:
                logger.debug(f"跳过无效符号: '{symbol}'")

        logger.info(f"过滤后保留 {len(filtered_symbols)} 个有效符号（原始: {len(all_symbols)}）")

        # 为关键术语创建占位符（用于训练时保护）
        protected_symbols = []
        for i, term in enumerate(critical_terms):
            if self._is_valid_user_symbol(term):  # 只为有效术语创建占位符
                placeholder = f"__TERM_{i:04d}__"
                protected_symbols.append(placeholder)

        # 将占位符也加入符号列表
        filtered_symbols.extend(protected_symbols)

        logger.info(f"总共收集了 {len(filtered_symbols)} 个用户定义符号（包含 {len(protected_symbols)} 个关键术语占位符）")

        # 保存符号到文件
        symbols_file = self.data_dir / 'user_defined_symbols.txt'
        with open(symbols_file, 'w', encoding='utf-8') as f:
            for symbol in filtered_symbols:
                f.write(symbol + '\n')

        logger.info(f"用户定义符号已保存到: {symbols_file}")
        return str(symbols_file)

    def _is_valid_user_symbol(self, symbol: str) -> bool:
        """检查符号是否符合SentencePiece用户定义符号的要求"""
        if not symbol or not isinstance(symbol, str):
            return False

        # 移除前后空格
        symbol = symbol.strip()

        # 检查长度（避免过长的符号）
        if len(symbol) == 0 or len(symbol) > 50:
            return False

        # 检查是否包含空格或其他空白字符（SentencePiece不支持包含空格的用户定义符号）
        if any(c.isspace() for c in symbol):
            return False

        # 检查是否包含特殊控制字符
        if any(ord(c) < 32 for c in symbol):
            return False

        # 检查是否以特殊字符开头（避免与SentencePiece内部符号冲突）
        if symbol.startswith(('▁', '<', '>', '[', ']', '__')):
            # 允许我们自己的占位符
            if symbol.startswith('__TERM_') and symbol.endswith('__'):
                return True
            return False

        return True

    def _get_priority_symbols(self) -> List[str]:
        """获取优先级最高的符号列表"""
        # 最重要的加密货币术语（必须包含）
        critical_symbols = [
            # 主流币种
            'btc', 'eth', 'bnb', 'usdt', 'usdc', 'dai', 'busd',
            'sol', 'ada', 'dot', 'matic', 'avax', 'link', 'uni',
            'doge', 'shib', 'ltc', 'xrp', 'atom', 'near', 'ftm',

            # 重要中文术语
            '比特币', '以太坊', '区块链', '加密货币', '数字货币', '去中心化',
            '智能合约', '去中心化金融', '稳定币', '流动性', '质押', '挖矿',
            '钱包', '交易所', '合约', '代币', '币安', '火币', '欧易',

            # 核心DeFi术语
            'defi', 'nft', 'dao', 'amm', 'tvl', 'apy', 'apr',
            'uniswap', 'compound', 'aave', 'makerdao', 'curve',
            'sushiswap', 'pancakeswap', 'yearn', 'lido',

            # 重要交易所
            'binance', 'coinbase', 'kraken', 'bybit', 'okx', 'kucoin',
            'huobi', 'gate', 'bitfinex', 'gemini', 'bitget',

            # 技术术语
            'layer2', 'rollup', 'zk-rollup', 'optimistic', 'arbitrum',
            'polygon', 'chainlink', 'avalanche', 'solana', 'cardano',
            'polkadot', 'cosmos', 'ethereum', 'bitcoin'
        ]

        return critical_symbols

    def _select_optimal_symbols(self, all_symbols: List[str], priority_symbols: List[str]) -> List[str]:
        """优化符号选择策略"""
        # 确保优先符号在列表开头
        selected = []

        # 1. 首先添加优先符号（如果存在）
        for symbol in priority_symbols:
            if symbol in all_symbols and symbol not in selected:
                selected.append(symbol)

        # 2. 添加其他符号，按长度和重要性排序
        remaining_symbols = [s for s in all_symbols if s not in selected]

        # 按以下优先级排序：
        # - 长度适中的符号（2-10个字符）优先
        # - 包含数字的符号（如币种代码）优先
        # - 中文术语优先
        def symbol_priority(symbol):
            score = 0

            # 长度评分
            if 2 <= len(symbol) <= 10:
                score += 10
            elif len(symbol) <= 15:
                score += 5

            # 包含数字（币种代码）
            if any(c.isdigit() for c in symbol):
                score += 5

            # 中文术语
            if any('\u4e00' <= c <= '\u9fff' for c in symbol):
                score += 8

            # 常见加密货币后缀
            if symbol.endswith(('coin', 'token', 'swap', 'finance')):
                score += 3

            # 避免过长的符号
            if len(symbol) > 20:
                score -= 10

            return score

        # 排序并添加剩余符号
        remaining_symbols.sort(key=symbol_priority, reverse=True)
        selected.extend(remaining_symbols)

        logger.info(f"符号选择完成: 优先符号 {len([s for s in priority_symbols if s in all_symbols])} 个, "
                   f"总符号 {len(selected)} 个")

        return selected

    def clean_text(self, text: str) -> str:
        """清理文本数据，使用CryptoNewsTextProcessor"""
        if pd.isna(text) or not isinstance(text, str):
            return ""

        try:
            # 导入轻量级文本处理器
            from crypto_text_processor import CryptoNewsTextProcessor

            # 创建处理器实例（如果还没有创建）
            if not hasattr(self, '_text_processor'):
                self._text_processor = CryptoNewsTextProcessor()

            # 处理文本
            processed_text = self._text_processor.process(text)

            logger.debug("使用CryptoNewsTextProcessor处理文本")
            return processed_text

        except ImportError as e:
            logger.error(f"无法导入CryptoNewsTextProcessor: {e}")
            raise ImportError("CryptoNewsTextProcessor是必需的依赖，请确保crypto_text_processor.py文件存在")

        except Exception as e:
            logger.error(f"文本处理出错: {e}")
            return ""

    def validate_and_clean_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """验证和清理数据框"""
        logger.info(f"开始清理数据，原始数据量: {len(df)}")

        # 检查必要的列
        required_columns = ['title', 'content']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"缺少必要的列: {missing_columns}")

        # 移除空值行
        df = df.dropna(subset=['title', 'content'])

        # 清理文本
        df['title'] = df['title'].apply(self.clean_text)
        df['content'] = df['content'].apply(self.clean_text)

        # 移除空文本行
        df = df[(df['title'].str.len() > 0) & (df['content'].str.len() > 0)]

        # 移除重复行
        df = df.drop_duplicates(subset=['title', 'content'])

        logger.info(f"清理完成，剩余数据量: {len(df)}")
        return df

    def prepare_training_data(self, news_df: pd.DataFrame, use_cache: bool = True) -> str:
        """准备训练数据"""
        train_file = self.data_dir / 'tokenizer_training_data.txt'

        # 检查缓存
        if use_cache and train_file.exists():
            logger.info(f"使用缓存的训练数据: {train_file}")
            return str(train_file)

        # 验证和清理数据
        news_df = self.validate_and_clean_dataframe(news_df)

        # 合并标题和正文
        texts = []
        for _, row in news_df.iterrows():
            combined_text = f"{row['title']} {row['content']}"
            if len(combined_text.strip()) > 10:  # 过滤过短的文本
                texts.append(combined_text)

        # 统计信息
        total_chars = sum(len(text) for text in texts)
        avg_length = total_chars / len(texts) if texts else 0

        logger.info(f"准备训练数据统计:")
        logger.info(f"  - 文本数量: {len(texts)}")
        logger.info(f"  - 总字符数: {total_chars:,}")
        logger.info(f"  - 平均长度: {avg_length:.1f}")

        # 保存为文本文件
        with open(train_file, 'w', encoding='utf-8') as f:
            for text in texts:
                f.write(text + '\n')

        logger.info(f"训练数据已保存到: {train_file}")
        return str(train_file)
    
    def optimize_training_parameters(self, input_file: str) -> Dict:
        """基于训练数据分析优化训练参数"""
        logger.info("分析训练数据以优化参数...")

        # 分析文本长度分布
        lengths = []
        total_chars = 0
        line_count = 0

        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        length = len(line)
                        lengths.append(length)
                        total_chars += length
                        line_count += 1
        except Exception as e:
            logger.error(f"分析训练数据时出错: {e}")
            return {}

        if not lengths:
            logger.warning("训练数据为空")
            return {}

        # 计算统计信息
        lengths_array = np.array(lengths)
        stats = {
            'total_lines': line_count,
            'total_chars': total_chars,
            'avg_length': np.mean(lengths_array),
            'median_length': np.median(lengths_array),
            'max_length': np.max(lengths_array),
            'min_length': np.min(lengths_array),
            'p95_length': np.percentile(lengths_array, 95),
            'p99_length': np.percentile(lengths_array, 99),
            'std_length': np.std(lengths_array)
        }

        # 计算超过当前max_sentence_length的行数
        long_lines = sum(1 for length in lengths if length > self.max_sentence_length)
        long_lines_ratio = long_lines / line_count if line_count > 0 else 0

        logger.info("训练数据统计:")
        logger.info(f"  总行数: {stats['total_lines']:,}")
        logger.info(f"  总字符数: {stats['total_chars']:,}")
        logger.info(f"  平均长度: {stats['avg_length']:.1f}")
        logger.info(f"  中位数长度: {stats['median_length']:.1f}")
        logger.info(f"  最大长度: {stats['max_length']:,}")
        logger.info(f"  95%分位数: {stats['p95_length']:.1f}")
        logger.info(f"  99%分位数: {stats['p99_length']:.1f}")
        logger.info(f"  当前max_sentence_length: {self.max_sentence_length}")
        logger.info(f"  超长行数: {long_lines} ({long_lines_ratio:.2%})")

        # 推荐优化的max_sentence_length
        recommended_max_length = int(stats['p99_length'] * 1.1)  # 99%分位数的1.1倍

        if long_lines_ratio > 0.01:  # 如果超过1%的行被跳过
            logger.warning(f"建议将max_sentence_length从{self.max_sentence_length}增加到{recommended_max_length}")
            stats['recommended_max_length'] = recommended_max_length
        else:
            stats['recommended_max_length'] = self.max_sentence_length

        return stats

    def train(self, input_file: str, model_prefix: str = 'crypto_tokenizer', use_user_symbols: bool = True, protect_terms: bool = True) -> str:
        """训练SentencePiece分词器，支持关键术语保护"""
        start_time = time.time()

        # 模型文件路径
        model_path = self.models_dir / f"{model_prefix}.model"
        vocab_path = self.models_dir / f"{model_prefix}.vocab"

        # 如果启用术语保护，预处理训练数据
        actual_input_file = input_file
        if protect_terms:
            logger.info("启用关键术语保护模式")
            protected_file = str(self.data_dir / f"protected_{Path(input_file).name}")
            actual_input_file = self.preprocess_training_data_with_term_protection(input_file, protected_file)
        else:
            logger.info("使用标准训练模式")

        # 分析训练数据并优化参数
        data_stats = self.optimize_training_parameters(actual_input_file)

        # 如果建议的max_sentence_length不同，使用建议值
        if data_stats.get('recommended_max_length', self.max_sentence_length) != self.max_sentence_length:
            original_max_length = self.max_sentence_length
            self.max_sentence_length = data_stats['recommended_max_length']
            logger.info(f"根据数据分析，将max_sentence_length从{original_max_length}调整为{self.max_sentence_length}")

        # 基础训练参数
        training_args = {
            '--input': actual_input_file,
            '--model_prefix': str(self.models_dir / model_prefix),
            '--vocab_size': str(self.vocab_size),
            '--character_coverage': str(self.character_coverage),
            '--model_type': self.model_type,
            '--max_sentence_length': str(self.max_sentence_length),
            '--pad_id': '0',
            '--unk_id': '1',
            '--bos_id': '2',
            '--eos_id': '3',
            '--pad_piece': '[PAD]',
            '--unk_piece': '[UNK]',
            '--bos_piece': '[CLS]',
            '--eos_piece': '[SEP]',
            '--control_symbols': '[MASK]',
        }

        # 添加用户定义符号支持 - 优化版
        if use_user_symbols:
            try:
                symbols_file = self.get_user_defined_symbols()  # 获取所有用户定义符号
                if os.path.exists(symbols_file):
                    # 读取符号列表
                    with open(symbols_file, 'r', encoding='utf-8') as f:
                        all_symbols = [line.strip() for line in f if line.strip()]

                    # 优化符号选择策略：优先选择重要术语
                    priority_symbols = self._get_priority_symbols()
                    selected_symbols = self._select_optimal_symbols(all_symbols, priority_symbols)

                    # 计算合理的符号数量限制
                    # 用户定义符号数量应该小于词汇表大小的一定比例
                    vocab_size = self.vocab_size
                    max_user_symbols = min(vocab_size // 10, 800)  # 最多使用词汇表的10%，且不超过800个

                    if len(selected_symbols) > max_user_symbols:
                        logger.warning(f"符号数量过多({len(selected_symbols)})，限制为{max_user_symbols}个以确保词汇表空间充足")
                        selected_symbols = selected_symbols[:max_user_symbols]

                    # SentencePiece期望的是逗号分隔的符号列表
                    symbols_str = ','.join(selected_symbols)
                    training_args['--user_defined_symbols'] = symbols_str
                    logger.info(f"已添加 {len(selected_symbols)} 个用户定义符号（词汇表大小: {vocab_size}）")

                else:
                    logger.warning("用户定义符号文件不存在，跳过")
            except Exception as e:
                logger.warning(f"处理用户定义符号时出错: {e}，将跳过用户定义符号")
        else:
            logger.info("跳过用户定义符号")

        # 添加优化参数
        if self.model_type == 'unigram':
            # Unigram模型的优化参数
            training_args.update({
                '--seed_sentencepiece_size': '1000000',  # 增加种子句子片段大小
                '--shrinking_factor': '0.75',  # 收缩因子
                '--num_sub_iterations': '2',  # 子迭代次数
            })
        elif self.model_type == 'bpe':
            # BPE模型的优化参数
            training_args.update({
                '--num_threads': str(min(16, mp.cpu_count())),  # 使用多线程
            })

        logger.info("开始训练分词器...")
        logger.info(f"训练参数: 词汇表大小={self.vocab_size}, 模型类型={self.model_type}")
        logger.info(f"最大句子长度: {self.max_sentence_length}")
        logger.info(f"字符覆盖率: {self.character_coverage}")

        try:
            args = ' '.join([f'{k}={v}' for k, v in training_args.items()])
            spm.SentencePieceTrainer.train(args)

            training_time = time.time() - start_time
            logger.info(f"分词器训练完成! 耗时: {training_time:.2f}秒")
            logger.info(f"模型文件: {model_path}")
            logger.info(f"词汇表文件: {vocab_path}")

            return str(model_path)

        except Exception as e:
            logger.error(f"训练失败: {e}")
            # 如果使用用户定义符号失败，尝试不使用符号重新训练
            if use_user_symbols and '--user_defined_symbols' in training_args:
                logger.info("尝试不使用用户定义符号重新训练...")
                del training_args['--user_defined_symbols']
                try:
                    args = ' '.join([f'{k}={v}' for k, v in training_args.items()])
                    spm.SentencePieceTrainer.train(args)

                    training_time = time.time() - start_time
                    logger.info(f"分词器训练完成! 耗时: {training_time:.2f}秒")
                    logger.info(f"模型文件: {model_path}")
                    logger.info(f"词汇表文件: {vocab_path}")

                    return str(model_path)
                except Exception as e2:
                    logger.error(f"重新训练也失败: {e2}")
                    raise e2
            else:
                raise

    def evaluate_tokenizer(self, model_path: str, test_texts: Optional[List[str]] = None) -> Dict:
        """评估分词器性能"""
        logger.info("开始评估分词器...")

        # 加载分词器
        sp = spm.SentencePieceProcessor()
        sp.load(model_path)

        # 默认测试文本
        if test_texts is None:
            test_texts = [
                "BTC价格突破历史新高，市场看多情绪高涨",
                "ETH 2.0质押量持续增长，DeFi生态繁荣发展",
                "某交易所推出创新型NFT交易平台",
                "比特币合约未平仓头寸降至570亿美元",
                "Binance将上线新的代币交易对",
                "去中心化金融协议完成3000万美元融资",
                "USDT市值突破1000亿美元大关",
                "以太坊网络手续费大幅下降",
                "加密货币市场总市值创历史新高",
                "某DeFi协议遭受闪电贷攻击损失500万美元"
            ]

        results = {
            'vocab_size': sp.vocab_size(),
            'test_results': [],
            'avg_tokens_per_text': 0,
            'avg_compression_ratio': 0,
            'unk_rate': 0
        }

        total_tokens = 0
        total_chars = 0
        total_unks = 0

        for text in test_texts:
            # 使用相同的文本处理器处理测试文本
            processed_text = self.clean_text(text)

            tokens = sp.encode_as_pieces(processed_text)
            token_ids = sp.encode_as_ids(processed_text)

            # 计算统计信息
            num_tokens = len(tokens)
            num_chars = len(text)  # 使用原始文本长度计算压缩比
            num_unks = sum(1 for token_id in token_ids if token_id == sp.unk_id())
            compression_ratio = num_chars / num_tokens if num_tokens > 0 else 0

            total_tokens += num_tokens
            total_chars += num_chars
            total_unks += num_unks

            results['test_results'].append({
                'original_text': text,
                'processed_text': processed_text,
                'tokens': tokens,
                'num_tokens': num_tokens,
                'num_chars': num_chars,
                'compression_ratio': compression_ratio,
                'unk_count': num_unks
            })

        # 计算平均值
        num_texts = len(test_texts)
        results['avg_tokens_per_text'] = total_tokens / num_texts
        results['avg_compression_ratio'] = total_chars / total_tokens if total_tokens > 0 else 0
        results['unk_rate'] = total_unks / total_tokens if total_tokens > 0 else 0

        logger.info(f"评估完成:")
        logger.info(f"  - 词汇表大小: {results['vocab_size']}")
        logger.info(f"  - 平均每文本token数: {results['avg_tokens_per_text']:.1f}")
        logger.info(f"  - 平均压缩比: {results['avg_compression_ratio']:.2f}")
        logger.info(f"  - UNK率: {results['unk_rate']:.3f}")

        return results

    def save_evaluation_report(self, results: Dict, output_file: str = None):
        """保存评估报告"""
        if output_file is None:
            output_file = self.models_dir / 'evaluation_report.json'

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        logger.info(f"评估报告已保存到: {output_file}")

    def analyze_tokenization_quality(self, model_path: str, sample_texts: List[str] = None) -> Dict:
        """深度分析分词质量"""
        logger.info("开始深度分析分词质量...")

        # 加载分词器
        sp = spm.SentencePieceProcessor()
        sp.load(model_path)

        if sample_texts is None:
            # 使用更全面的测试文本
            sample_texts = [
                # 基础加密货币新闻
                "BTC价格突破历史新高，市场看多情绪高涨",
                "ETH 2.0质押量持续增长，DeFi生态繁荣发展",
                "某交易所推出创新型NFT交易平台",

                # 技术术语密集文本
                "Uniswap V3引入集中流动性机制，AMM协议迎来重大升级",
                "Layer2解决方案Arbitrum和Optimism竞争激烈，ZK-Rollup技术备受关注",
                "MakerDAO治理代币MKR价格上涨，DeFi协议TVL创新高",

                # 中英混合复杂文本
                "Binance宣布支持Ethereum Shanghai升级，ETH质押提取功能正式上线",
                "Polygon推出zkEVM测试网，与Ethereum兼容的零知识证明技术获得突破",
                "Chainlink预言机网络集成Avalanche，为DeFi应用提供可靠数据源",

                # 市场分析文本
                "比特币合约未平仓头寸降至570亿美元，多空比例趋于平衡",
                "以太坊网络手续费大幅下降，Layer2采用率持续提升",
                "USDT市值突破1000亿美元大关，稳定币市场竞争加剧",

                # 新兴概念文本
                "AI代币板块表现强劲，ChatGPT概念币种普遍上涨",
                "RWA（现实世界资产）代币化趋势明显，传统金融与DeFi融合加速",
                "Restaking协议EigenLayer获得巨额融资，以太坊生态安全性进一步提升"
            ]

        analysis_results = {
            'model_info': {
                'vocab_size': sp.vocab_size(),
                'model_path': model_path
            },
            'detailed_analysis': [],
            'quality_metrics': {},
            'symbol_coverage': {},
            'recommendations': []
        }

        total_tokens = 0
        total_chars = 0
        total_unks = 0
        crypto_terms_found = 0
        crypto_terms_total = 0

        # 定义关键加密货币术语（英文转为小写以匹配处理后的文本）
        key_crypto_terms_original = [
            'BTC', 'ETH', 'DeFi', 'NFT', 'Uniswap', 'AMM', 'Layer2', 'Arbitrum',
            'Optimistic', 'ZK-Rollup', 'MakerDAO', 'TVL', 'Binance', 'Ethereum',
            'Polygon', 'zkEVM', 'Chainlink', 'Avalanche', 'USDT', 'AI', 'RWA',
            'Restaking', 'EigenLayer', '比特币', '以太坊', '去中心化', '稳定币',
            '流动性', '治理代币', '预言机', '零知识证明'
        ]

        # 将英文术语转换为小写，中文术语保持不变
        key_crypto_terms = []
        for term in key_crypto_terms_original:
            if re.search(r'[a-zA-Z]', term):  # 包含英文字符
                key_crypto_terms.append(term.lower())
            else:  # 纯中文术语
                key_crypto_terms.append(term)

        for text in sample_texts:
            # 使用相同的文本处理器处理测试文本
            processed_text = self.clean_text(text)

            tokens = sp.encode_as_pieces(processed_text)
            token_ids = sp.encode_as_ids(processed_text)

            # 基础统计
            num_tokens = len(tokens)
            num_chars = len(text)  # 使用原始文本长度计算压缩比
            num_unks = sum(1 for token_id in token_ids if token_id == sp.unk_id())
            compression_ratio = num_chars / num_tokens if num_tokens > 0 else 0

            # 分析加密货币术语覆盖（在处理后的文本中查找）
            terms_in_text = [term for term in key_crypto_terms if term in processed_text]
            crypto_terms_total += len(terms_in_text)

            # 检查术语是否被正确分词（不被分割）
            terms_correctly_tokenized = 0
            for term in terms_in_text:
                # 检查术语是否作为完整token出现
                if any(term in token for token in tokens):
                    terms_correctly_tokenized += 1

            crypto_terms_found += terms_correctly_tokenized

            total_tokens += num_tokens
            total_chars += num_chars
            total_unks += num_unks

            analysis_results['detailed_analysis'].append({
                'original_text': text,
                'processed_text': processed_text,
                'tokens': tokens,
                'num_tokens': num_tokens,
                'num_chars': num_chars,
                'compression_ratio': compression_ratio,
                'unk_count': num_unks,
                'unk_rate': num_unks / num_tokens if num_tokens > 0 else 0,
                'crypto_terms': terms_in_text,
                'terms_correctly_tokenized': terms_correctly_tokenized
            })

        # 计算整体质量指标
        num_texts = len(sample_texts)
        analysis_results['quality_metrics'] = {
            'avg_tokens_per_text': total_tokens / num_texts,
            'avg_compression_ratio': total_chars / total_tokens if total_tokens > 0 else 0,
            'overall_unk_rate': total_unks / total_tokens if total_tokens > 0 else 0,
            'crypto_term_coverage': crypto_terms_found / crypto_terms_total if crypto_terms_total > 0 else 0
        }

        # 生成建议
        recommendations = []
        if analysis_results['quality_metrics']['overall_unk_rate'] > 0.05:
            recommendations.append("UNK率较高，建议增加词汇表大小或调整字符覆盖率")

        if analysis_results['quality_metrics']['crypto_term_coverage'] < 0.8:
            recommendations.append("加密货币术语覆盖率较低，建议增加更多领域特定词汇")

        if analysis_results['quality_metrics']['avg_compression_ratio'] < 2.0:
            recommendations.append("压缩比较低，可能需要优化分词策略")

        analysis_results['recommendations'] = recommendations

        logger.info("分词质量分析完成:")
        logger.info(f"  平均压缩比: {analysis_results['quality_metrics']['avg_compression_ratio']:.2f}")
        logger.info(f"  整体UNK率: {analysis_results['quality_metrics']['overall_unk_rate']:.3f}")
        logger.info(f"  加密术语覆盖率: {analysis_results['quality_metrics']['crypto_term_coverage']:.3f}")

        return analysis_results

def load_news_data(data_dir: str = 'news_data', use_filtered: bool = True) -> pd.DataFrame:
    """加载新闻数据"""
    # 优先使用过滤后的数据
    if use_filtered and os.path.exists('filter_news'):
        data_dir = 'filter_news'
        logger.info("使用过滤后的新闻数据")
        all_news_files = [
        os.path.join(data_dir, f)
        for f in os.listdir(data_dir)
        if f.endswith('_filtered.csv')
     ]
    else:
        all_news_files = [
            os.path.join(data_dir, f)
            for f in os.listdir(data_dir)
            if f.endswith('.csv')
        ]

    if not all_news_files:
        raise FileNotFoundError(f"未找到任何新闻CSV文件在 '{data_dir}' 目录下")

    logger.info(f"找到 {len(all_news_files)} 个数据文件")

    news_dfs = []
    failed_files = []

    for file_path in all_news_files:
        try:
            df = pd.read_csv(file_path)
            if not df.empty:
                news_dfs.append(df)
                logger.debug(f"成功读取文件: {file_path} ({len(df)} 条记录)")
        except Exception as e:
            logger.warning(f"读取文件失败 {file_path}: {e}")
            failed_files.append(file_path)
            continue

    if not news_dfs:
        raise ValueError("没有成功读取任何新闻数据")

    if failed_files:
        logger.warning(f"有 {len(failed_files)} 个文件读取失败")

    news_df = pd.concat(news_dfs, ignore_index=True)
    logger.info(f"成功合并 {len(news_dfs)} 个文件，总计 {len(news_df)} 条新闻数据")

    return news_df

def main(config_name: str = 'medium', use_filtered_data: bool = True, force_retrain: bool = False,
         use_user_symbols: bool = True, symbol_min_freq: int = 2):
    """
    主函数

    Args:
        config_name: 配置名称 ('small', 'medium', 'large', 'bpe_medium')
        use_filtered_data: 是否使用过滤后的数据
        force_retrain: 是否强制重新训练
        use_user_symbols: 是否使用用户定义符号
        symbol_min_freq: 符号最小频率阈值
    """
    logger.info("开始训练加密货币新闻分词器")
    logger.info(f"使用配置: {config_name}")
    logger.info(f"使用用户定义符号: {use_user_symbols}")
    if use_user_symbols:
        logger.info(f"符号最小频率阈值: {symbol_min_freq}")

    try:
        # 显示可用配置
        if config_name == 'list':
            TokenizerConfig.list_configs()
            return

        # 获取配置
        config = TokenizerConfig.get_config(config_name)
        logger.info(f"配置详情: {config['description']}")

        # 加载新闻数据
        news_df = load_news_data(use_filtered=use_filtered_data)

        # 创建训练器
        trainer = TokenizerTrainer(
            vocab_size=config['vocab_size'],
            model_type=config['model_type'],
            character_coverage=config['character_coverage']
        )

        # 准备训练数据
        train_file = trainer.prepare_training_data(news_df, use_cache=not force_retrain)

        # 如果使用用户定义符号，先进行符号频率分析
        if use_user_symbols:
            logger.info("开始符号频率分析...")
            # 注意：get_user_defined_symbols方法不需要参数，它会自动处理所有符号

        # 训练分词器
        model_prefix = f'crypto_tokenizer_{config_name}'
        if use_user_symbols:
            model_prefix += '_with_symbols'

        model_path = trainer.train(train_file, model_prefix=model_prefix, use_user_symbols=use_user_symbols)

        # 评估分词器
        evaluation_results = trainer.evaluate_tokenizer(model_path)

        # 进行深度质量分析
        quality_analysis = trainer.analyze_tokenization_quality(model_path)

        # 添加训练配置信息到评估结果
        evaluation_results['training_config'] = {
            'config_name': config_name,
            'use_user_symbols': use_user_symbols,
            'symbol_min_freq': symbol_min_freq if use_user_symbols else None,
            'vocab_size': config['vocab_size'],
            'model_type': config['model_type'],
            'character_coverage': config['character_coverage']
        }

        # 合并质量分析结果
        evaluation_results['quality_analysis'] = quality_analysis

        # 保存评估报告
        report_file = trainer.models_dir / f'evaluation_report_{model_prefix}.json'
        trainer.save_evaluation_report(evaluation_results, str(report_file))

        # 显示详细的测试结果
        print("\n" + "="*80)
        print("分词器训练完成！详细测试结果:")
        print("="*80)
        print(f"配置: {config_name} ({'包含用户符号' if use_user_symbols else '不包含用户符号'})")
        print(f"模型文件: {model_path}")

        for i, result in enumerate(evaluation_results['test_results'], 1):
            print(f"\n测试 {i}:")
            print(f"原文: {result['original_text']}")
            if 'processed_text' in result:
                print(f"处理后: {result['processed_text']}")
            print(f"分词: {result['tokens']}")
            print(f"Token数: {result['num_tokens']}, 字符数: {result['num_chars']}")
            print(f"压缩比: {result['compression_ratio']:.2f}, UNK数: {result['unk_count']}")

        print(f"\n总体统计:")
        print(f"词汇表大小: {evaluation_results['vocab_size']}")
        print(f"平均每文本token数: {evaluation_results['avg_tokens_per_text']:.1f}")
        print(f"平均压缩比: {evaluation_results['avg_compression_ratio']:.2f}")
        print(f"UNK率: {evaluation_results['unk_rate']:.3f}")

        # 显示质量分析结果
        if 'quality_analysis' in evaluation_results:
            qa = evaluation_results['quality_analysis']['quality_metrics']
            print(f"\n质量分析:")
            print(f"深度分析压缩比: {qa['avg_compression_ratio']:.2f}")
            print(f"深度分析UNK率: {qa['overall_unk_rate']:.3f}")
            print(f"加密术语覆盖率: {qa['crypto_term_coverage']:.3f}")

            # 显示建议
            recommendations = evaluation_results['quality_analysis']['recommendations']
            if recommendations:
                print(f"\n优化建议:")
                for i, rec in enumerate(recommendations, 1):
                    print(f"  {i}. {rec}")

        # 如果使用了用户定义符号，显示符号统计
        if use_user_symbols:
            symbols_file = trainer.data_dir / 'user_defined_symbols.txt'
            if symbols_file.exists():
                with open(symbols_file, 'r', encoding='utf-8') as f:
                    symbol_count = len(f.readlines())
                print(f"\n符号统计:")
                print(f"使用的用户定义符号数量: {symbol_count}")

        print(f"\n报告文件: {report_file}")
        logger.info("分词器训练和评估完成!")

    except Exception as e:
        logger.error(f"训练过程中发生错误: {e}")
        raise

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='训练加密货币新闻分词器')
    parser.add_argument('--config', '-c', default='medium',
                       help='配置名称 (small/medium/large/bpe_medium/list)')
    parser.add_argument('--no-filtered', action='store_false',
                       help='使用原始数据而不是过滤后的数据')
    parser.add_argument('--force-retrain', action='store_true',
                       help='强制重新训练，不使用缓存')
    parser.add_argument('--no-user-symbols', action='store_true',
                       help='不使用用户定义符号')
    parser.add_argument('--symbol-min-freq', type=int, default=2,
                       help='符号最小频率阈值 (默认: 2)')

    args = parser.parse_args()

    main(
        config_name=args.config,
        use_filtered_data=True,
        force_retrain=args.force_retrain,
        use_user_symbols=not args.no_user_symbols,
        symbol_min_freq=args.symbol_min_freq
    )
