#!/usr/bin/env python3
"""
新闻情感分析机器学习训练流程

功能：
1. 使用CryptoNewsTextProcessor预处理文本
2. 构建TF-IDF特征
3. 结合技术指标
4. 将收益率划分为5个等级
5. 训练和评估多种传统机器学习模型

作者：AI Assistant
日期：2025-07-27
"""

import pandas as pd
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.pipeline import Pipeline
from sklearn.compose import ColumnTransformer
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# 导入文本处理器
from crypto_text_processor import CryptoNewsTextProcessor


class NewsMLTrainingPipeline:
    """新闻机器学习训练流程"""
    
    def __init__(self, data_file='news_training_samples_complete_all.csv'):
        self.data_file = data_file
        self.text_processor = CryptoNewsTextProcessor()
        self.models = {}
        self.vectorizer = None
        self.scaler = None
        self.label_encoder = None
        
    def load_and_preprocess_data(self):
        """加载和预处理数据"""
        print("=== 加载和预处理数据 ===")
        
        # 加载数据
        df = pd.read_csv(self.data_file)
        print(f"📊 原始数据量: {len(df):,} 条")
        
        # 过滤掉占位符数据
        df = df[df['label'] != 'TBD'].copy()
        print(f"📊 有效数据量: {len(df):,} 条")
        
        if len(df) == 0:
            raise ValueError("没有有效的训练数据！请确保数据文件包含真实的标签数据。")
        
        # 数据类型转换
        numeric_columns = ['close', 'volume', 'relative_price', 'label', 'natr', 'mfi', 'rsi', 'btc_natr', 'btc_mfi', 'btc_rsi']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # 移除缺失值
        df = df.dropna(subset=['news_title', 'news_content', 'label'])
        print(f"📊 清洗后数据量: {len(df):,} 条")
        
        return df
    
    def create_return_categories(self, df):
        """将收益率划分为5个等级"""
        print("\n=== 创建收益率分类 ===")
        
        # 计算收益率分位数（使用label列）
        returns = df['label'].values
        quantiles = np.percentile(returns, [20, 40, 60, 80])
        
        print(f"📈 收益率分位数:")
        print(f"  20%: {quantiles[0]:.4f}")
        print(f"  40%: {quantiles[1]:.4f}")
        print(f"  60%: {quantiles[2]:.4f}")
        print(f"  80%: {quantiles[3]:.4f}")
        
        # 创建5个等级
        def categorize_return(ret):
            if ret <= quantiles[0]:
                return 0  # 大跌
            elif ret <= quantiles[1]:
                return 1  # 小跌
            elif ret <= quantiles[2]:
                return 2  # 平稳
            elif ret <= quantiles[3]:
                return 3  # 小涨
            else:
                return 4  # 大涨
        
        df['return_category'] = df['label'].apply(categorize_return)
        
        # 统计各类别分布
        category_counts = df['return_category'].value_counts().sort_index()
        category_names = ['大跌', '小跌', '平稳', '小涨', '大涨']
        
        print(f"\n📊 收益率分类分布:")
        for i, (cat, count) in enumerate(category_counts.items()):
            print(f"  {cat} ({category_names[cat]}): {count:,} 条 ({count/len(df)*100:.1f}%)")
        
        return df
    
    def preprocess_text_features(self, df):
        """预处理文本特征"""
        print("\n=== 预处理文本特征 ===")
        
        # 合并标题和内容
        df['combined_text'] = df['news_title'].fillna('') + ' ' + df['news_content'].fillna('')
        
        # 使用CryptoNewsTextProcessor处理文本
        print("🔄 处理文本...")
        processed_texts = []
        for text in df['combined_text']:
            processed_text = self.text_processor.process(text)
            processed_texts.append(processed_text)
        
        df['processed_text'] = processed_texts
        
        # 过滤掉空文本
        df = df[df['processed_text'].str.len() > 10].copy()
        print(f"📊 文本处理后数据量: {len(df):,} 条")
        
        return df
    
    def create_features(self, df):
        """创建特征"""
        print("\n=== 创建特征 ===")
        
        # 1. TF-IDF特征
        print("🔄 创建TF-IDF特征...")
        self.vectorizer = TfidfVectorizer(
            max_features=1000,
            min_df=2,
            max_df=0.8,
            ngram_range=(1, 2),
            stop_words=None  # 已经在文本处理器中处理了停用词
        )
        
        tfidf_features = self.vectorizer.fit_transform(df['processed_text'])
        tfidf_df = pd.DataFrame(
            tfidf_features.toarray(),
            columns=[f'tfidf_{i}' for i in range(tfidf_features.shape[1])],
            index=df.index
        )
        
        # 2. 技术指标特征
        print("🔄 创建技术指标特征...")
        technical_features = df[['natr', 'mfi', 'rsi', 'btc_natr', 'btc_mfi', 'btc_rsi']].copy()
        
        # 3. 其他特征
        print("🔄 创建其他特征...")
        other_features = pd.DataFrame(index=df.index)
        
        # 文本长度特征
        other_features['text_length'] = df['processed_text'].str.len()
        other_features['title_length'] = df['news_title'].str.len()
        
        # 时间特征
        df['news_time_utc0'] = pd.to_datetime(df['news_time_utc0'])
        other_features['hour'] = df['news_time_utc0'].dt.hour
        other_features['day_of_week'] = df['news_time_utc0'].dt.dayofweek
        
        # 交易对特征（编码）
        self.label_encoder = LabelEncoder()
        other_features['symbol_encoded'] = self.label_encoder.fit_transform(df['symbol'])
        
        # 合并所有特征
        features = pd.concat([tfidf_df, technical_features, other_features], axis=1)
        
        print(f"📊 特征维度: {features.shape}")
        print(f"  - TF-IDF特征: {tfidf_df.shape[1]}")
        print(f"  - 技术指标特征: {technical_features.shape[1]}")
        print(f"  - 其他特征: {other_features.shape[1]}")
        
        return features, df['return_category']
    
    def train_models(self, X, y):
        """训练多种模型"""
        print("\n=== 训练模型 ===")
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        print(f"📊 训练集: {X_train.shape[0]:,} 条")
        print(f"📊 测试集: {X_test.shape[0]:,} 条")
        
        # 标准化特征
        self.scaler = StandardScaler()
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # 定义模型
        models_config = {
            'RandomForest': RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1),
            'GradientBoosting': GradientBoostingClassifier(n_estimators=100, random_state=42),
            'LogisticRegression': LogisticRegression(random_state=42, max_iter=1000),
            'SVM': SVC(random_state=42, probability=True)
        }
        
        results = {}
        
        for name, model in models_config.items():
            print(f"\n🔄 训练 {name}...")
            
            # 对于需要标准化的模型使用标准化数据
            if name in ['LogisticRegression', 'SVM']:
                X_train_model = X_train_scaled
                X_test_model = X_test_scaled
            else:
                X_train_model = X_train
                X_test_model = X_test
            
            # 训练模型
            model.fit(X_train_model, y_train)
            
            # 预测
            y_pred = model.predict(X_test_model)
            
            # 评估
            accuracy = accuracy_score(y_test, y_pred)
            
            # 交叉验证
            if name in ['LogisticRegression', 'SVM']:
                cv_scores = cross_val_score(model, X_train_scaled, y_train, cv=5)
            else:
                cv_scores = cross_val_score(model, X_train, y_train, cv=5)
            
            results[name] = {
                'model': model,
                'accuracy': accuracy,
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std(),
                'y_pred': y_pred,
                'y_test': y_test
            }
            
            print(f"  ✅ 准确率: {accuracy:.4f}")
            print(f"  ✅ 交叉验证: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
        
        self.models = results
        return results
    
    def evaluate_models(self):
        """评估模型"""
        print("\n=== 模型评估 ===")
        
        category_names = ['大跌', '小跌', '平稳', '小涨', '大涨']
        
        for name, result in self.models.items():
            print(f"\n📊 {name} 详细评估:")
            print(f"准确率: {result['accuracy']:.4f}")
            print(f"交叉验证: {result['cv_mean']:.4f} ± {result['cv_std']:.4f}")
            
            # 分类报告
            print("\n分类报告:")
            print(classification_report(
                result['y_test'], 
                result['y_pred'],
                target_names=category_names
            ))
    
    def plot_results(self):
        """绘制结果"""
        print("\n=== 绘制结果 ===")
        
        # 模型性能比较
        model_names = list(self.models.keys())
        accuracies = [self.models[name]['accuracy'] for name in model_names]
        cv_means = [self.models[name]['cv_mean'] for name in model_names]
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 准确率比较
        ax1.bar(model_names, accuracies)
        ax1.set_title('模型准确率比较')
        ax1.set_ylabel('准确率')
        ax1.set_ylim(0, 1)
        for i, v in enumerate(accuracies):
            ax1.text(i, v + 0.01, f'{v:.3f}', ha='center')
        
        # 交叉验证比较
        ax2.bar(model_names, cv_means)
        ax2.set_title('交叉验证准确率比较')
        ax2.set_ylabel('交叉验证准确率')
        ax2.set_ylim(0, 1)
        for i, v in enumerate(cv_means):
            ax2.text(i, v + 0.01, f'{v:.3f}', ha='center')
        
        plt.tight_layout()
        plt.savefig('model_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # 最佳模型的混淆矩阵
        best_model_name = max(self.models.keys(), key=lambda x: self.models[x]['accuracy'])
        best_result = self.models[best_model_name]
        
        plt.figure(figsize=(10, 8))
        cm = confusion_matrix(best_result['y_test'], best_result['y_pred'])
        category_names = ['大跌', '小跌', '平稳', '小涨', '大涨']
        
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=category_names, yticklabels=category_names)
        plt.title(f'{best_model_name} 混淆矩阵')
        plt.ylabel('真实标签')
        plt.xlabel('预测标签')
        plt.savefig('confusion_matrix.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"🏆 最佳模型: {best_model_name} (准确率: {best_result['accuracy']:.4f})")
    
    def run_pipeline(self):
        """运行完整的训练流程"""
        print("🚀 开始新闻情感分析机器学习训练流程")
        
        try:
            # 1. 加载和预处理数据
            df = self.load_and_preprocess_data()
            
            # 2. 创建收益率分类
            df = self.create_return_categories(df)
            
            # 3. 预处理文本特征
            df = self.preprocess_text_features(df)
            
            # 4. 创建特征
            X, y = self.create_features(df)
            
            # 5. 训练模型
            results = self.train_models(X, y)
            
            # 6. 评估模型
            self.evaluate_models()
            
            # 7. 绘制结果
            self.plot_results()
            
            print("\n✅ 训练流程完成！")
            
        except Exception as e:
            print(f"❌ 训练过程中出现错误: {e}")
            raise


def main():
    """主函数"""
    pipeline = NewsMLTrainingPipeline()
    pipeline.run_pipeline()


if __name__ == "__main__":
    main()
