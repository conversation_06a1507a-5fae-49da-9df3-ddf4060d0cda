#!/usr/bin/env python3
"""
高级机器学习方法对比测试

包含：
1. jieba分词 + TF-IDF
2. 自训练SentencePiece + TF-IDF  
3. Word2Vec
4. GloVe
5. EventHAN (Hierarchical Attention Network for Events)
6. WordHAN (Word-level Hierarchical Attention Network)

作者：AI Assistant
日期：2025-07-27
"""

import pandas as pd
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import accuracy_score, classification_report
from sklearn.preprocessing import StandardScaler
import os
import warnings
warnings.filterwarnings('ignore')


class AdvancedMLComparison:
    """高级机器学习方法对比"""
    
    def __init__(self, data_file='news_training_samples_complete_all.csv'):
        self.data_file = data_file
        self.results = {}
        
    def load_and_prepare_data(self):
        """加载和准备数据"""
        print("=== 加载和准备数据 ===")
        
        df = pd.read_csv(self.data_file)
        print(f"原始数据: {len(df)} 条")
        
        # 过滤有效数据
        df = df[df['label'] != 'TBD'].copy()
        df = df.dropna(subset=['news_title', 'news_content', 'label'])
        
        # 数据类型转换
        numeric_columns = ['label', 'natr', 'mfi', 'rsi', 'btc_natr', 'btc_mfi', 'btc_rsi']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        df = df.dropna(subset=numeric_columns)
        print(f"有效数据: {len(df)} 条")
        
        # 创建5级分类
        returns = df['label'].values
        quantiles = np.percentile(returns, [20, 40, 60, 80])
        
        def categorize_return(ret):
            if ret <= quantiles[0]:
                return 0  # 大跌
            elif ret <= quantiles[1]:
                return 1  # 小跌
            elif ret <= quantiles[2]:
                return 2  # 平稳
            elif ret <= quantiles[3]:
                return 3  # 小涨
            else:
                return 4  # 大涨
        
        df['return_category'] = df['label'].apply(categorize_return)
        
        # 准备文本
        df['combined_text'] = df['news_title'].fillna('') + ' ' + df['news_content'].fillna('')
        
        # 技术指标特征
        technical_features = df[['natr', 'mfi', 'rsi', 'btc_natr', 'btc_mfi', 'btc_rsi']].values
        
        print(f"分类分布: {df['return_category'].value_counts().sort_index().to_dict()}")
        
        return df, technical_features
    
    def method_1_jieba_tfidf(self, texts, y, technical_features):
        """方法1: jieba分词 + TF-IDF"""
        print("\n=== 方法1: jieba分词 + TF-IDF ===")
        
        try:
            import jieba
            
            def jieba_tokenize(text):
                return ' '.join(jieba.cut(text))
            
            print("进行jieba分词...")
            tokenized_texts = [jieba_tokenize(text) for text in texts]
            
            # TF-IDF特征
            vectorizer = TfidfVectorizer(max_features=800, min_df=2, max_df=0.8, ngram_range=(1, 2))
            X_text = vectorizer.fit_transform(tokenized_texts)
            
            # 合并特征
            X = np.hstack([X_text.toarray(), technical_features])
            
            return self._train_and_evaluate(X, y, "jieba_tfidf")
            
        except ImportError:
            print("jieba未安装，跳过")
            return None
    
    def method_2_sentencepiece_tfidf(self, texts, y, technical_features):
        """方法2: 自训练SentencePiece + TF-IDF"""
        print("\n=== 方法2: 自训练SentencePiece + TF-IDF ===")
        
        try:
            import sentencepiece as spm
            
            model_path = "models/crypto_tokenizer_simple.model"
            if not os.path.exists(model_path):
                print("SentencePiece模型不存在，跳过")
                return None
            
            # 加载模型
            sp = spm.SentencePieceProcessor()
            sp.load(model_path)
            
            def sp_tokenize(text):
                return ' '.join(sp.encode_as_pieces(text))
            
            print("进行SentencePiece分词...")
            tokenized_texts = [sp_tokenize(text) for text in texts]
            
            # TF-IDF特征
            vectorizer = TfidfVectorizer(max_features=800, min_df=2, max_df=0.8, ngram_range=(1, 2))
            X_text = vectorizer.fit_transform(tokenized_texts)
            
            # 合并特征
            X = np.hstack([X_text.toarray(), technical_features])
            
            return self._train_and_evaluate(X, y, "sentencepiece_tfidf")
            
        except ImportError:
            print("sentencepiece未安装，跳过")
            return None
    
    def method_3_word2vec(self, texts, y, technical_features):
        """方法3: Word2Vec"""
        print("\n=== 方法3: Word2Vec ===")
        
        try:
            from gensim.models import Word2Vec
            from gensim.utils import simple_preprocess
            
            # 预处理文本
            sentences = [simple_preprocess(text) for text in texts]
            
            print("训练Word2Vec模型...")
            model = Word2Vec(
                sentences=sentences,
                vector_size=100,
                window=5,
                min_count=2,
                workers=4,
                epochs=10
            )
            
            # 创建文档向量
            def get_doc_vector(words):
                vectors = []
                for word in words:
                    if word in model.wv:
                        vectors.append(model.wv[word])
                
                if vectors:
                    return np.mean(vectors, axis=0)
                else:
                    return np.zeros(model.vector_size)
            
            print("生成文档向量...")
            X_text = np.array([get_doc_vector(simple_preprocess(text)) for text in texts])
            
            # 合并特征
            X = np.hstack([X_text, technical_features])
            
            return self._train_and_evaluate(X, y, "word2vec")
            
        except ImportError:
            print("gensim未安装，跳过")
            return None
    
    def method_4_glove(self, texts, y, technical_features):
        """方法4: GloVe (使用预训练模型或简化实现)"""
        print("\n=== 方法4: GloVe (简化实现) ===")
        
        try:
            from sklearn.feature_extraction.text import CountVectorizer
            from sklearn.decomposition import TruncatedSVD
            
            # 简化的GloVe实现：使用共现矩阵 + SVD
            print("构建共现矩阵...")
            vectorizer = CountVectorizer(max_features=1000, min_df=2, max_df=0.8)
            X_counts = vectorizer.fit_transform(texts)
            
            # 使用SVD降维模拟GloVe
            print("SVD降维...")
            svd = TruncatedSVD(n_components=100, random_state=42)
            X_text = svd.fit_transform(X_counts)
            
            # 合并特征
            X = np.hstack([X_text, technical_features])
            
            return self._train_and_evaluate(X, y, "glove_simplified")
            
        except Exception as e:
            print(f"GloVe实现失败: {e}")
            return None
    
    def method_5_event_han(self, texts, y, technical_features):
        """方法5: EventHAN (简化实现)"""
        print("\n=== 方法5: EventHAN (简化实现) ===")
        
        try:
            from sklearn.feature_extraction.text import TfidfVectorizer
            from sklearn.decomposition import LatentDirichletAllocation
            
            # 简化的EventHAN：事件级别的注意力机制
            # 使用LDA主题模型模拟事件表示
            print("构建事件表示...")
            
            # 分句处理（简化）
            event_texts = []
            for text in texts:
                sentences = text.split('。')  # 简单分句
                event_texts.extend([s.strip() for s in sentences if len(s.strip()) > 5])
            
            # TF-IDF + LDA
            vectorizer = TfidfVectorizer(max_features=500, min_df=2, max_df=0.8)
            X_tfidf = vectorizer.fit_transform(event_texts)
            
            # LDA主题模型
            lda = LatentDirichletAllocation(n_components=20, random_state=42)
            X_topics = lda.fit_transform(X_tfidf)
            
            # 聚合回文档级别（简化的注意力机制）
            doc_features = []
            start_idx = 0
            for text in texts:
                sentences = text.split('。')
                valid_sentences = [s.strip() for s in sentences if len(s.strip()) > 5]
                end_idx = start_idx + len(valid_sentences)
                
                if end_idx > start_idx:
                    # 简单平均作为注意力聚合
                    doc_feature = np.mean(X_topics[start_idx:end_idx], axis=0)
                else:
                    doc_feature = np.zeros(20)
                
                doc_features.append(doc_feature)
                start_idx = end_idx
            
            X_text = np.array(doc_features)
            
            # 合并特征
            X = np.hstack([X_text, technical_features])
            
            return self._train_and_evaluate(X, y, "event_han")
            
        except Exception as e:
            print(f"EventHAN实现失败: {e}")
            return None
    
    def method_6_word_han(self, texts, y, technical_features):
        """方法6: WordHAN (简化实现)"""
        print("\n=== 方法6: WordHAN (简化实现) ===")
        
        try:
            from sklearn.feature_extraction.text import TfidfVectorizer
            
            # 简化的WordHAN：词级别的注意力机制
            print("构建词级注意力表示...")
            
            # 使用TF-IDF权重作为注意力权重
            vectorizer = TfidfVectorizer(max_features=800, min_df=2, max_df=0.8)
            X_tfidf = vectorizer.fit_transform(texts)
            
            # 计算注意力权重（基于TF-IDF分数）
            tfidf_array = X_tfidf.toarray()
            
            # 应用注意力机制（加权平均）
            attention_weights = np.exp(tfidf_array) / (np.sum(np.exp(tfidf_array), axis=1, keepdims=True) + 1e-8)
            X_text = tfidf_array * attention_weights
            
            # 降维
            from sklearn.decomposition import PCA
            pca = PCA(n_components=100, random_state=42)
            X_text = pca.fit_transform(X_text)
            
            # 合并特征
            X = np.hstack([X_text, technical_features])
            
            return self._train_and_evaluate(X, y, "word_han")
            
        except Exception as e:
            print(f"WordHAN实现失败: {e}")
            return None
    
    def _train_and_evaluate(self, X, y, method_name):
        """训练和评估模型"""
        print(f"训练和评估 {method_name}...")
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.3, random_state=42, stratify=y
        )
        
        # 模型（跳过SVM）
        models = {
            'RandomForest': RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1),
            'GradientBoosting': GradientBoostingClassifier(n_estimators=100, random_state=42),
            'LogisticRegression': LogisticRegression(random_state=42, max_iter=1000)
        }
        
        results = {}
        
        for model_name, model in models.items():
            # 对逻辑回归进行标准化
            if model_name == 'LogisticRegression':
                scaler = StandardScaler()
                X_train_scaled = scaler.fit_transform(X_train)
                X_test_scaled = scaler.transform(X_test)
                
                model.fit(X_train_scaled, y_train)
                y_pred = model.predict(X_test_scaled)
                cv_scores = cross_val_score(model, X_train_scaled, y_train, cv=3)
            else:
                model.fit(X_train, y_train)
                y_pred = model.predict(X_test)
                cv_scores = cross_val_score(model, X_train, y_train, cv=3)
            
            accuracy = accuracy_score(y_test, y_pred)
            
            results[model_name] = {
                'accuracy': accuracy,
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std()
            }
            
            print(f"  {model_name}: 准确率={accuracy:.4f}, CV={cv_scores.mean():.4f}±{cv_scores.std():.4f}")
        
        return results
    
    def run_comparison(self):
        """运行完整对比"""
        print("🚀 开始高级机器学习方法对比")
        
        # 加载数据
        df, technical_features = self.load_and_prepare_data()
        
        # 取样本数据以加快测试
        sample_size = min(2000, len(df))
        sample_df = df.sample(sample_size, random_state=42)
        texts = sample_df['combined_text'].values
        y = sample_df['return_category'].values
        tech_features = technical_features[sample_df.index]
        
        print(f"\n使用 {sample_size} 条样本进行对比测试")
        
        # 测试各种方法
        methods = [
            ('jieba_tfidf', self.method_1_jieba_tfidf),
            ('sentencepiece_tfidf', self.method_2_sentencepiece_tfidf),
            ('word2vec', self.method_3_word2vec),
            ('glove', self.method_4_glove),
            ('event_han', self.method_5_event_han),
            ('word_han', self.method_6_word_han)
        ]
        
        all_results = {}
        
        for method_name, method_func in methods:
            try:
                results = method_func(texts, y, tech_features)
                if results:
                    all_results[method_name] = results
            except Exception as e:
                print(f"❌ {method_name} 失败: {e}")
        
        # 总结结果
        self._summarize_results(all_results)
        
        return all_results
    
    def _summarize_results(self, all_results):
        """总结结果"""
        print("\n" + "="*80)
        print("🏆 方法对比总结")
        print("="*80)
        
        if not all_results:
            print("❌ 没有成功的测试结果")
            return
        
        # 计算每种方法的平均性能
        method_scores = {}
        for method, models in all_results.items():
            scores = [model_result['accuracy'] for model_result in models.values()]
            method_scores[method] = np.mean(scores)
        
        # 排序
        sorted_methods = sorted(method_scores.items(), key=lambda x: x[1], reverse=True)
        
        print("📊 各方法平均准确率排名:")
        for i, (method, score) in enumerate(sorted_methods, 1):
            print(f"  {i}. {method}: {score:.4f}")
        
        print(f"\n🥇 最佳方法: {sorted_methods[0][0]} (准确率: {sorted_methods[0][1]:.4f})")
        
        # 详细结果
        print(f"\n📋 详细结果:")
        for method, models in all_results.items():
            print(f"\n{method}:")
            for model_name, result in models.items():
                print(f"  {model_name}: {result['accuracy']:.4f} (CV: {result['cv_mean']:.4f}±{result['cv_std']:.4f})")


def main():
    """主函数"""
    comparison = AdvancedMLComparison()
    comparison.run_comparison()


if __name__ == "__main__":
    main()
