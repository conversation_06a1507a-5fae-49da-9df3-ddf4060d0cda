#!/usr/bin/env python3
"""
新闻训练样本生成脚本 - 改进版

功能：
1. 处理process/filter_news文件夹下的所有过滤新闻数据
2. 将UTC+8时间转换为UTC+0
3. 根据matched_symbols拆分新闻为多个样本
4. 过滤稳定币、alpha币、BTC等不适合的交易对
5. 找到最接近的往后时间点的标签和技术指标值
6. 生成统一的训练样本CSV文件

改进：
- 扩展处理所有filtered新闻文件
- 增强交易对过滤逻辑（移除稳定币、alpha币等）
- 生成统一的训练数据集

作者：AI Assistant
日期：2025-07-27
"""

import pandas as pd
import numpy as np
import os
import glob
from datetime import datetime, timedelta, timezone
from pathlib import Path
import json
import re


def convert_utc8_to_utc0(time_str, date_str):
    """
    将UTC+8时间转换为UTC+0
    Args:
        time_str: 时间字符串，如 "23:45"
        date_str: 日期字符串，如 "3月1日"
    Returns:
        UTC datetime对象
    """
    try:
        # 检查输入是否为None或空
        if not date_str or date_str is None or str(date_str).strip() == '' or str(date_str) == 'None':
            print(f"日期字符串为空或None: {date_str}")
            return None

        if not time_str or time_str is None or str(time_str).strip() == '' or str(time_str) == 'None':
            print(f"时间字符串为空或None: {time_str}")
            return None

        # 转换为字符串并清理
        date_str = str(date_str).strip()
        time_str = str(time_str).strip()

        # 解析中文日期格式
        month_day = date_str.replace("月", "-").replace("日", "")
        if "-" in month_day:
            month, day = month_day.split("-")
            # 假设是2025年
            year = 2025

            # 创建UTC+8时间
            dt_utc8 = datetime(year, int(month), int(day))

            # 解析时间
            if ":" in time_str:
                hour, minute = time_str.split(":")
                dt_utc8 = dt_utc8.replace(hour=int(hour), minute=int(minute))

            # 转换UTC+8到UTC+0（减去8小时）
            dt_utc0 = dt_utc8 - timedelta(hours=8)

            return dt_utc0
    except Exception as e:
        print(f"时间转换错误 {time_str} {date_str}: {e}")
        return None


def find_next_5min_timestamp(target_time):
    """
    找到目标时间之后最近的5分钟对齐时间戳
    Args:
        target_time: 目标时间
    Returns:
        下一个5分钟对齐的时间戳
    """
    minute = target_time.minute
    next_5min = ((minute // 5) + 1) * 5
    
    if next_5min >= 60:
        next_time = target_time.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
    else:
        next_time = target_time.replace(minute=next_5min, second=0, microsecond=0)
    
    return next_time


def is_excluded_symbol(symbol):
    """
    判断是否为需要排除的交易对
    Args:
        symbol: 交易对名称
    Returns:
        bool: True表示需要排除
    """
    symbol = symbol.upper().strip()

    # 排除BTC,alpha (容易和binance alpha混淆)
    if symbol == 'BTCUSDT' or symbol=="ALPHAUSDT":
        return True

    # 只排除纯稳定币本身（保留以USDT结尾的交易对）
    pure_stablecoins = ['USDT', 'USDC', 'BUSD', 'DAI', 'TUSD', 'USDP', 'USDD', 'FRAX', 'LUSD', 'FDUSD', 'PYUSD']
    if symbol in pure_stablecoins:
        return True

    # 保留所有其他币种，包括：
    # - meme币（DOGE, SHIB, PEPE等）- 这些最容易受新闻影响
    # - 投机币 - 同样容易受新闻影响
    # - 以数字开头的币种（1000PEPE等）- 这些也是热门投机标的
    # - 已崩盘的币种（LUNA, FTT等）- 虽然风险高但仍有交易价值

    return False


def parse_matched_symbols(matched_symbols_str):
    """
    解析matched_symbols字符串，返回过滤后的symbol列表
    Args:
        matched_symbols_str: 逗号分隔的symbol字符串（如"ETH-PERP.BINANCE,BTC-PERP.BINANCE"）
    Returns:
        过滤后的symbol列表
    """
    if not matched_symbols_str or matched_symbols_str.strip() == '':
        return []

    symbols = []
    for s in matched_symbols_str.split(','):
        s = s.strip()
        # 提取symbol部分（去掉-PERP.BINANCE后缀）
        if '-PERP.BINANCE' in s:
            symbol = s.replace('-PERP.BINANCE', 'USDT')
        elif 'USDT' not in s:
            symbol = s + 'USDT'
        else:
            symbol = s

        symbol = symbol.upper()

        # 应用过滤规则
        if not is_excluded_symbol(symbol):
            symbols.append(symbol)

    return symbols


def load_symbol_labeled_data(symbol):
    """
    加载指定symbol的相对标签数据
    Args:
        symbol: symbol名称，如 "ETHUSDT"
    Returns:
        polars DataFrame或None
    """
    # 应用排除规则
    if is_excluded_symbol(symbol):
        return None

    file_path = f"../data/{symbol}_5m_relative_labeled.parquet"
    if not os.path.exists(file_path):
        return None

    try:
        df = pd.read_parquet(file_path)
        return df
    except Exception as e:
        print(f"加载{file_path}错误: {e}")
        return None


def validate_sample_data(match_data):
    """
    验证样本数据的有效性
    Args:
        match_data: 匹配的数据字典
    Returns:
        bool: 数据是否有效
    """
    if match_data is None:
        return False

    # 检查必需的字段
    required_fields = ['label', 'natr', 'mfi', 'rsi', 'btc_natr', 'btc_mfi', 'btc_rsi']

    for field in required_fields:
        value = match_data.get(field)
        if value is None:
            return False

        # 检查是否为NaN
        try:
            if pd.isna(value) or (isinstance(value, float) and (value != value)):  # NaN check
                return False
        except:
            # 如果无法检查NaN，认为无效
            return False

    return True


def find_closest_future_timestamp(target_timestamp, labeled_df):
    """
    在标签数据中找到最接近的往后时间点
    Args:
        target_timestamp: 目标时间戳
        labeled_df: 标签数据DataFrame
    Returns:
        匹配的数据行或None
    """
    if labeled_df is None or len(labeled_df) == 0:
        return None

    try:
        # 转换目标时间戳为UTC时区的datetime
        if isinstance(target_timestamp, datetime):
            if target_timestamp.tzinfo is None:
                target_timestamp = target_timestamp.replace(tzinfo=timezone.utc)

        # 确保labeled_df的timestamp列是正确的datetime类型
        if labeled_df['timestamp'].dtype == 'object':
            labeled_df['timestamp'] = pd.to_datetime(labeled_df['timestamp'])

        # 找到大于等于目标时间的数据
        future_data = labeled_df[labeled_df['timestamp'] >= target_timestamp]

        if len(future_data) == 0:
            return None

        # 按时间戳排序，取第一个
        closest_data = future_data.sort_values('timestamp').iloc[0]

        return closest_data.to_dict()

    except Exception as e:
        print(f"查找最近时间戳错误: {e}")
        return None


def get_available_symbols():
    """
    获取可用的symbol列表（有相对标签数据的，且未被排除的）
    Returns:
        symbol列表
    """
    symbol_files = glob.glob("../data/*_5m_relative_labeled.parquet")
    symbols = [os.path.basename(f).replace("_5m_relative_labeled.parquet", "") for f in symbol_files]
    # 应用排除规则
    return [s for s in symbols if not is_excluded_symbol(s)]


def process_news_file(news_file_path, available_symbols):
    """
    处理单个新闻文件
    Args:
        news_file_path: 新闻文件路径
        available_symbols: 可用的symbol列表
    Returns:
        处理后的样本列表
    """
    try:
        print(f"📰 处理新闻文件: {news_file_path}")
        
        # 读取新闻文件
        news_df = pd.read_csv(news_file_path)
        
        if len(news_df) == 0:
            print(f"新闻文件为空: {news_file_path}")
            return []

        processed_samples = []
        skipped_count = 0
        processed_count = 0

        print(f"📊 新闻总数: {len(news_df)}")

        for _, news_row in news_df.iterrows():
            try:
                # 提取新闻信息
                title = news_row.get('title', '')
                content = news_row.get('content', '')
                time_str = news_row.get('time', '')
                date_str = news_row.get('date', '')
                matched_symbols = news_row.get('matched_symbols', '')

                # 解析symbols（使用新的matched_symbols字段）
                symbols = parse_matched_symbols(matched_symbols)
                
                if not symbols:
                    skipped_count += 1
                    continue
                
                # 转换时间UTC+8到UTC+0
                utc_time = convert_utc8_to_utc0(time_str, date_str)
                if utc_time is None:
                    skipped_count += 1
                    continue
                
                # 找到下一个5分钟对齐时间戳
                target_timestamp = find_next_5min_timestamp(utc_time)
                
                # 处理每个symbol（每个symbol生成一个独立样本）
                for symbol in symbols:
                    # 应用排除规则
                    if is_excluded_symbol(symbol):
                        skipped_count += 1
                        continue

                    # 检查symbol是否在可用列表中
                    if symbol not in available_symbols:
                        skipped_count += 1
                        continue

                    # 加载symbol的标签数据
                    labeled_df = load_symbol_labeled_data(symbol)
                    if labeled_df is None:
                        skipped_count += 1
                        continue

                    # 找到最接近的往后时间点
                    match_data = find_closest_future_timestamp(target_timestamp, labeled_df)
                    if match_data is None:
                        skipped_count += 1
                        continue

                    # 验证数据有效性（检查label和技术指标）
                    if not validate_sample_data(match_data):
                        skipped_count += 1
                        continue

                    # 创建训练样本
                    sample = {
                        'news_title': title,
                        'news_content': content,
                        'news_time_utc8': f"{date_str} {time_str}",
                        'news_time_utc0': utc_time.isoformat(),
                        'target_timestamp': target_timestamp.isoformat(),
                        'matched_timestamp': match_data['timestamp'],
                        'symbol': symbol,
                        'close': match_data.get('close'),
                        'volume': match_data.get('volume'),
                        'relative_price': match_data.get('relative_price'),
                        'label': match_data.get('label'),
                        'natr': match_data.get('natr'),
                        'mfi': match_data.get('mfi'),
                        'rsi': match_data.get('rsi'),
                        'btc_close': match_data.get('btc_close'),
                        'btc_natr': match_data.get('btc_natr'),
                        'btc_mfi': match_data.get('btc_mfi'),
                        'btc_rsi': match_data.get('btc_rsi'),
                        'event_starts': match_data.get('event_starts'),
                        'event_ends': match_data.get('event_ends')
                    }

                    processed_samples.append(sample)
                    processed_count += 1
                    
            except Exception as e:
                print(f"处理新闻行错误: {e}")
                skipped_count += 1
                continue
        
        print(f"✅ 处理完成 - 生成样本: {len(processed_samples)}, 跳过: {skipped_count}")
        return processed_samples
        
    except Exception as e:
        print(f"处理新闻文件错误 {news_file_path}: {e}")
        return []


def save_samples_to_csv(samples, output_path):
    """
    将样本保存为CSV文件
    Args:
        samples: 样本列表
        output_path: 输出文件路径
    """
    if not samples:
        print("没有样本需要保存")
        return
    
    try:
        # 转换为DataFrame
        df = pd.DataFrame(samples)
        
        # 保存为CSV
        df.to_csv(output_path, index=False, encoding='utf-8-sig')
        print(f"💾 样本已保存到: {output_path}")
        print(f"📊 总样本数: {len(samples)}")
        
        # 显示统计信息
        if 'symbol' in df.columns:
            symbol_counts = df['symbol'].value_counts()
            print(f"📈 各symbol样本数:")
            for symbol, count in symbol_counts.items():
                print(f"  {symbol}: {count}")
                
    except Exception as e:
        print(f"保存样本错误: {e}")


def process_multiple_files(file_pattern, available_symbols, output_prefix="news_training_samples"):
    """
    处理多个新闻文件
    Args:
        file_pattern: 文件模式，如 "filter_news/panews_flash_*.csv"
        available_symbols: 可用的symbol列表
        output_prefix: 输出文件前缀
    """
    news_files = glob.glob(file_pattern)
    news_files.sort()  # 按文件名排序

    if not news_files:
        print(f"❌ 没有找到匹配的文件: {file_pattern}")
        return

    print(f"📁 找到 {len(news_files)} 个新闻文件")

    all_samples = []

    for i, news_file in enumerate(news_files, 1):
        print(f"\n[{i}/{len(news_files)}] 处理文件: {news_file}")

        samples = process_news_file(news_file, available_symbols)
        if samples:
            all_samples.extend(samples)
            print(f"✅ 生成 {len(samples)} 个样本")
        else:
            print("⚠️  没有生成样本")

    if all_samples:
        # 保存所有样本
        output_file = f"{output_prefix}_all.csv"
        save_samples_to_csv(all_samples, output_file)

        print(f"\n🎉 总计处理完成:")
        print(f"📁 处理文件数: {len(news_files)}")
        print(f"📊 总样本数: {len(all_samples)}")
    else:
        print("❌ 没有生成任何样本")


def main():
    """
    主函数 - 处理所有过滤新闻文件
    """
    print("=== 新闻训练样本生成器 - 改进版 ===")

    # 获取可用的symbols
    available_symbols = get_available_symbols()
    print(f"📋 可用symbols: {len(available_symbols)}")
    print(f"🔗 Symbols: {', '.join(available_symbols[:10])}{'...' if len(available_symbols) > 10 else ''}")

    # 处理所有过滤新闻文件
    file_pattern = "filter_news/panews_flash_*_filtered.csv"

    print(f"\n🔍 搜索文件模式: {file_pattern}")

    # 处理所有文件并生成统一的训练数据集
    process_multiple_files(file_pattern, available_symbols, output_prefix="news_training_samples_complete")

    print(f"\n✅ 所有新闻文件处理完成！")
    print(f"📁 训练数据已保存到: process/news_training_samples_complete_all.csv")
    print(f"🎯 可用于后续模型训练")


if __name__ == "__main__":
    main()
