"""
新闻PnL预测Controller

- 订阅新闻数据并加载PnL预测模型
- 动态创建和管理交易策略
- 发布预测信号给专门的交易策略
- 管理策略生命周期（创建、销毁）

用法：
    python news_pnl_controller.py
"""

import os
import pickle
import numpy as np
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, Optional, List, Tuple


from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
import sentencepiece as spm

from nautilus_trader.common.component import TimeEvent
from nautilus_trader.common.config import ActorConfig
from nautilus_trader.core.correctness import PyCondition
from nautilus_trader.core.uuid import UUID4
from nautilus_trader.model import DataType
from nautilus_trader.model.identifiers import InstrumentId, StrategyId, Venue
from nautilus_trader.model.instruments import Instrument
from nautilus_trader.trading.controller import Controller
from nautilus_trader.trading.strategy import Strategy
from nautilus_trader.trading.trader import Trader
from nautilus_trader.model.data import BarType
from nautilus_trader.model.enums import OrderSide, TimeInForce
from nautilus_trader.model.objects import Quantity
from nautilus_trader.model.orders import MarketOrder, StopMarketOrder
from nautilus_trader.indicators.average.sma import SimpleMovingAverage
from nautilus_trader.indicators.atr import AverageTrueRange
from nautilus_trader.indicators.rsi import RelativeStrengthIndex
from nautilus_trader.indicators.mfi import MoneyFlowIndex

from news_data_type import NewsData
from crypto_text_processor import CryptoNewsTextProcessor
from news_instrument_extractor_v2 import NewsInstrumentExtractor


# 基于实验结果的最优模型配置
class OptimalNewsPredictor:
    """
    基于实验结果报告的最优新闻预测器

    配置:
    - Tokenizer: baseline_2k (SentencePiece, 2K词汇表)
    - 特征工程: TF-IDF(300维) + 技术指标(6维)
    - 模型: RandomForest(n_estimators=50) - 通过加载获得
    - 阈值: [-3.0%, -0.8%, +0.8%, +3.0%]
    """

    def __init__(self, tokenizer_path: str, model_path: str):
        self.tokenizer_path = tokenizer_path
        self.model_path = model_path
        self.sp_model = None
        self.tfidf_vectorizer = None
        self.rf_model = None

        # 分类阈值 (基于实验结果)
        self.thresholds = [-3.0, -0.8, 0.8, 3.0]  # 百分比

        # 加载预训练的模型组件
        self._load_trained_components()

    def _load_trained_components(self):
        """加载预训练的模型组件 - 与最优模型处理过程完全一致"""
        try:
            # 1. 加载预训练的SentencePiece tokenizer (baseline_2k)
            if os.path.exists(self.tokenizer_path):
                self.sp_model = spm.SentencePieceProcessor()
                self.sp_model.load(self.tokenizer_path)
                print(f"✅ 已加载SentencePiece模型: {self.tokenizer_path}")
            else:
                print(f"WARNING: SentencePiece模型不存在: {self.tokenizer_path}")

            # 2. 加载预训练的TF-IDF vectorizer
            tfidf_path = self.model_path.replace('.pkl', '_tfidf.pkl')
            if os.path.exists(tfidf_path):
                with open(tfidf_path, 'rb') as f:
                    self.tfidf_vectorizer = pickle.load(f)
                print(f"✅ 已加载TF-IDF vectorizer: {tfidf_path}")
            else:
                print(f"WARNING: TF-IDF vectorizer不存在: {tfidf_path}")

            # 3. 加载预训练的RandomForest模型
            if os.path.exists(self.model_path):
                with open(self.model_path, 'rb') as f:
                    self.rf_model = pickle.load(f)
                print(f"✅ 已加载RandomForest模型: {self.model_path}")
            else:
                print(f"WARNING: RandomForest模型不存在: {self.model_path}")

        except Exception as e:
            print(f"ERROR: 加载模型组件失败: {e}")

    def preprocess_text(self, text: str) -> str:
        """使用SentencePiece预处理文本"""
        if self.sp_model:
            # 使用SentencePiece分词
            pieces = self.sp_model.encode_as_pieces(text)
            return ' '.join(pieces)
        else:
            # 简单预处理
            return text.lower().strip()

    def extract_features(self, text: str, technical_indicators: List[float]) -> np.ndarray:
        """提取特征向量 (TF-IDF + 技术指标) - 与最优模型处理方式完全一致"""
        try:
            # 1. 预处理文本 (使用SentencePiece)
            processed_text = self.preprocess_text(text)

            # 2. TF-IDF特征提取 (必须使用预训练的vectorizer)
            if self.tfidf_vectorizer and hasattr(self.tfidf_vectorizer, 'transform'):
                text_features = self.tfidf_vectorizer.transform([processed_text]).toarray()[0]
            else:
                raise ValueError("TF-IDF vectorizer未正确加载，无法进行特征提取")

            # 3. 合并技术指标 (严格6维: NATR, MFI, RSI, BTC_NATR, BTC_MFI, BTC_RSI)
            if len(technical_indicators) != 6:
                raise ValueError(f"技术指标维度错误: 期望6维，实际{len(technical_indicators)}维")

            features = np.concatenate([text_features, technical_indicators])
            return features

        except Exception as e:
            print(f"特征提取失败: {e}")
            raise  # 不使用fallback，确保与最优模型一致

    def predict(self, text: str, technical_indicators: List[float]) -> Tuple[int, float]:
        """
        预测价格走势

        Returns:
            Tuple[int, float]: (预测类别, 置信度)
            类别定义:
            - 0 (大跌): < -3.0%
            - 1 (小跌): -3.0% ~ -0.8%
            - 2 (平稳): -0.8% ~ +0.8%
            - 3 (小涨): +0.8% ~ +3.0%
            - 4 (大涨): > +3.0%
        """
        try:
            # 提取特征
            features = self.extract_features(text, technical_indicators)

            if self.rf_model and hasattr(self.rf_model, 'predict_proba'):
                # 使用训练好的模型
                features_2d = features.reshape(1, -1)

                # 标准化 (如果有scaler)
                if self.scaler:
                    features_2d = self.scaler.transform(features_2d)

                # 预测
                probabilities = self.rf_model.predict_proba(features_2d)[0]
                predicted_class = int(np.argmax(probabilities))
                confidence = float(probabilities[predicted_class])

                return predicted_class, confidence
            else:
                # 简化预测逻辑
                return self._simple_prediction(text, technical_indicators)

        except Exception as e:
            print(f"预测失败: {e}")
            return self._simple_prediction(text, technical_indicators)

    def _simple_prediction(self, text: str, technical_indicators: List[float]) -> Tuple[int, float]:
        """简化预测逻辑 - 当ML模型不可用时的fallback

        基于您提供的prepare_features和test_configuration逻辑实现
        """
        try:
            # 1. 文本预处理和特征提取
            processed_text = self.preprocess_text(text)

            # 2. 使用简化的TF-IDF特征提取（模拟最优模型的处理过程）
            text_features = self._simple_text_features(processed_text)

            # 3. 技术指标特征（确保6维）
            if len(technical_indicators) != 6:
                print(f"警告: 技术指标维度不正确，期望6维，实际{len(technical_indicators)}维")
                # 填充或截断到6维
                tech_features = (technical_indicators + [0.0] * 6)[:6]
            else:
                tech_features = technical_indicators

            # 4. 合并特征（模拟 X = np.hstack([X_text.toarray(), technical_features])）
            combined_features = np.concatenate([text_features, tech_features])

            # 5. 简化的分类逻辑（模拟RandomForest的决策过程）
            return self._classify_with_simple_rules(processed_text, tech_features, combined_features)

        except Exception as e:
            print(f"简化预测失败: {e}")
            # 最基本的fallback
            return self._basic_keyword_prediction(text, technical_indicators)

    def _classify_with_simple_rules(self, text: str, tech_features: List[float], combined_features: np.ndarray) -> Tuple[int, float]:
        """基于规则的分类，模拟RandomForest的决策过程"""

        # 文本情感分析
        text_lower = text.lower()

        # 扩展关键词列表，更准确地识别情感
        positive_words = [
            'surge', 'surges', 'rise', 'rises', 'bull', 'bullish', 'up', 'gain', 'gains',
            'high', 'higher', 'break', 'breaks', 'breakthrough', 'moon', 'pump', 'pumps',
            'adoption', 'institutional', 'massive', 'huge', 'major', 'strong', 'strength',
            '涨', '牛市', '上涨', '突破', '暴涨'
        ]
        negative_words = [
            'drop', 'drops', 'fall', 'falls', 'bear', 'bearish', 'down', 'loss', 'losses',
            'low', 'lower', 'crash', 'crashes', 'dump', 'dumps', 'selloff', 'sell-off',
            'crisis', 'tighten', 'regulatory', 'uncertainty', 'faces', 'major selloff',
            '跌', '熊市', '下跌', '暴跌', '崩盘'
        ]

        # 计算情感得分，考虑词汇强度
        pos_score = 0
        neg_score = 0

        for word in positive_words:
            if word in text_lower:
                # 给强烈词汇更高权重
                if word in ['surge', 'surges', 'massive', 'huge', 'breakthrough', 'moon', '暴涨']:
                    pos_score += 2
                else:
                    pos_score += 1

        for word in negative_words:
            if word in text_lower:
                # 给强烈词汇更高权重
                if word in ['crash', 'crashes', 'crisis', 'major selloff', 'dump', '暴跌', '崩盘']:
                    neg_score += 2
                else:
                    neg_score += 1

        # 技术指标分析（基于6维技术指标）
        symbol_natr, symbol_mfi, symbol_rsi, btc_natr, btc_mfi, btc_rsi = tech_features

        # 技术指标综合评分
        tech_score = 0

        # RSI分析（更细致的分级）
        avg_rsi = (symbol_rsi + btc_rsi) / 2
        if avg_rsi > 80:
            tech_score -= 2  # 严重超买
        elif avg_rsi > 70:
            tech_score -= 1  # 超买
        elif avg_rsi < 20:
            tech_score += 2  # 严重超卖
        elif avg_rsi < 30:
            tech_score += 1  # 超卖
        elif 45 <= avg_rsi <= 55:
            tech_score += 0  # 中性

        # MFI分析（资金流向）
        avg_mfi = (symbol_mfi + btc_mfi) / 2
        if avg_mfi > 80:
            tech_score -= 1.5  # 资金大量流出
        elif avg_mfi > 70:
            tech_score -= 0.5  # 资金流出
        elif avg_mfi < 20:
            tech_score += 1.5  # 资金大量流入
        elif avg_mfi < 30:
            tech_score += 0.5  # 资金流入

        # NATR分析（波动率影响）
        avg_natr = (symbol_natr + btc_natr) / 2
        if avg_natr > 0.06:  # 极高波动
            volatility_factor = 1.5
            tech_score += 0.5  # 高波动通常伴随趋势
        elif avg_natr > 0.04:  # 高波动
            volatility_factor = 1.3
        elif avg_natr < 0.01:  # 低波动
            volatility_factor = 0.8
            tech_score -= 0.5  # 低波动倾向于平稳
        else:
            volatility_factor = 1.0

        # 综合评分
        sentiment_score = pos_score - neg_score
        total_score = sentiment_score + tech_score
        total_score *= volatility_factor

        # 分类决策（调整阈值，更敏感地响应信号）
        confidence_base = 0.5

        if total_score >= 3.0:
            return 4, min(0.85, confidence_base + 0.35)  # 大涨 (>+3%)
        elif total_score >= 1.0:
            return 3, min(0.75, confidence_base + 0.25)  # 小涨 (+0.8%~+3%)
        elif total_score <= -3.0:
            return 0, min(0.85, confidence_base + 0.35)  # 大跌 (<-3%)
        elif total_score <= -1.0:
            return 1, min(0.75, confidence_base + 0.25)  # 小跌 (-3%~-0.8%)
        else:
            return 2, confidence_base + 0.05  # 平稳 (-0.8%~+0.8%)

    def _basic_keyword_prediction(self, text: str, technical_indicators: List[float]) -> Tuple[int, float]:
        """最基本的关键词预测"""
        text_lower = text.lower()

        positive_words = ['surge', 'rise', 'bull', 'up', 'gain', 'high']
        negative_words = ['drop', 'fall', 'bear', 'down', 'loss', 'low']

        pos_count = sum(1 for word in positive_words if word in text_lower)
        neg_count = sum(1 for word in negative_words if word in text_lower)

        if pos_count > neg_count:
            return 3, 0.6  # 小涨
        elif neg_count > pos_count:
            return 1, 0.6  # 小跌
        else:
            return 2, 0.5  # 平稳

    def _simple_text_features(self, text: str) -> np.ndarray:
        """简化的文本特征提取 - 模拟TF-IDF vectorizer的输出

        基于您提供的prepare_features逻辑，使用max_features=300
        """
        try:
            # 模拟TF-IDF特征提取过程
            features = []

            # 1. 基本文本统计特征
            words = text.split()
            features.extend([
                len(text),                    # 文本长度
                len(words),                   # 词数
                len(set(words)),              # 唯一词数
                text.count('!'),              # 感叹号数量
                text.count('?'),              # 问号数量
                text.count('$'),              # 美元符号
                text.count('%'),              # 百分号
                text.count('bitcoin'),        # bitcoin出现次数
                text.count('btc'),            # btc出现次数
                text.count('crypto'),         # crypto出现次数
            ])

            # 2. 关键词特征（模拟TF-IDF的重要词汇）
            crypto_keywords = [
                'bitcoin', 'btc', 'ethereum', 'eth', 'crypto', 'cryptocurrency',
                'blockchain', 'defi', 'nft', 'trading', 'market', 'price',
                'surge', 'rise', 'bull', 'bullish', 'moon', 'pump',
                'drop', 'fall', 'bear', 'bearish', 'crash', 'dump',
                'adoption', 'institutional', 'regulation', 'sec', 'etf',
                'volume', 'support', 'resistance', 'breakout', 'breakdown'
            ]

            text_lower = text.lower()
            for keyword in crypto_keywords:
                features.append(text_lower.count(keyword))

            # 3. N-gram特征（模拟ngram_range=(1, 2)）
            bigrams = [f"{words[i]} {words[i+1]}" for i in range(len(words)-1)]
            important_bigrams = [
                'bitcoin price', 'crypto market', 'new high', 'all time',
                'market cap', 'trading volume', 'price surge', 'major selloff',
                'institutional adoption', 'regulatory clarity'
            ]

            for bigram in important_bigrams:
                features.append(text_lower.count(bigram))

            # 4. 情感特征
            positive_words = ['surge', 'rise', 'bull', 'up', 'gain', 'high', 'break', 'moon', 'pump', '涨', '牛市', '上涨', '突破']
            negative_words = ['drop', 'fall', 'bear', 'down', 'loss', 'low', 'crash', 'dump', '跌', '熊市', '下跌', '暴跌']

            pos_count = sum(1 for word in positive_words if word in text_lower)
            neg_count = sum(1 for word in negative_words if word in text_lower)

            features.extend([
                pos_count,                    # 正面词汇数量
                neg_count,                    # 负面词汇数量
                pos_count - neg_count,        # 情感差值
                max(pos_count, neg_count),    # 最大情感强度
            ])

            # 5. 填充到300维（模拟max_features=300）
            while len(features) < 300:
                features.append(0.0)

            # 6. 简单的TF-IDF权重模拟（归一化）
            features_array = np.array(features[:300])

            # 避免除零错误
            norm = np.linalg.norm(features_array)
            if norm > 0:
                features_array = features_array / norm

            return features_array

        except Exception as e:
            print(f"文本特征提取失败: {e}")
            # 返回零向量
            return np.zeros(300)

    def _fallback_features(self, text: str, technical_indicators: List[float]) -> np.ndarray:
        """备用特征提取"""
        text_features = self._simple_text_features(text)
        tech_features = np.array(technical_indicators + [0.0] * 6)[:6]
        return np.concatenate([text_features, tech_features])



class NewsPnlPrediction(DataType):
    """新闻PnL预测数据类型"""
    
    def __init__(self, news_id, symbol, pnl_type, confidence, ts_event, ts_init):
        self.news_id = news_id
        self.symbol = symbol
        self.pnl_type = pnl_type  
        self.confidence = confidence
        self._ts_event = ts_event
        self._ts_init = ts_init

    @property
    def ts_event(self):
        return self._ts_event

    @property
    def ts_init(self):
        return self._ts_init

    def __repr__(self):
        return f"NewsPnlPrediction(news_id={self.news_id}, symbol={self.symbol}, pnl_type={self.pnl_type}, conf={self.confidence:.2f})"


class NewsPnlControllerConfig(ActorConfig, frozen=True):
    """Controller配置 - 基于实验结果的最优配置"""
    # 模型路径 (基于实验结果)
    model_path: str = "./models/optimal_rf_model.pkl"  # RandomForest模型
    tokenizer_path: str = "./models/baseline_2k.model"  # SentencePiece baseline_2k
    venue: str = "BINANCE"  # 交易所

    # 交易参数 (基于实验阈值配置)
    max_trade_usd: Decimal = Decimal("1000.0")  # 最大交易金额USD
    min_trade_usd: Decimal = Decimal("50.0")   # 最小交易金额USD
    stop_loss_pct: Decimal = Decimal("0.03")  # 止损百分比 3% (对应大跌阈值)
    max_positions: int = 5  # 最大同时持仓数
    min_confidence: float = 0.6  # 最小置信度 (基于实验结果调整)

    # 两级别移动止盈参数
    first_trail_profit_threshold: Decimal = Decimal("0.02")  # 第一级移动止盈触发阈值 2%

    # 新闻处理参数
    max_symbols_per_news: int = 3  # 每条新闻最多提取的symbol数量

    # 预测阈值 (基于实验结果: [-3.0%, -0.8%, +0.8%, +3.0%])
    prediction_thresholds: Optional[List[float]] = None


class NewsPnlController(Controller):
    """
    新闻PnL预测Controller
    
    负责：
    1. 订阅新闻数据并预测PnL
    2. 动态创建交易策略
    3. 发布预测信号
    4. 管理策略生命周期
    """

    def __init__(
        self,
        trader: Trader,
        config: NewsPnlControllerConfig | None = None,
    ) -> None:
        if config is None:
            config = NewsPnlControllerConfig()
        PyCondition.type(config, NewsPnlControllerConfig, "config")
        super().__init__(config=config, trader=trader)

        # 模型路径配置 (传递给策略)
        self.tokenizer_path = config.tokenizer_path
        self.model_path = config.model_path

        # 设置默认阈值
        self.prediction_thresholds = config.prediction_thresholds or [-3.0, -0.8, 0.8, 3.0]

        # 交易参数
        self.max_trade_usd = config.max_trade_usd
        self.min_trade_usd = config.min_trade_usd
        self.stop_loss_pct = config.stop_loss_pct
        self.max_positions = config.max_positions
        self.min_confidence = config.min_confidence
        self.first_trail_profit_threshold = config.first_trail_profit_threshold
        self.venue = Venue(config.venue)

        # 新闻处理组件
        self.text_processor = CryptoNewsTextProcessor()
        self.symbol_extractor = NewsInstrumentExtractor(
            cache=self.cache,
            venue=self.venue,
            max_symbols=config.max_symbols_per_news
        )

        # 策略管理
        self.active_strategies: Dict[str, Strategy] = {}
        self.strategy_positions: Dict[str, bool] = {}  # 记录策略是否有持仓

        # 订阅新闻数据
        self.subscribe_data(DataType(NewsData))

        # 订阅策略完成信号
        self.msgbus.subscribe("strategy.*.complete", self._on_strategy_complete)

        self.log.info("✅ Controller已订阅NewsData和策略完成信号")

    def on_start(self) -> None:
        """Controller启动时初始化"""
        try:
            # 预测器已在__init__中初始化
            self.log.info("✅ Controller启动完成，使用最优ML配置")
            self.log.info(f"  - 模型: RandomForest (n_estimators=50)")
            self.log.info(f"  - Tokenizer: SentencePiece baseline_2k")
            self.log.info(f"  - 特征: TF-IDF(300维) + 技术指标(6维)")
            self.log.info(f"  - 阈值: {self.prediction_thresholds}")

        except Exception as e:
            self.log.error(f"❌ Controller启动失败: {e}")
            return

    def on_data(self, data) -> None:
        """处理新闻数据并预测PnL"""
        if isinstance(data, NewsData):
            self._process_news_data(data)

    def _process_news_data(self, news_data: NewsData) -> None:
        """处理新闻数据，提取symbol并创建/通知策略"""
        try:
            # 1. 使用轻量化预处理新闻title
            processed_title = self.text_processor.process(news_data.title)
            processed_content = self.text_processor.process(news_data.content)

            self.log.info(f"📰 处理新闻: {news_data.title[:50]}...")

            # 2. 使用news_instrument_extractor_v2提取symbol
            symbols_with_confidence = self.symbol_extractor.extract_symbols_from_text(
                processed_title, processed_content
            )

            if not symbols_with_confidence:
                self.log.info("未从新闻中提取到有效symbol，跳过")
                return

            # 3. 检测symbol是否在cache.instruments中，并处理多个symbol
            valid_symbols = []
            for symbol, match_count, confidence in symbols_with_confidence:
                # 构造instrument_id
                instrument_str = f"{symbol}-PERP.{self.venue.value}"
                try:
                    instrument_id = InstrumentId.from_str(instrument_str)
                    # 检查是否在cache中
                    instrument = self.cache.instrument(instrument_id)
                    if instrument:
                        valid_symbols.append((symbol, instrument_id, match_count, confidence))
                        self.log.info(f"✅ 找到有效symbol: {symbol} -> {instrument_id}")
                    else:
                        self.log.warning(f"❌ Symbol {symbol} 不在cache.instruments中")
                except Exception as e:
                    self.log.warning(f"❌ 无效的instrument_id: {instrument_str}, 错误: {e}")

            if not valid_symbols:
                self.log.info("没有找到有效的交易对，跳过")
                return

            # 4. 为每个有效symbol管理策略
            for symbol, instrument_id, match_count, confidence in valid_symbols:
                # 创建新闻数据对象传递给策略
                news_signal = {
                    'title': processed_title,
                    'content': processed_content,
                    'symbol': symbol,
                    'ts_event': news_data.ts_event,
                    'ts_init': news_data.ts_init
                }
                self._manage_trading_strategy(instrument_id, news_signal)

        except Exception as e:
            self.log.error(f"处理新闻数据失败: {e}")

    def _on_strategy_complete(self, data: Dict) -> None:
        """处理策略完成信号"""
        try:
            strategy_id = data.get("strategy_id")
            if strategy_id and strategy_id in self.active_strategies:
                # 停止并移除策略
                strategy = self.active_strategies[strategy_id]
                self.stop_strategy(strategy)

                # 从活跃策略中移除
                del self.active_strategies[strategy_id]
                if strategy_id in self.strategy_positions:
                    del self.strategy_positions[strategy_id]

                self.log.info(f"🗑️ 已销毁完成的策略: {strategy_id}")

        except Exception as e:
            self.log.error(f"处理策略完成信号失败: {e}")





    def _manage_trading_strategy(self, instrument_id: InstrumentId, news_signal: Dict) -> None:
        """管理交易策略"""
        strategy_id = f"NewsPnlStrategy_{instrument_id.value}"

        # 检查策略是否已存在
        if strategy_id in self.active_strategies:
            strategy = self.active_strategies[strategy_id]
            # 如果策略没有持仓，发送新闻信号
            if not self.strategy_positions.get(strategy_id, False):
                self.log.info(f"向现有策略 {strategy_id} 发送新闻信号")
                # 通过消息总线发送新闻信号
                self.msgbus.publish(f"strategy.{strategy_id}.news_signal", news_signal)
            return

        # 检查是否达到最大策略数量
        if len(self.active_strategies) >= self.max_positions * 2:  # 允许更多策略但限制持仓
            self.log.warning(f"已达到最大策略数量: {len(self.active_strategies)}")
            return

        # 创建新策略，传入初始新闻数据
        try:
            strategy = self._create_trading_strategy(instrument_id, strategy_id, news_signal)
            if strategy:
                self.create_strategy(strategy, start=True)
                self.active_strategies[strategy_id] = strategy
                self.strategy_positions[strategy_id] = False
                self.log.info(f"✅ 创建新策略: {strategy_id}，包含初始新闻数据")

        except Exception as e:
            self.log.error(f"创建策略失败 {strategy_id}: {e}")

    def _create_trading_strategy(self, instrument_id: InstrumentId, strategy_id: str, initial_news_data: Dict = None) -> Optional[Strategy]:
        """创建交易策略实例"""
        try:
            # 获取交易对信息
            instrument = self.cache.instrument(instrument_id)
            if not instrument:
                self.log.error(f"未找到交易对: {instrument_id}")
                return None
            
            # 创建策略配置
            config = NewsPnlTradingStrategyConfig(
                instrument_id=instrument_id,
                bar_type=BarType.from_str(f"{instrument_id.value}-5-MINUTE-LAST-EXTERNAL"),
                btc_bar_type=BarType.from_str(f"BTCUSDT-PERP.{self.venue.value}-5-MINUTE-LAST-EXTERNAL"),
                max_trade_usd=self.max_trade_usd,
                min_trade_usd=self.min_trade_usd,
                stop_loss_pct=self.stop_loss_pct,
                strategy_id=StrategyId(strategy_id),
                prediction_thresholds=self.prediction_thresholds,
                tokenizer_path=self.tokenizer_path,
                model_path=self.model_path,
                first_trail_profit_threshold=self.first_trail_profit_threshold,
                initial_news_data=initial_news_data,
            )
            
            # 创建策略实例
            strategy = NewsPnlTradingStrategy(config=config)
            return strategy
            
        except Exception as e:
            self.log.error(f"创建策略实例失败: {e}")
            return None

    def on_stop(self) -> None:
        """Controller停止时清理资源"""
        self.log.info("🛑 Controller停止，清理资源...")
        
        # 停止所有活跃策略
        for strategy_id, strategy in self.active_strategies.items():
            try:
                self.stop_strategy(strategy)
                self.remove_strategy(strategy)
                self.log.info(f"已停止策略: {strategy_id}")
            except Exception as e:
                self.log.error(f"停止策略失败 {strategy_id}: {e}")
        
        self.active_strategies.clear()
        self.strategy_positions.clear()


# 交易策略配置
class NewsPnlTradingStrategyConfig(ActorConfig, frozen=True):
    """新闻PnL交易策略配置"""
    instrument_id: InstrumentId
    bar_type: BarType
    btc_bar_type: BarType
    max_trade_usd: Decimal
    min_trade_usd: Decimal
    stop_loss_pct: Decimal
    strategy_id: StrategyId
    prediction_thresholds: List[float]
    # 模型路径参数
    tokenizer_path: str
    model_path: str
    # 两级别移动止盈参数
    first_trail_profit_threshold: Decimal  # 第一级移动止盈触发阈值
    second_trail_profit_threshold: Decimal = Decimal("0.03")  # 第二级移动止盈触发阈值
    first_trail_stop_pct: Decimal = Decimal("0.5")   # 第一级回撤50%
    second_trail_stop_pct: Decimal = Decimal("0.3")  # 第二级回撤30%
    # 初始新闻数据
    initial_news_data: Optional[Dict] = None


class NewsPnlTradingStrategy(Strategy):
    """
    新闻PnL交易策略
    
    负责：
    1. 接收预测信号
    2. 执行交易决策
    3. 管理止损止盈
    4. 监控5分钟K线
    """

    def __init__(self, config: NewsPnlTradingStrategyConfig) -> None:
        super().__init__(config)

        self.instrument_id = config.instrument_id
        self.bar_type = config.bar_type
        self.btc_bar_type = config.btc_bar_type
        self.max_trade_usd = config.max_trade_usd
        self.min_trade_usd = config.min_trade_usd
        self.stop_loss_pct = float(config.stop_loss_pct)

        # 预测阈值配置
        self.prediction_thresholds = config.prediction_thresholds

        # 状态管理
        self.first_signal = True  # 初次创建策略时为True
        self.has_symbol_position = False
        self.has_btc_position = False
        self.current_news_data = config.initial_news_data  # 存储初始新闻数据

        # 两级别移动止盈参数
        self.first_trail_profit_threshold = float(config.first_trail_profit_threshold)
        self.second_trail_profit_threshold = float(config.second_trail_profit_threshold)
        self.first_trail_stop_pct = float(config.first_trail_stop_pct)
        self.second_trail_stop_pct = float(config.second_trail_stop_pct)
        self.take_fee = 0.0004  # 交易费用

        # 持仓管理
        self.highest_profit_dict = {}  # 记录每个仓位的最高利润

        # 创建预测器实例 (使用传入的路径参数)
        self.predictor = OptimalNewsPredictor(
            tokenizer_path=config.tokenizer_path,
            model_path=config.model_path
        )

        # 技术指标 - symbol (MFI, RSI, NATR)
        self.symbol_atr = AverageTrueRange(14)
        self.symbol_rsi = RelativeStrengthIndex(14)
        self.symbol_mfi = MoneyFlowIndex(14)

        # 技术指标 - BTC (MFI, RSI, NATR)
        self.btc_atr = AverageTrueRange(14)
        self.btc_rsi = RelativeStrengthIndex(14)
        self.btc_mfi = MoneyFlowIndex(14)

        # 订阅新闻信号
        self.msgbus.subscribe(f"strategy.{self.id}.news_signal", self._on_news_signal)

    def on_start(self) -> None:
        """策略启动"""
        # 订阅symbol和btc的5分钟K线数据
        self.subscribe_bars(self.bar_type)
        self.subscribe_bars(self.btc_bar_type)

        # 注册技术指标 - symbol
        self.register_indicator_for_bars(self.bar_type, self.symbol_atr)
        self.register_indicator_for_bars(self.bar_type, self.symbol_rsi)
        self.register_indicator_for_bars(self.bar_type, self.symbol_mfi)

        # 注册技术指标 - BTC
        self.register_indicator_for_bars(self.btc_bar_type, self.btc_atr)
        self.register_indicator_for_bars(self.btc_bar_type, self.btc_rsi)
        self.register_indicator_for_bars(self.btc_bar_type, self.btc_mfi)

        # 请求历史数据来计算技术指标
        self._request_historical_data()

        self.log.info(f"✅ 策略启动: {self.id}")

    def _request_historical_data(self) -> None:
        """请求历史数据来计算技术指标"""
        try:
            # 请求symbol的历史数据
            self.request_bars(self.bar_type, limit=100)
            # 请求BTC的历史数据
            self.request_bars(self.btc_bar_type, limit=100)
            self.log.info("📊 已请求历史K线数据")
        except Exception as e:
            self.log.error(f"请求历史数据失败: {e}")

    def _on_news_signal(self, news_signal: Dict) -> None:
        """处理新闻信号并进行预测"""
        # 有仓位时也可以执行后续动作（更新新闻数据）
        self.log.info(f"📊 收到新闻信号: {news_signal['title'][:50]}...")

        # 存储新闻数据用于后续处理
        self.current_news_data = news_signal

        # 如果已有持仓，只更新新闻数据，不执行新的交易决策
        if self.has_symbol_position or self.has_btc_position:
            self.log.info(f"已有持仓，仅更新新闻数据")
            return

        # 检查是否有足够的技术指标数据
        if self._has_sufficient_indicators():
            self._execute_trading_decision()
        else:
            self.log.info("等待更多技术指标数据...")

    def _has_sufficient_indicators(self) -> bool:
        """检查是否有足够的技术指标数据"""
        return (self.symbol_atr.has_inputs and self.symbol_rsi.has_inputs and self.symbol_mfi.has_inputs and
                self.btc_atr.has_inputs and self.btc_rsi.has_inputs and self.btc_mfi.has_inputs)

    def _execute_trading_decision(self) -> None:
        """执行交易决策 - 包含预测逻辑"""
        if not self.current_news_data:
            return

        try:
            # 1. 获取技术指标数据
            technical_indicators = self._get_technical_indicators()

            # 如果技术指标不可用，不能执行后续动作
            if technical_indicators is None:
                self.log.warning("技术指标不可用，跳过交易决策")
                return

            # 2. 使用ML模型进行预测
            text = f"{self.current_news_data['title']} {self.current_news_data['content']}"
            pred_class, confidence = self.predictor.predict(text, technical_indicators)

            # # 3. 检查置信度
            # if confidence < 0.6:  # 最小置信度阈值
            #     self.log.info(f"预测置信度不足: {confidence:.3f} < 0.6，跳过交易")
            #     return

            # 4. 计算交易金额
            signal_ratio = abs(pred_class - 2) / 2  # 将0-4映射到0-1
            trade_usd = float(self.max_trade_usd) * signal_ratio

            # 检查最小交易金额
            if trade_usd < float(self.min_trade_usd):
                self.log.info(f"交易金额太小: {trade_usd} < {self.min_trade_usd}")
                return

            # 5. 根据预测类型执行交易
            pred_labels = ["大跌(<-3%)", "小跌(-3%~-0.8%)", "平稳(-0.8%~+0.8%)", "小涨(+0.8%~+3%)", "大涨(>+3%)"]
            self.log.info(f"🔮 ML预测结果: {pred_labels[pred_class]}, 置信度: {confidence:.3f}, 交易金额: ${trade_usd:.2f}")

            if pred_class > 2:  # 预测上涨 (3, 4)
                # 做多symbol，做空BTC
                self._execute_long_symbol_short_btc(trade_usd)
            elif pred_class < 2:  # 预测下跌 (0, 1)
                # 做空symbol，做多BTC
                self._execute_short_symbol_long_btc(trade_usd)
            else:  # pred_class == 2，震荡
                self.log.info("预测震荡，不执行交易")

        except Exception as e:
            self.log.error(f"执行交易决策失败: {e}")
        finally:
            # 清除当前新闻数据
            self.current_news_data = None

    def _get_technical_indicators(self) -> Optional[List[float]]:
        """获取技术指标数据 (6维: symbol_NATR, symbol_MFI, symbol_RSI, btc_NATR, btc_MFI, btc_RSI)"""
        try:
            # 获取当前价格 (使用5min bar的close)
            symbol_close = self._get_current_price_from_bar(self.bar_type)
            btc_close = self._get_current_price_from_bar(self.btc_bar_type)

            if not symbol_close or not btc_close:
                self.log.warning("无法获取当前价格，技术指标不可用")
                return None

            # 检查所有技术指标是否有输入
            if not (self.symbol_atr.has_inputs and self.symbol_mfi.has_inputs and self.symbol_rsi.has_inputs and
                    self.btc_atr.has_inputs and self.btc_mfi.has_inputs and self.btc_rsi.has_inputs):
                self.log.warning("技术指标数据不足，无法进行预测")
                return None

            # Symbol技术指标
            symbol_natr = self.symbol_atr.value / symbol_close
            symbol_mfi = self.symbol_mfi.value
            symbol_rsi = self.symbol_rsi.value

            # BTC技术指标
            btc_natr = self.btc_atr.value / btc_close
            btc_mfi = self.btc_mfi.value
            btc_rsi = self.btc_rsi.value

            return [symbol_natr, symbol_mfi, symbol_rsi, btc_natr, btc_mfi, btc_rsi]

        except Exception as e:
            self.log.error(f"获取技术指标失败: {e}")
            return None

    def _get_current_price_from_bar(self, bar_type: BarType) -> Optional[float]:
        """从最新的bar获取当前价格 (close)"""
        try:
            # 获取最新的bar
            bars = self.cache.bars(bar_type)
            if bars:
                latest_bar = bars[-1]
                return float(latest_bar.close)
            return None
        except Exception as e:
            self.log.warning(f"获取价格失败: {e}")
            return None

    def _execute_long_symbol_short_btc(self, trade_usd: float) -> None:
        """做多symbol，做空BTC - 简化版本，只下市价单"""
        self.log.info(f"🟢 执行策略: 做多 {self.instrument_id.symbol}, 做空 BTC, 金额: ${trade_usd:.2f}")

        try:
            # 获取当前价格 (使用5min bar的close)
            symbol_price = self._get_current_price_from_bar(self.bar_type)
            btc_price = self._get_current_price_from_bar(self.btc_bar_type)

            if not symbol_price or not btc_price:
                self.log.error("无法获取当前价格")
                return

            # 计算交易数量
            symbol_quantity = Quantity.from_decimal(Decimal(str(trade_usd / symbol_price)))
            btc_quantity = Quantity.from_decimal(Decimal(str(trade_usd / btc_price)))

            # 做多symbol (市价单)
            symbol_order = MarketOrder(
                trader_id=self.trader_id,
                strategy_id=self.id,
                instrument_id=self.instrument_id,
                client_order_id=self.cache.client_order_id(),
                order_side=OrderSide.BUY,
                quantity=symbol_quantity,
                time_in_force=TimeInForce.IOC,
            )

            # 做空BTC (市价单)
            btc_instrument_id = InstrumentId.from_str(f"BTCUSDT-PERP.{self.instrument_id.venue}")
            btc_order = MarketOrder(
                trader_id=self.trader_id,
                strategy_id=self.id,
                instrument_id=btc_instrument_id,
                client_order_id=self.cache.client_order_id(),
                order_side=OrderSide.SELL,
                quantity=btc_quantity,
                time_in_force=TimeInForce.IOC,
            )

            # 提交订单
            self.submit_order(symbol_order)
            self.submit_order(btc_order)

            # 更新状态
            self.has_symbol_position = True
            self.has_btc_position = True

            self.log.info(f"✅ 已提交订单: 做多{symbol_quantity} {self.instrument_id.symbol}, 做空{btc_quantity} BTC")

        except Exception as e:
            self.log.error(f"执行做多symbol做空BTC失败: {e}")

    def _execute_short_symbol_long_btc(self, trade_usd: float) -> None:
        """做空symbol，做多BTC - 简化版本，只下市价单"""
        self.log.info(f"🔴 执行策略: 做空 {self.instrument_id.symbol}, 做多 BTC, 金额: ${trade_usd:.2f}")

        try:
            # 获取当前价格 (使用5min bar的close)
            symbol_price = self._get_current_price_from_bar(self.bar_type)
            btc_price = self._get_current_price_from_bar(self.btc_bar_type)

            if not symbol_price or not btc_price:
                self.log.error("无法获取当前价格")
                return

            # 计算交易数量
            symbol_quantity = Quantity.from_decimal(Decimal(str(trade_usd / symbol_price)))
            btc_quantity = Quantity.from_decimal(Decimal(str(trade_usd / btc_price)))

            # 做空symbol (市价单)
            symbol_order = MarketOrder(
                trader_id=self.trader_id,
                strategy_id=self.id,
                instrument_id=self.instrument_id,
                client_order_id=self.cache.client_order_id(),
                order_side=OrderSide.SELL,
                quantity=symbol_quantity,
                time_in_force=TimeInForce.IOC,
            )

            # 做多BTC (市价单)
            btc_instrument_id = InstrumentId.from_str(f"BTCUSDT-PERP.{self.instrument_id.venue}")
            btc_order = MarketOrder(
                trader_id=self.trader_id,
                strategy_id=self.id,
                instrument_id=btc_instrument_id,
                client_order_id=self.cache.client_order_id(),
                order_side=OrderSide.BUY,
                quantity=btc_quantity,
                time_in_force=TimeInForce.IOC,
            )

            # 提交订单
            self.submit_order(symbol_order)
            self.submit_order(btc_order)

            # 更新状态
            self.has_symbol_position = True
            self.has_btc_position = True

            self.log.info(f"✅ 已提交订单: 做空{symbol_quantity} {self.instrument_id.symbol}, 做多{btc_quantity} BTC")

        except Exception as e:
            self.log.error(f"执行做空symbol做多BTC失败: {e}")

 
    def on_bar(self, bar) -> None:
        """处理5分钟K线数据"""
        # 首次信号处理：controller初次创建策略时传入新闻，在on_bar触发_on_news_signal
        if self.first_signal and self.current_news_data and self._has_sufficient_indicators():
            self.log.info("🎯 触发首次预测信号")
            self._on_news_signal(self.current_news_data)
            self.first_signal = False  # 完成后设为False，不再触发
            return

        # 检查是否有新的技术指标数据，如果有预测信号等待处理，则执行交易决策
        if (self.current_news_data and not (self.has_symbol_position or self.has_btc_position)
            and self._has_sufficient_indicators()):
            self._execute_trading_decision()

        # 如果有持仓，进行止盈止损管理
        if self.has_symbol_position or self.has_btc_position:
            self._manage_positions(bar)

    def _manage_positions(self, bar) -> None:
        """管理持仓的止盈止损 - 两级别移动止盈 + 一个级别止损"""
        try:
            # 获取symbol和btc的仓位
            symbol_position = self.portfolio.position(self.instrument_id)
            btc_instrument_id = InstrumentId.from_str(f"BTCUSDT-PERP.{self.instrument_id.venue}")
            btc_position = self.portfolio.position(btc_instrument_id)

            if not symbol_position and not btc_position:
                return

            # 获取当前价格 (使用5min bar的close)
            symbol_price = float(bar.close)
            btc_bars = self.cache.bars(self.btc_bar_type)
            btc_price = float(btc_bars[-1].close) if btc_bars else symbol_price

            # 计算组合PnL率
            total_pnl_rate = self._calculate_combined_pnl_rate(
                symbol_position, btc_position, symbol_price, btc_price
            )

            # 更新最高利润记录
            position_key = f"{self.instrument_id.value}_combined"
            if position_key not in self.highest_profit_dict:
                self.highest_profit_dict[position_key] = total_pnl_rate
            else:
                self.highest_profit_dict[position_key] = max(
                    self.highest_profit_dict[position_key], total_pnl_rate
                )

            highest_profit = self.highest_profit_dict[position_key]

            # 执行两级别移动止盈 + 一个级别止损
            self._execute_two_tier_trailing_stop(
                symbol_position, btc_position, total_pnl_rate, highest_profit
            )

        except Exception as e:
            self.log.error(f"管理持仓失败: {e}")

    def _execute_two_tier_trailing_stop(self, symbol_position, btc_position,
                                      current_pnl_rate: float, highest_profit: float) -> None:
        """执行两级别移动止盈 + 一个级别止损"""
        try:
            # 1. 固定止损检查
            if current_pnl_rate <= -self.stop_loss_pct:
                self.log.info(f"🔴 触发固定止损: {current_pnl_rate:.3f} <= -{self.stop_loss_pct:.3f}")
                self._close_all_positions("固定止损")
                return

            # 2. 第一级移动止盈 (利润 > first_trail_profit_threshold)
            if highest_profit > self.first_trail_profit_threshold:
                drawdown_from_peak = highest_profit - current_pnl_rate
                trail_threshold = highest_profit * self.first_trail_stop_pct

                if drawdown_from_peak >= trail_threshold:
                    self.log.info(f"🟡 触发第一级移动止盈: 回撤 {drawdown_from_peak:.3f} >= {trail_threshold:.3f}")
                    self._close_all_positions("第一级移动止盈")
                    return

            # 3. 第二级移动止盈 (利润 > second_trail_profit_threshold)
            if highest_profit > self.second_trail_profit_threshold:
                drawdown_from_peak = highest_profit - current_pnl_rate
                trail_threshold = highest_profit * self.second_trail_stop_pct

                if drawdown_from_peak >= trail_threshold:
                    self.log.info(f"🟢 触发第二级移动止盈: 回撤 {drawdown_from_peak:.3f} >= {trail_threshold:.3f}")
                    self._close_all_positions("第二级移动止盈")
                    return

            # 记录当前状态
            self.log.debug(f"持仓管理: 当前PnL={current_pnl_rate:.3f}, 最高利润={highest_profit:.3f}")

        except Exception as e:
            self.log.error(f"执行止盈止损失败: {e}")

    def _close_all_positions(self, reason: str) -> None:
        """关闭所有仓位"""
        try:
            self.log.info(f"🔄 关闭所有仓位: {reason}")

            # 使用close_positions API关闭所有仓位
            self.close_positions(self.instrument_id)

            # 关闭BTC仓位
            btc_instrument_id = InstrumentId.from_str(f"BTCUSDT-PERP.{self.instrument_id.venue}")
            self.close_positions(btc_instrument_id)

            # 更新状态
            self.has_symbol_position = False
            self.has_btc_position = False

            # 清理最高利润记录
            position_key = f"{self.instrument_id.value}_combined"
            if position_key in self.highest_profit_dict:
                del self.highest_profit_dict[position_key]

            self.log.info(f"✅ 所有仓位已关闭: {reason}")

        except Exception as e:
            self.log.error(f"关闭仓位失败: {e}")

    def _calculate_combined_pnl_rate(self, symbol_position, btc_position,
                                   symbol_price: float, btc_price: float) -> float:
        """计算组合PnL率"""
        total_pnl = 0.0
        total_value = 0.0

        if symbol_position:
            symbol_pnl_rate = self._calculate_position_pnl_rate(symbol_position, symbol_price)
            symbol_value = float(symbol_position.quantity) * symbol_price
            total_pnl += symbol_pnl_rate * symbol_value
            total_value += symbol_value

        if btc_position:
            btc_pnl_rate = self._calculate_position_pnl_rate(btc_position, btc_price)
            btc_value = float(btc_position.quantity) * btc_price
            total_pnl += btc_pnl_rate * btc_value
            total_value += btc_value

        return total_pnl / total_value if total_value > 0 else 0.0

    def _calculate_position_pnl_rate(self, position, current_price: float) -> float:
        """计算单个仓位的PnL率"""
        total_fee = 2.0 * self.take_fee  # 开仓和平仓费用

        if position.entry == OrderSide.BUY:
            return current_price / float(position.avg_px_open) - 1.0 - total_fee
        else:
            return 1.0 - current_price / float(position.avg_px_open) - total_fee

    def _execute_multi_tier_stop_loss(self, symbol_position, btc_position,
                                    current_pnl_rate: float, highest_profit: float,
                                    symbol_price: float, btc_price: float) -> None:
        """执行多层止盈止损逻辑"""
        # 检查固定止损
        if current_pnl_rate < -self.stop_loss_pct:
            self.log.info(f"🛑 固定止损触发: {current_pnl_rate:.4%} < -{self.stop_loss_pct:.4%}")
            self._close_all_positions(symbol_position, btc_position, "固定止损")
            return

        # 确定当前追踪止损层级
        trail_tier = 0
        trail_pct = 0.0
        trail_name = ""

        if highest_profit > self.second_trail_profit_threshold:
            # 第3层：高级追踪止损 (20%回撤) 当利润 > 3%
            trail_tier = 3
            trail_pct = self.higher_trail_stop_loss_pct
            trail_name = "高级"
        elif highest_profit > self.first_trail_profit_threshold:
            # 第2层：标准追踪止损 (30%回撤) 当利润 > 2%
            trail_tier = 2
            trail_pct = self.trail_stop_loss_pct
            trail_name = "标准"
        elif highest_profit > self.low_trail_profit_threshold:
            # 第1层：低级追踪止损 (50%回撤) 当利润 > 1%
            trail_tier = 1
            trail_pct = self.low_trail_stop_loss_pct
            trail_name = "低级"

        # 如果有活跃的追踪止损层级，检查是否触发
        if trail_tier > 0:
            trail_threshold = highest_profit * (1.0 - trail_pct)

            if current_pnl_rate < trail_threshold:
                self.log.info(
                    f"🎯 {trail_name}追踪止损(第{trail_tier}层)触发: "
                    f"当前利润 {current_pnl_rate:.4%} < 阈值 {trail_threshold:.4%} "
                    f"(最高: {highest_profit:.4%}, 回撤: {trail_pct:.2%})"
                )
                self._close_all_positions(symbol_position, btc_position, f"{trail_name}追踪止损")
                return
            else:
                self.log.debug(
                    f"📊 活跃{trail_name}追踪止损(第{trail_tier}层): "
                    f"当前利润 {current_pnl_rate:.4%}, 阈值 {trail_threshold:.4%}, "
                    f"最高利润 {highest_profit:.4%}"
                )



    def _close_position(self, position, reason: str) -> None:
        """关闭单个仓位"""
        try:
            exit_side = OrderSide.SELL if position.entry == OrderSide.BUY else OrderSide.BUY

            order = MarketOrder(
                trader_id=self.trader_id,
                strategy_id=self.id,
                instrument_id=position.instrument_id,
                client_order_id=self.cache.client_order_id(),
                order_side=exit_side,
                quantity=position.quantity,
                time_in_force=TimeInForce.GTC,
            )

            self.submit_order(order)
            self.log.info(f"✅ 提交平仓订单: {position.instrument_id.symbol}, 原因: {reason}")

        except Exception as e:
            self.log.error(f"关闭仓位失败: {e}")

    def _notify_controller_strategy_complete(self) -> None:
        """通知controller策略完成，可以销毁"""
        try:
            # 通过msgbus发送完成信号
            self.msgbus.publish(f"strategy.{self.id}.complete", {"strategy_id": str(self.id)})
            self.log.info(f"📤 已通知controller策略完成: {self.id}")
        except Exception as e:
            self.log.error(f"通知controller失败: {e}")

    def _update_stop_orders(self, stop_loss_price: float, take_profit_price: float) -> None:
        """更新止损止盈订单"""
        try:
            instrument = self.cache.instrument(self.instrument_id)
            if not instrument:
                return
            
            # 取消现有止损止盈订单
            if self.stop_loss_order:
                self.cancel_order(self.stop_loss_order)
            if self.take_profit_order:
                self.cancel_order(self.take_profit_order)
            
            # 创建新的止损止盈订单
            if self.has_position:
                position = self.portfolio.position(self.instrument_id)
                if position:
                    # 止损订单
                    self.stop_loss_order = self.order_factory.stop_market(
                        instrument_id=self.instrument_id,
                        order_side=OrderSide.SELL if position.is_long else OrderSide.BUY,
                        quantity=position.quantity,
                        trigger_price=stop_loss_price,
                        time_in_force=TimeInForce.GTC,
                    )
                    self.submit_order(self.stop_loss_order)
                    
                    # 止盈订单
                    self.take_profit_order = self.order_factory.stop_market(
                        instrument_id=self.instrument_id,
                        order_side=OrderSide.SELL if position.is_long else OrderSide.BUY,
                        quantity=position.quantity,
                        trigger_price=take_profit_price,
                        time_in_force=TimeInForce.GTC,
                    )
                    self.submit_order(self.take_profit_order)
                    
        except Exception as e:
            self.log.error(f"更新止损止盈订单失败: {e}")

    def on_order_accepted(self, event) -> None:
        """订单被接受"""
        self.log.info(f"订单被接受: {event.order}")

    def on_order_filled(self, event) -> None:
        """订单被成交"""
        self.log.info(f"订单被成交: {event.order}")
        
        # 更新持仓状态
        position = self.portfolio.position(self.instrument_id)
        if position and position.quantity > 0:
            self.has_position = True
            self.entry_price = event.order.avg_px
            self.entry_time = event.ts_event
            self.log.info(f"🟢 进入仓位: 价格={self.entry_price}, 数量={position.quantity}")

    def on_order_canceled(self, event) -> None:
        """订单被取消"""
        self.log.info(f"订单被取消: {event.order}")

    def on_position_closed(self, event) -> None:
        """仓位被关闭"""
        self.log.info(f"仓位被关闭: {event.position}")
        
        # 重置状态
        self.has_position = False
        self.entry_price = None
        self.entry_time = None
        self.stop_loss_order = None
        self.take_profit_order = None
        
        # 通知Controller策略已无持仓
        self.msgbus.publish(f"strategy.{self.id}.position_closed", {
            "strategy_id": str(self.id),
            "timestamp": self.clock.timestamp_ns()
        })
        
        # 策略完成任务，可以销毁
        self.log.info(f"🔄 策略 {self.id} 完成交易，准备销毁")
        self.stop()

    def on_stop(self) -> None:
        """策略停止"""
        self.log.info(f"🛑 策略停止: {self.id}")
        
        # 关闭所有持仓
        if self.has_position:
            position = self.portfolio.position(self.instrument_id)
            if position:
                # 市价平仓
                order = self.order_factory.market(
                    instrument_id=self.instrument_id,
                    order_side=OrderSide.SELL if position.is_long else OrderSide.BUY,
                    quantity=position.quantity,
                    time_in_force=TimeInForce.GTC,
                )
                self.submit_order(order)
                self.log.info(f"强制平仓: {order}")

    def on_dispose(self) -> None:
        """策略销毁"""
        self.log.info(f"🗑️ 策略销毁: {self.id}")


# 回测运行函数
def run_news_pnl_backtest():
    """运行新闻PnL预测回测"""
    from nautilus_trader.backtest.engine import BacktestEngine
    from nautilus_trader.backtest.config import BacktestEngineConfig, BacktestRunConfig
    from nautilus_trader.config import LoggingConfig
    from nautilus_trader.model.identifiers import TraderId
    from nautilus_trader.persistence.config import DataCatalogConfig
    from nautilus_trader.backtest.config import BacktestDataConfig
    from nautilus_trader.backtest.node import BacktestNode
    from nautilus_trader.config import ImportableActorConfig
    from pathlib import Path
    
    # 检查必要文件
    catalog_path = Path("./catalog")
    model_path = Path("./best_model.pt")
    tokenizer_path = Path("./models/crypto_tokenizer_large_with_symbols.model")
    
    assert catalog_path.exists(), f"catalog目录不存在: {catalog_path}"
    assert model_path.exists(), f"模型文件不存在: {model_path}"
    assert tokenizer_path.exists(), f"分词器文件不存在: {tokenizer_path}"

    # 创建catalog配置
    catalog_config = DataCatalogConfig(
        path=str(catalog_path),
        fs_protocol="file",
    )
    
    # 创建数据配置
    data_config = BacktestDataConfig(
        catalog_path=str(catalog_path),
        data_cls=f"{NewsData.__module__}:{NewsData.__name__}",
        client_id="NewsDataClient",
    )
    
    # 创建Controller配置
    controller_config = ImportableActorConfig(
        actor_path=f"{NewsPnlController.__module__}:{NewsPnlController.__name__}",
        config_path=f"{NewsPnlControllerConfig.__module__}:{NewsPnlControllerConfig.__name__}",
        config={
            "model_path": str(model_path),
            "tokenizer_path": str(tokenizer_path),
            "use_torchscript": True,
            "device": "cpu",
            "trade_size": "100.0",
            "stop_loss_pct": "0.02",
            "take_profit_pct": "0.04",
            "max_positions": 5,
            "min_confidence": 0.7,
        },
    )
    
    # 创建引擎配置
    engine_config = BacktestEngineConfig(
        trader_id=TraderId("NEWS_PNL_TRADER-001"),
        logging=LoggingConfig(
            log_level="INFO",
            bypass_logging=False,
        ),
        catalogs=[catalog_config],
        actors=[controller_config],
    )
    
    # 创建回测运行配置
    run_config = BacktestRunConfig(
        engine=engine_config,
        venues=[],
        data=[data_config],
        start="2025-01-01",
        end="2025-01-31",
    )
    
    # 创建回测节点
    node = BacktestNode(configs=[run_config])
    
    print("🚀 启动新闻PnL预测回测...")
    node.run()
    print("✅ 回测完成！")


if __name__ == "__main__":
    run_news_pnl_backtest() 