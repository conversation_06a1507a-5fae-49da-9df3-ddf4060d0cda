#!/bin/bash

# 改进版批量处理所有新闻文件的脚本
# 增加了更好的错误处理、进度显示和恢复功能

set -e  # 遇到错误时退出

# 配置路径
NEWS_DATA_DIR="/root/news_train/news_data"
OUTPUT_DIR="/root/news_train/process/filter_news"
SCRIPT_DIR="/root/news_train/process/train_process"
BINANCE_SYMBOLS="/root/news_train/process/binance_symbols.json"
CRYPTO_ALIASES="/root/news_train/process/alias_symbols_processed.txt"
LOG_FILE="$SCRIPT_DIR/processing.log"
PROGRESS_FILE="$SCRIPT_DIR/progress.txt"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

log_progress() {
    echo -e "${CYAN}[PROGRESS]${NC} $1" | tee -a "$LOG_FILE"
}

# 初始化日志
init_logging() {
    echo "========================================" > "$LOG_FILE"
    echo "新闻数据批量处理日志" >> "$LOG_FILE"
    echo "开始时间: $(date)" >> "$LOG_FILE"
    echo "========================================" >> "$LOG_FILE"
}

# 检查必要文件和目录
check_prerequisites() {
    log_info "检查必要文件和目录..."
    
    local missing_files=()
    
    if [ ! -d "$NEWS_DATA_DIR" ]; then
        missing_files+=("新闻数据目录: $NEWS_DATA_DIR")
    fi
    
    if [ ! -f "$SCRIPT_DIR/news_symbol_matcher.py" ]; then
        missing_files+=("新闻符号匹配器脚本: $SCRIPT_DIR/news_symbol_matcher.py")
    fi
    
    if [ ! -f "$BINANCE_SYMBOLS" ]; then
        missing_files+=("币安符号文件: $BINANCE_SYMBOLS")
    fi
    
    if [ ! -f "$CRYPTO_ALIASES" ]; then
        missing_files+=("加密货币别名文件: $CRYPTO_ALIASES")
    fi
    
    if [ ${#missing_files[@]} -gt 0 ]; then
        log_error "缺少必要文件:"
        for file in "${missing_files[@]}"; do
            echo "  - $file"
        done
        exit 1
    fi
    
    # 创建输出目录
    mkdir -p "$OUTPUT_DIR"
    
    log_success "所有必要文件和目录检查完成"
}

# 获取所有新闻文件
get_news_files() {
    find "$NEWS_DATA_DIR" -name "panews_flash_*.csv" | sort
}

# 检查文件是否已处理
is_file_processed() {
    local input_file="$1"
    local filename=$(basename "$input_file" .csv)
    local output_file="$OUTPUT_DIR/${filename}_filtered.csv"
    
    [ -f "$output_file" ]
}

# 保存进度
save_progress() {
    local current="$1"
    local total="$2"
    echo "$current/$total" > "$PROGRESS_FILE"
}

# 读取进度
load_progress() {
    if [ -f "$PROGRESS_FILE" ]; then
        cat "$PROGRESS_FILE"
    else
        echo "0/0"
    fi
}

# 处理单个文件
process_file() {
    local input_file="$1"
    local filename=$(basename "$input_file")
    
    log_info "处理文件: $filename"
    
    # 切换到脚本目录
    cd "$SCRIPT_DIR"
    
    # 执行处理，捕获输出
    local start_time=$(date +%s)
    
    if python3 news_symbol_matcher.py \
        --input "$input_file" \
        --output-dir "$OUTPUT_DIR" \
        --binance-symbols "$BINANCE_SYMBOLS" \
        --crypto-aliases "$CRYPTO_ALIASES" 2>&1 | tee -a "$LOG_FILE"; then
        
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        
        log_success "完成处理: $filename (耗时: ${duration}秒)"
        return 0
    else
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        
        log_error "处理失败: $filename (耗时: ${duration}秒)"
        return 1
    fi
}

# 显示进度条
show_progress_bar() {
    local current="$1"
    local total="$2"
    local width=50
    local percentage=$((current * 100 / total))
    local filled=$((current * width / total))
    local empty=$((width - filled))
    
    printf "\r["
    printf "%*s" "$filled" | tr ' ' '='
    printf "%*s" "$empty" | tr ' ' '-'
    printf "] %d%% (%d/%d)" "$percentage" "$current" "$total"
}

# 显示统计信息
show_statistics() {
    local total_files="$1"
    local processed_files="$2"
    local failed_files="$3"
    local skipped_files="$4"
    local start_time="$5"
    local end_time="$6"
    
    local duration=$((end_time - start_time))
    local hours=$((duration / 3600))
    local minutes=$(((duration % 3600) / 60))
    local seconds=$((duration % 60))
    
    echo
    echo "=========================================="
    echo "           处理统计"
    echo "=========================================="
    echo "总文件数:     $total_files"
    echo "成功处理:     $processed_files"
    echo "处理失败:     $failed_files"
    echo "跳过文件:     $skipped_files"
    echo "成功率:       $(( (processed_files + skipped_files) * 100 / total_files ))%"
    echo "总耗时:       ${hours}h ${minutes}m ${seconds}s"
    echo "=========================================="
    
    # 显示输出目录信息
    if [ -d "$OUTPUT_DIR" ]; then
        local output_files=$(find "$OUTPUT_DIR" -name "*_filtered.csv" | wc -l)
        echo "生成的过滤文件数: $output_files"
        echo "输出目录: $OUTPUT_DIR"
        echo "日志文件: $LOG_FILE"
    fi
}

# 主函数
main() {
    local skip_processed=true
    local force_reprocess=false
    
    # 处理命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --force)
                force_reprocess=true
                skip_processed=false
                shift
                ;;
            --no-skip)
                skip_processed=false
                shift
                ;;
            *)
                shift
                ;;
        esac
    done
    
    echo "=========================================="
    echo "    改进版新闻数据批量处理脚本"
    echo "=========================================="
    echo "数据目录: $NEWS_DATA_DIR"
    echo "输出目录: $OUTPUT_DIR"
    echo "日志文件: $LOG_FILE"
    echo "跳过已处理: $skip_processed"
    echo "强制重新处理: $force_reprocess"
    echo "=========================================="
    echo
    
    # 初始化日志
    init_logging
    
    # 检查先决条件
    check_prerequisites
    
    # 获取所有新闻文件
    log_info "扫描新闻文件..."
    news_files=($(get_news_files))
    total_files=${#news_files[@]}
    
    if [ $total_files -eq 0 ]; then
        log_warning "未找到任何新闻文件"
        exit 0
    fi
    
    log_info "找到 $total_files 个新闻文件"
    
    # 读取之前的进度
    local previous_progress=$(load_progress)
    log_info "之前的进度: $previous_progress"
    
    echo
    
    # 处理文件
    local processed_files=0
    local failed_files=0
    local skipped_files=0
    local failed_list=()
    local start_time=$(date +%s)
    
    for i in "${!news_files[@]}"; do
        local file="${news_files[$i]}"
        local current_index=$((i + 1))
        
        # 检查是否已处理
        if [ "$skip_processed" = true ] && is_file_processed "$file"; then
            log_info "跳过已处理文件: $(basename "$file")"
            ((skipped_files++))
        else
            if process_file "$file"; then
                ((processed_files++))
            else
                ((failed_files++))
                failed_list+=("$(basename "$file")")
            fi
        fi
        
        # 保存进度
        save_progress "$current_index" "$total_files"
        
        # 显示进度条
        show_progress_bar "$current_index" "$total_files"
        
        echo  # 换行
    done
    
    local end_time=$(date +%s)
    
    # 显示统计信息
    show_statistics "$total_files" "$processed_files" "$failed_files" "$skipped_files" "$start_time" "$end_time"
    
    # 显示失败的文件
    if [ $failed_files -gt 0 ]; then
        echo
        log_warning "以下文件处理失败:"
        for failed_file in "${failed_list[@]}"; do
            echo "  - $failed_file"
        done
    fi
    
    echo
    if [ $failed_files -eq 0 ]; then
        log_success "所有文件处理完成！"
    else
        log_warning "处理完成，但有 $failed_files 个文件失败"
    fi
    
    # 清理进度文件
    rm -f "$PROGRESS_FILE"
}

# 处理命令行参数
case "${1:-}" in
    --help|-h)
        echo "用法: $0 [选项]"
        echo
        echo "选项:"
        echo "  --help, -h     显示此帮助信息"
        echo "  --dry-run      仅显示将要处理的文件，不实际处理"
        echo "  --force        强制重新处理已存在的文件"
        echo "  --no-skip      不跳过已处理的文件"
        echo
        echo "环境变量:"
        echo "  NEWS_DATA_DIR  新闻数据目录 (默认: /root/news_train/news_data)"
        echo "  OUTPUT_DIR     输出目录 (默认: /root/news_train/process/filter_news)"
        exit 0
        ;;
    --dry-run)
        log_info "干运行模式 - 仅显示将要处理的文件"
        news_files=($(get_news_files))
        echo "将要处理的文件 (${#news_files[@]} 个):"
        for file in "${news_files[@]}"; do
            filename=$(basename "$file")
            if is_file_processed "$file"; then
                echo "  - $filename (已处理)"
            else
                echo "  - $filename (待处理)"
            fi
        done
        exit 0
        ;;
esac

# 执行主函数
main "$@"
