"""
新闻数据类型定义

用于新闻PnL控制器的数据类型
"""

from nautilus_trader.core.data import Data
from nautilus_trader.model.identifiers import InstrumentId


class NewsData(Data):
    """新闻数据类型"""
    
    def __init__(
        self,
        title: str,
        content: str,
        source: str,
        instrument_id: InstrumentId,
        ts_event: int,
        ts_init: int,
    ):
        super().__init__(ts_event, ts_init)
        self.title = title
        self.content = content
        self.source = source
        self.instrument_id = instrument_id
    
    def __str__(self) -> str:
        return f"NewsData(title='{self.title[:50]}...', source='{self.source}', instrument_id={self.instrument_id})"
    
    def __repr__(self) -> str:
        return self.__str__()


class NewsPnlPrediction(Data):
    """新闻PnL预测结果"""
    
    def __init__(
        self,
        news_id: int,
        symbol: str,
        pnl_type: int,
        confidence: float,
        ts_event: int,
        ts_init: int,
    ):
        super().__init__(ts_event, ts_init)
        self.news_id = news_id
        self.symbol = symbol
        self.pnl_type = pnl_type
        self.confidence = confidence
    
    def __str__(self) -> str:
        return f"NewsPnlPrediction(symbol='{self.symbol}', pnl_type={self.pnl_type}, confidence={self.confidence:.3f})"
    
    def __repr__(self) -> str:
        return self.__str__()
