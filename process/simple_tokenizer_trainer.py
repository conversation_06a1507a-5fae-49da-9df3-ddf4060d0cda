#!/usr/bin/env python3
"""
简化版tokenizer训练脚本
"""

import pandas as pd
import os
import glob
import sentencepiece as spm
from pathlib import Path
import tempfile


def load_news_data():
    """加载新闻数据"""
    print("=== 加载新闻数据 ===")
    
    # 从过滤新闻文件加载
    news_files = glob.glob("filter_news/panews_flash_*_filtered.csv")
    print(f"找到 {len(news_files)} 个过滤新闻文件")
    
    if not news_files:
        print("没有找到过滤新闻文件，尝试使用训练数据")
        if os.path.exists("news_training_samples_complete_all.csv"):
            df = pd.read_csv("news_training_samples_complete_all.csv")
            return df[['news_title', 'news_content']].dropna()
        else:
            raise FileNotFoundError("没有找到任何新闻数据文件")
    
    # 读取前10个文件作为训练数据
    dfs = []
    for file_path in news_files[:10]:
        try:
            df = pd.read_csv(file_path)
            if 'title' in df.columns and 'content' in df.columns:
                dfs.append(df[['title', 'content']].dropna())
                print(f"  加载: {os.path.basename(file_path)} ({len(df)} 条)")
        except Exception as e:
            print(f"  跳过: {file_path} - {e}")
    
    if not dfs:
        raise ValueError("没有成功加载任何新闻数据")
    
    combined_df = pd.concat(dfs, ignore_index=True)
    print(f"总计加载 {len(combined_df)} 条新闻")
    
    return combined_df


def prepare_training_text(df):
    """准备训练文本"""
    print("=== 准备训练文本 ===")
    
    # 合并标题和内容
    texts = []
    for _, row in df.iterrows():
        title = str(row.get('title', '') or row.get('news_title', ''))
        content = str(row.get('content', '') or row.get('news_content', ''))
        
        if title and title != 'nan':
            texts.append(title)
        if content and content != 'nan':
            texts.append(content)
    
    # 过滤空文本
    texts = [text.strip() for text in texts if text.strip() and text.strip() != 'nan']
    
    print(f"准备了 {len(texts)} 条训练文本")
    return texts


def train_sentencepiece_tokenizer(texts, vocab_size=4000, model_name="crypto_tokenizer_simple"):
    """训练SentencePiece tokenizer"""
    print(f"=== 训练SentencePiece tokenizer (vocab_size={vocab_size}) ===")
    
    # 创建models目录
    models_dir = Path("models")
    models_dir.mkdir(exist_ok=True)
    
    # 创建临时训练文件
    with tempfile.NamedTemporaryFile(mode='w', encoding='utf-8', delete=False, suffix='.txt') as f:
        for text in texts:
            f.write(text + '\n')
        temp_file = f.name
    
    try:
        # 训练参数
        model_prefix = str(models_dir / model_name)
        
        training_args = [
            f'--input={temp_file}',
            f'--model_prefix={model_prefix}',
            f'--vocab_size={vocab_size}',
            '--model_type=unigram',
            '--character_coverage=0.995',
            '--max_sentence_length=4192',
            '--pad_id=0',
            '--unk_id=1',
            '--bos_id=2',
            '--eos_id=3',
            '--pad_piece=[PAD]',
            '--unk_piece=[UNK]',
            '--bos_piece=[CLS]',
            '--eos_piece=[SEP]',
        ]
        
        # 训练
        spm.SentencePieceTrainer.train(' '.join(training_args))
        
        model_path = f"{model_prefix}.model"
        print(f"✅ 训练完成: {model_path}")
        
        return model_path
        
    finally:
        # 清理临时文件
        if os.path.exists(temp_file):
            os.unlink(temp_file)


def test_tokenizer(model_path):
    """测试tokenizer"""
    print("=== 测试tokenizer ===")
    
    # 加载模型
    sp = spm.SentencePieceProcessor()
    sp.load(model_path)
    
    # 测试文本
    test_texts = [
        "BTC价格突破历史新高，市场看多情绪高涨",
        "ETH 2.0质押量持续增长，DeFi生态繁荣发展",
        "某交易所推出创新型NFT交易平台",
        "比特币合约未平仓头寸降至570亿美元",
        "Binance将上线新的代币交易对",
        "去中心化金融协议完成3000万美元融资"
    ]
    
    print(f"词汇表大小: {sp.vocab_size()}")
    
    for i, text in enumerate(test_texts, 1):
        tokens = sp.encode_as_pieces(text)
        print(f"\n测试 {i}:")
        print(f"原文: {text}")
        print(f"分词: {tokens}")
        print(f"Token数: {len(tokens)}")


def main():
    """主函数"""
    print("🚀 开始训练简化版tokenizer")
    
    try:
        # 1. 加载数据
        df = load_news_data()
        
        # 2. 准备文本
        texts = prepare_training_text(df)
        
        if len(texts) < 100:
            print("⚠️ 训练文本太少，可能影响质量")
        
        # 3. 训练tokenizer
        model_path = train_sentencepiece_tokenizer(texts)
        
        # 4. 测试tokenizer
        test_tokenizer(model_path)
        
        print(f"\n✅ 训练完成！模型文件: {model_path}")
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
