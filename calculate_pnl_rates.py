import os
import csv
import json
import time
import urllib.request
import urllib.parse
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

# --- Configuration ---
INPUT_DIR = 'news_data_filtered'
OUTPUT_DIR = 'news_data_with_pnl_new'
BINANCE_KLINES_URL = "https://fapi.binance.com/fapi/v1/klines"

# Cache for kline data to avoid repeated API calls
kline_cache = {}

def fetch_url_with_retry(url: str, max_retries: int = 3, delay: float = 1.0) -> Optional[dict]:
    """
    Fetch data from URL with retry mechanism.
    """
    for attempt in range(max_retries):
        try:
            with urllib.request.urlopen(url, timeout=10) as response:
                if response.status == 200:
                    return json.loads(response.read().decode('utf-8'))
                else:
                    print(f"HTTP Error {response.status} for {url}")
        except Exception as e:
            print(f"Attempt {attempt + 1} failed for {url}: {e}")
            if attempt < max_retries - 1:
                time.sleep(delay * (2 ** attempt))  # Exponential backoff
    return None

def convert_utc8_to_utc0_timestamp(date_str: str, time_str: str, filename: str) -> Optional[int]:
    """
    Convert UTC+8 time to UTC+0 timestamp in milliseconds.
    
    Args:
        date_str: Date string like "1月1日"
        time_str: Time string like "23:45"
        filename: Filename to extract year
    
    Returns:
        UTC timestamp in milliseconds, or None if parsing fails
    """
    try:
        # Extract year from filename
        year_str = filename.split('_')[2][:4]
        
        # Convert Chinese date format to standard format
        full_datetime_str = f"{year_str}-{date_str.replace('月', '-').replace('日', '')} {time_str}"
        
        # Parse as naive datetime (assuming UTC+8)
        naive_dt = datetime.strptime(full_datetime_str, '%Y-%m-%d %H:%M')
        
        # Convert UTC+8 to UTC+0
        utc_dt = naive_dt - timedelta(hours=8)
        
        # Return timestamp in milliseconds
        return int(utc_dt.timestamp() * 1000)
    except Exception as e:
        print(f"Error parsing timestamp {date_str} {time_str}: {e}")
        return None

def get_kline_data(symbol: str, start_time: int, end_time: int) -> Optional[List]:
    """
    Get 5-minute kline data from Binance API.
    
    Args:
        symbol: Trading symbol (e.g., "BTCUSDT")
        start_time: Start timestamp in milliseconds
        end_time: End timestamp in milliseconds
    
    Returns:
        List of kline data or None if failed
    """
    # Create cache key
    cache_key = f"{symbol}_{start_time}_{end_time}"
    
    # Check cache first
    if cache_key in kline_cache:
        return kline_cache[cache_key]
    
    # Build API URL
    params = {
        'symbol': symbol,
        'interval': '5m',
        'startTime': start_time,
        'endTime': end_time,
        'limit': 1000  # Maximum limit
    }
    
    url = f"{BINANCE_KLINES_URL}?{urllib.parse.urlencode(params)}"
    
    print(f"Fetching kline data for {symbol} from {start_time} to {end_time}")

    # Add delay before API call to avoid rate limiting
    time.sleep(0.2)

    # Fetch data
    data = fetch_url_with_retry(url)

    if data:
        # Cache the result
        kline_cache[cache_key] = data
        print(f"Successfully fetched {len(data)} kline records for {symbol}")
        return data
    else:
        print(f"Failed to fetch kline data for {symbol}")
        return None

def find_closest_price(kline_data: List, target_timestamp: int) -> Optional[float]:
    """
    Find the closest price to the target timestamp.
    
    Args:
        kline_data: List of kline records
        target_timestamp: Target timestamp in milliseconds
    
    Returns:
        Close price or None if not found
    """
    if not kline_data:
        return None
    
    closest_kline = None
    min_diff = float('inf')
    
    for kline in kline_data:
        kline_timestamp = int(kline[0])  # Open time
        diff = abs(kline_timestamp - target_timestamp)
        
        if diff < min_diff:
            min_diff = diff
            closest_kline = kline
    
    if closest_kline:
        return float(closest_kline[4])  # Close price
    
    return None

def calculate_price_change_rates(symbol: str, news_timestamp: int) -> Dict[str, Optional[float]]:
    """
    Calculate price change rates for different time periods.
    
    Args:
        symbol: Trading symbol
        news_timestamp: News timestamp in milliseconds (UTC)
    
    Returns:
        Dictionary with price change rates for different periods
    """
    # Define time periods in milliseconds
    periods = {
        '1h': 60 * 60 * 1000,
        '4h': 4 * 60 * 60 * 1000,
        '6h': 6 * 60 * 60 * 1000,
        '12h': 12 * 60 * 60 * 1000,
        '24h': 24 * 60 * 60 * 1000
    }
    
    results = {}
    
    # Get kline data for the required time range
    # We need data from news time to 24h after news time
    start_time = news_timestamp
    end_time = news_timestamp + periods['24h'] + 60 * 60 * 1000  # Add 1h buffer
    
    kline_data = get_kline_data(symbol, start_time, end_time)
    
    if not kline_data:
        # Return None for all periods if no data
        return {period: None for period in periods.keys()}
    
    # Find the base price (closest to news timestamp)
    base_price = find_closest_price(kline_data, news_timestamp)
    
    if base_price is None:
        return {period: None for period in periods.keys()}
    
    # Calculate price change rates for each period
    for period_name, period_ms in periods.items():
        target_timestamp = news_timestamp + period_ms
        target_price = find_closest_price(kline_data, target_timestamp)
        
        if target_price is not None:
            # Calculate percentage change
            change_rate = ((target_price - base_price) / base_price) * 100
            results[period_name] = round(change_rate, 4)
        else:
            results[period_name] = None
    
    return results

def format_pnl_rate(symbols: List[str], pnl_data: Dict[str, Dict[str, Optional[float]]]) -> str:
    """
    Format PnL rate string for multiple symbols.
    
    Args:
        symbols: List of symbols
        pnl_data: Dictionary mapping symbol to its PnL rates
    
    Returns:
        Formatted PnL rate string
    """
    if len(symbols) == 1:
        symbol = symbols[0]
        rates = pnl_data.get(symbol, {})
        # Format: 1h:x.xx%,4h:x.xx%,6h:x.xx%,12h:x.xx%,24h:x.xx%
        parts = []
        for period in ['1h', '4h', '6h', '12h', '24h']:
            rate = rates.get(period)
            if rate is not None:
                parts.append(f"{period}:{rate:+.2f}%")
            else:
                parts.append(f"{period}:N/A")
        return ','.join(parts)
    else:
        # Multiple symbols: use a,b,c format
        result_parts = []
        for i, symbol in enumerate(symbols):
            label = chr(ord('a') + i)  # a, b, c, ...
            rates = pnl_data.get(symbol, {})
            
            parts = []
            for period in ['1h', '4h', '6h', '12h', '24h']:
                rate = rates.get(period)
                if rate is not None:
                    parts.append(f"{period}:{rate:+.2f}%")
                else:
                    parts.append(f"{period}:N/A")
            
            result_parts.append(f"{label}:({','.join(parts)})")
        
        return ';'.join(result_parts)

def process_csv_file(input_path: str, output_path: str) -> None:
    """
    Process a single CSV file to add PnL rates.
    
    Args:
        input_path: Path to input CSV file
        output_path: Path to output CSV file
    """
    filename = os.path.basename(input_path)
    print(f"\nProcessing {filename}...")
    
    try:
        with open(input_path, 'r', newline='', encoding='utf-8-sig') as infile, \
             open(output_path, 'w', newline='', encoding='utf-8-sig') as outfile:
            
            reader = csv.reader(infile)
            writer = csv.writer(outfile)
            
            # Read header and add pnl_rate column
            header = next(reader)
            header.append('pnl_rate')
            writer.writerow(header)
            
            # Get column indices
            date_index = header.index('date')
            time_index = header.index('time')
            symbols_index = header.index('relative_symbols')
            
            processed_count = 0
            
            for row in reader:
                # Convert news timestamp to UTC
                news_timestamp = convert_utc8_to_utc0_timestamp(
                    row[date_index], row[time_index], filename
                )
                
                if news_timestamp is None:
                    # Skip if timestamp conversion failed
                    row.append('N/A')
                    writer.writerow(row)
                    continue
                
                # Parse symbols
                symbols_str = row[symbols_index]
                if not symbols_str or symbols_str.strip() == '':
                    row.append('N/A')
                    writer.writerow(row)
                    continue
                
                # Split symbols (handle comma-separated and quoted strings)
                symbols = [s.strip().strip('"') for s in symbols_str.split(',')]
                symbols = [s for s in symbols if s]  # Remove empty strings
                
                if not symbols:
                    row.append('N/A')
                    writer.writerow(row)
                    continue
                
                # Calculate PnL rates for each symbol
                pnl_data = {}
                for symbol in symbols:
                    print(f"  Calculating PnL for {symbol}...")
                    pnl_rates = calculate_price_change_rates(symbol, news_timestamp)
                    pnl_data[symbol] = pnl_rates

                    # Add delay to avoid rate limiting (longer for multiple symbols)
                    time.sleep(0.5)
                
                # Format PnL rate string
                pnl_rate_str = format_pnl_rate(symbols, pnl_data)
                row.append(pnl_rate_str)
                writer.writerow(row)
                
                processed_count += 1
                if processed_count % 10 == 0:
                    print(f"  Processed {processed_count} rows...")
            
            print(f"Successfully processed {processed_count} rows in {filename}")
    
    except Exception as e:
        print(f"Error processing {filename}: {e}")

def main():
    """Main function to process all CSV files."""
    print("Starting PnL rate calculation process...")

    # Create output directory
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)
        print(f"Created output directory: {OUTPUT_DIR}")

    # Get all CSV files to process
    all_files = [f for f in sorted(os.listdir(INPUT_DIR)) if f.endswith('.csv')]
    total_files = len(all_files)

    print(f"Found {total_files} CSV files to process")

    # Process all CSV files in input directory
    processed_files = 0
    skipped_files = 0

    for i, filename in enumerate(all_files, 1):
        input_file_path = os.path.join(INPUT_DIR, filename)
        output_file_path = os.path.join(OUTPUT_DIR, filename)

        print(f"\n[{i}/{total_files}] Processing {filename}...")

        # Skip if output file already exists
        if os.path.exists(output_file_path):
            print(f"Skipping {filename} - already processed")
            skipped_files += 1
            continue

        process_csv_file(input_file_path, output_file_path)
        processed_files += 1

        # Add longer delay between files to avoid Binance IP blocking
        if i < total_files:  # Don't wait after the last file
            print(f"Waiting 5 seconds before processing next file...")
            time.sleep(5)

    print(f"\n=== Processing Complete ===")
    print(f"Total files found: {total_files}")
    print(f"Files processed: {processed_files}")
    print(f"Files skipped (already processed): {skipped_files}")
    print(f"Results saved to '{OUTPUT_DIR}' directory.")
    print(f"Cache contains {len(kline_cache)} kline data entries.")

if __name__ == '__main__':
    main()
