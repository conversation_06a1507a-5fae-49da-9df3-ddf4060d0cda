import polars as pl
import pandas as pd
from datetime import datetime, timedelta, timezone
import os

def debug_single_news_item():
    """Debug a single news item processing"""
    print("🔍 Debugging Single News Item Processing")
    print("=" * 50)
    
    # Load news data
    news_file = "/root/news_train/filter_news/panews_flash_20250101.csv"
    news_df = pd.read_csv(news_file)
    print(f"📰 Loaded {len(news_df)} news items")
    
    # Find first non-BTC news
    for idx, row in news_df.iterrows():
        symbols = str(row['relative_symbols']).strip()
        if symbols != 'BTCUSDT' and not pd.isna(symbols) and symbols != 'nan':
            print(f"\n📰 Processing news item {idx}:")
            print(f"   Title: {row['title'][:60]}...")
            print(f"   Time: {row['date']} {row['time']}")
            print(f"   Symbols: {symbols}")
            
            # Test time conversion
            try:
                date_str = row['date']
                time_str = row['time']
                
                # Parse Chinese date format
                month_day = str(date_str).replace("月", "-").replace("日", "")
                if "-" in month_day:
                    month, day = month_day.split("-")
                    year = 2025
                    
                    dt_utc8 = datetime(year, int(month), int(day))
                    
                    if ":" in str(time_str):
                        hour, minute = str(time_str).split(":")
                        dt_utc8 = dt_utc8.replace(hour=int(hour), minute=int(minute))
                    
                    dt_utc0 = dt_utc8 - timedelta(hours=8)
                    print(f"   ✅ UTC+8: {dt_utc8}")
                    print(f"   ✅ UTC+0: {dt_utc0}")
                    
                    # Find next 5-minute timestamp
                    minute = dt_utc0.minute
                    next_5min = ((minute // 5) + 1) * 5
                    
                    if next_5min >= 60:
                        target_time = dt_utc0.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
                    else:
                        target_time = dt_utc0.replace(minute=next_5min, second=0, microsecond=0)
                    
                    print(f"   ✅ Target: {target_time}")
                    
                    # Test symbol loading
                    symbol = symbols.split(',')[0].strip()
                    if symbol != 'BTCUSDT':
                        file_path = f"/root/news_train/data/{symbol}_5m_relative_labeled.parquet"
                        print(f"   📁 Looking for: {file_path}")
                        
                        if os.path.exists(file_path):
                            print(f"   ✅ File exists!")
                            
                            try:
                                df = pl.read_parquet(file_path)
                                print(f"   ✅ Loaded {df.height} records")
                                
                                # Test filtering
                                target_with_tz = target_time.replace(tzinfo=timezone.utc)
                                filtered_df = df.filter(pl.col("timestamp") >= target_with_tz)
                                print(f"   ✅ Found {filtered_df.height} records after {target_with_tz}")
                                
                                if filtered_df.height > 0:
                                    match_row = filtered_df.sort("timestamp").head(1)
                                    row_dict = match_row.to_dicts()[0]
                                    print(f"   ✅ Match found:")
                                    print(f"      Timestamp: {row_dict['timestamp']}")
                                    print(f"      Label: {row_dict.get('label')}")
                                    print(f"      Close: {row_dict.get('close')}")
                                    print(f"      Relative Price: {row_dict.get('relative_price')}")
                                else:
                                    print(f"   ❌ No matching timestamp found")
                                
                            except Exception as e:
                                print(f"   ❌ Error loading parquet: {e}")
                        else:
                            print(f"   ❌ File not found")
                    
                    break
                    
            except Exception as e:
                print(f"   ❌ Time conversion error: {e}")
                continue

if __name__ == "__main__":
    debug_single_news_item()