"""
Enhanced Elegant Order Manager with Trail Stop functionality.

This module extends the elegant_order_manager.py with advanced trailing stop functionality
inspired by chua_bn.py. It provides a comprehensive order management system with
sophisticated position management capabilities.
"""

from typing import Dict, List, Optional, Tuple, Any, Callable, Set, Union
from decimal import Decimal
import pandas as pd
import numpy as np
import uuid
from enum import Enum

from nautilus_trader.common.component import Clock
from nautilus_trader.core.message import Event
from nautilus_trader.model.enums import OrderSide, OrderType, TimeInForce, OrderStatus
from nautilus_trader.model.identifiers import InstrumentId, ClientOrderId, PositionId, TradeId
from nautilus_trader.model.instruments import Instrument
from nautilus_trader.model.orders import Order
from nautilus_trader.model.orders.limit import LimitOrder
from nautilus_trader.model.orders.market import MarketOrder
from nautilus_trader.model.position import Position
from nautilus_trader.model.events.position import PositionOpened, PositionClosed,PositionChanged
from nautilus_trader.model.events.order import OrderFilled, OrderCanceled, OrderRejected
from nautilus_trader.model.objects import Price, Quantity
from nautilus_trader.trading.strategy import Strategy


class EnhancedOrderManager:
    """
    Enhanced Order Manager with Trail Stop functionality.

    This class extends the elegant_order_manager.py with advanced trailing stop functionality
    inspired by chua_bn.py. It provides a comprehensive order management system with
    sophisticated position management capabilities.

    Features:
    - Signal-based order tracking with position mapping
    - Unified handling of Maker and Taker orders
    - Efficient order state management with modification capability
    - Clean event handling with callback mechanisms
    - Performance analytics and execution quality metrics
    - Advanced trailing stop functionality with multiple thresholds
    """

    def __init__(
        self,
        strategy: Strategy,
        min_update_interval_seconds: int,
        stop_loss_pct: float = 0.02,
        low_trail_stop_loss_pct: float = 0.5,
        trail_stop_loss_pct: float = 0.3,
        higher_trail_stop_loss_pct: float = 0.2,
        low_trail_profit_threshold: float = 0.01,
        first_trail_profit_threshold: float = 0.02,
        second_trail_profit_threshold: float = 0.03,
        take_fee: float = 0.0004,
    ):
        """
        Initialize the EnhancedOrderManager.

        Parameters
        ----------
        strategy : Strategy
            The strategy instance that will use this order manager
        min_update_interval_seconds : int
            Minimum interval between order updates in seconds
        stop_loss_pct : float
            Stop loss percentage (e.g., 0.02 for 2%)
        low_trail_stop_loss_pct : float
            Low trailing stop loss percentage (e.g., 0.5 for 50% of profit)
        trail_stop_loss_pct : float
            Standard trailing stop loss percentage (e.g., 0.3 for 30% of profit)
        higher_trail_stop_loss_pct : float
            Higher trailing stop loss percentage (e.g., 0.2 for 20% of profit)
        low_trail_profit_threshold : float
            Profit threshold for low trailing stop (e.g., 0.01 for 1%)
        first_trail_profit_threshold : float
            First profit threshold for standard trailing stop (e.g., 0.02 for 2%)
        second_trail_profit_threshold : float
            Second profit threshold for higher trailing stop (e.g., 0.03 for 3%)
        take_fee : float
            Trading fee percentage (e.g., 0.0004 for 0.04%)
        """
        self.strategy = strategy
        self.min_update_interval_seconds = min_update_interval_seconds
        self.stop_loss_pct = stop_loss_pct
        self.low_trail_stop_loss_pct = low_trail_stop_loss_pct
        self.trail_stop_loss_pct = trail_stop_loss_pct
        self.higher_trail_stop_loss_pct = higher_trail_stop_loss_pct
        self.low_trail_profit_threshold = low_trail_profit_threshold
        self.first_trail_profit_threshold = first_trail_profit_threshold
        self.second_trail_profit_threshold = second_trail_profit_threshold
        self.take_fee = take_fee

        # Entry Signal tracking
        # {"instrument_id_value": {OrderSide.BUY: Order, OrderSide.SELL: Order}}
        self.entry_signals: Dict[str, Dict[OrderSide, Order]] = {}
        self.exit_signals: Dict[str, Dict[OrderSide, Order]] = {}

        # Position tracking
        self.highest_profit_of_position_dict: Dict[str, float] = {}  # Track highest profit for each position

        # Logger
        self.log = strategy.log

    def current_pnl_rate(self, position: Position, current_price: float) -> float:
        """
        Calculate the current profit and loss rate for a position, accounting for trading fees.

        Parameters
        ----------
        position : Position
            The position to calculate PnL for
        current_price : float
            Current market price

        Returns
        -------
        float
            The PnL rate as a decimal (e.g., 0.01 for 1% profit)
        """
        total_fee = 2.0 * self.take_fee  # 2.0 accounts for entry and exit fees

        if position.entry == OrderSide.BUY:
            return current_price / float(position.avg_px_open) - 1.0 - total_fee
        else:
            return 1.0 - current_price / float(position.avg_px_open) - total_fee

    def update_highest_profit_of_position(self, position: Position, current_price: float) -> None:
        """
        Update the highest profit achieved for a position.

        Parameters
        ----------
        position : Position
            The position to update
        current_price : float
            Current market price
        """
        current_profit = self.current_pnl_rate(position, current_price)
        position_id = str(position.id)

        if position_id not in self.highest_profit_of_position_dict:
            self.highest_profit_of_position_dict[position_id] = current_profit
        else:
            self.highest_profit_of_position_dict[position_id] = max(
                self.highest_profit_of_position_dict[position_id],
                current_profit
            )

    def _place_limit_order(
        self,
        instrument: Instrument,
        side: OrderSide,
        price: float,
        size: float,
        post_only: bool = True,
        reduce_only: bool = False,
        time_in_force: TimeInForce = TimeInForce.GTC,
    ) -> Optional[LimitOrder]:
        """
        Place a limit order.

        Parameters
        ----------
        instrument : Instrument
            The trading instrument
        side : OrderSide
            The order side (BUY or SELL)
        price : float
            The order price
        size : float
            The order size
        post_only : bool
            Whether to use post_only flag
        reduce_only : bool
            Whether to use reduce_only flag
        time_in_force : TimeInForce
            Time in force for the order

        Returns
        -------
        Optional[LimitOrder]
            The placed order, or None if no order was placed
        """
        order = self.strategy.order_factory.limit(
            instrument_id=instrument.id,
            price=instrument.make_price(price),
            order_side=side,
            quantity=instrument.make_qty(size),
            post_only=post_only,
            reduce_only=reduce_only,
            time_in_force=time_in_force,
        )
        self.strategy.submit_order(order)
        return order

    def _place_market_order(
        self,
        instrument: Instrument,
        side: OrderSide,
        size: float,
    ) -> Optional[MarketOrder]:
        """
        Place a market order.

        Parameters
        ----------
        instrument : Instrument
            The trading instrument
        side : OrderSide
            The order side (BUY or SELL)
        size : float
            The order size

        Returns
        -------
        Optional[MarketOrder]
            The placed order, or None if no order was placed
        """
        order = self.strategy.order_factory.market(
            instrument_id=instrument.id,
            order_side=side,
            quantity=instrument.make_qty(size),
        )
        self.strategy.submit_order(order)
        return order

    def on_event(self, event: Event) -> None:
        """
        Process events related to orders and positions.

        Parameters
        ----------
        event : Event
            The event to process
        """
        if isinstance(event, PositionClosed):
            self._on_position_closed(event)
        # elif isinstance(event, PositionOpened | PositionChanged):
        #     self._on_position_opened_or_changed(event, tp_price)

    def _on_position_opened_or_changed(self, event: Union[PositionOpened, PositionChanged], tp_price: float) -> None:
        """
        Handle position opened or changed events.

        Parameters
        ----------
        event : Union[PositionOpened, PositionChanged]
            The position opened or changed event
        tp_price : float
            The take profit price for the position
        """
        instrument = self.strategy.cache.instrument(event.instrument_id)
        #try to place tp order for position
        exit_side = OrderSide.SELL if event.entry == OrderSide.BUY else OrderSide.BUY
        self.process_signal(
            self.exit_signals,
            instrument=instrument,
            side=exit_side,
            price=tp_price,
            size=event.quantity.as_double(),
            is_maker=True,
            time_in_force=TimeInForce.GTC,
            force_update=True
        )

    def _on_position_closed(self, event: PositionClosed) -> None:
        """
        Handle position closed events.

        Parameters
        ----------
        event : PositionClosed
            The position closed event
        """
        position_id = str(event.position_id)
        instrument_id_value = event.instrument_id.value

        # Clean up highest profit tracking
        if position_id in self.highest_profit_of_position_dict:
            del self.highest_profit_of_position_dict[position_id]

        # Check if the position was opened by an entry signal
        if event.entry in self.entry_signals.get(instrument_id_value, {}):
            entry_order = self.entry_signals[instrument_id_value][event.entry]
            #self.log.info(f"the event opening order id is {event.opening_order_id} and the entry order id is {entry_order.client_order_id}")
            if str(event.opening_order_id) == str(entry_order.client_order_id):
                # Remove the entry signal
                del self.entry_signals[instrument_id_value][event.entry]

                # Cancel any exit signals for this position
                exit_side = OrderSide.SELL if event.entry == OrderSide.BUY else OrderSide.BUY
                if exit_side in self.exit_signals.get(instrument_id_value, {}):
                    exit_order = self.exit_signals[instrument_id_value][exit_side]
                    if exit_order.is_open or exit_order.is_active_local:
                        self.strategy.cancel_order(order=exit_order)
                    del self.exit_signals[instrument_id_value][exit_side]

        # Check if the position was closed by an exit signal
        if event.entry in self.exit_signals.get(instrument_id_value, {}):
            exit_order = self.exit_signals[instrument_id_value][event.entry]
            if str(event.opening_order_id) == str(exit_order.client_order_id):
                del self.exit_signals[instrument_id_value][event.entry]

    def cancel_signal(
        self,
        order_dict: Dict[str, Dict[OrderSide, Order]],
        instrument: Instrument,
        side: OrderSide
    ) -> None:
        """
        Cancel a signal order.

        Parameters
        ----------
        order_dict : Dict[str, Dict[OrderSide, Order]]
            The order dictionary containing the orders
        instrument : Instrument
            The trading instrument
        side : OrderSide
            The order side (BUY or SELL)
        """
        # Check if the order exists in the dictionary
        if side in order_dict.get(instrument.id.value, {}):
            # Cancel the order
            order = order_dict[instrument.id.value][side]
            if order.is_open or order.is_active_local:
                self.strategy.cancel_order(order=order)
                self.log.info(f"Canceled {side} order {order.client_order_id} for {instrument.id.value}")
            # Remove the order from the dictionary
            del order_dict[instrument.id.value][side]

    def process_signal(
        self,
        order_dict: Dict[str, Dict[OrderSide, Order]],
        instrument: Instrument,
        side: OrderSide,
        price: float,
        size: float,
        is_maker: Optional[bool] = None,
        time_in_force: TimeInForce = TimeInForce.GTC,
        force_update: bool = False,
    ) -> Order:
        """
        Process a signal and place or update an order.

        Parameters
        ----------
        order_dict : Dict[str, Dict[OrderSide, Order]]
            The order dictionary to store the order in
        instrument : Instrument
            The trading instrument
        side : OrderSide
            The order side (BUY or SELL)
        price : float
            The order price
        size : float
            The order size
        is_maker : Optional[bool]
            Whether the order is a maker order
        time_in_force : TimeInForce
            Time in force for the order
        force_update : bool
            Whether to force update the order

        Returns
        -------
        Order
            The created or updated order
        """
        if is_maker is None:
            is_maker = True  # Default to maker orders

        # Initialize the instrument entry in the dictionary if it doesn't exist
        if instrument.id.value not in order_dict:
            order_dict[instrument.id.value] = {}

        # Check if we already have an order for this instrument and side
        existing_order = order_dict.get(instrument.id.value, {}).get(side)

        if existing_order:
            # Check if the order is closed or being canceled
            if existing_order.is_closed or existing_order.is_pending_cancel:
                # Remove the old order and create a new one
                del order_dict[instrument.id.value][side]
                return self.create_new_order(
                    order_dict=order_dict,
                    instrument=instrument,
                    side=side,
                    price=price,
                    size=size,
                    is_maker=is_maker,
                    time_in_force=time_in_force,
                )
            else:
                # Update the existing order
                self.update_existing_order(
                    instrument=instrument,
                    order=existing_order,
                    price=price,
                    size=size,
                    force_update=force_update,
                )
                return existing_order
        else:
            # Create a new order
            return self.create_new_order(
                order_dict=order_dict,
                instrument=instrument,
                side=side,
                price=price,
                size=size,
                is_maker=is_maker,
                time_in_force=time_in_force,
            )

    def update_existing_order(
        self,
        instrument: Instrument,
        order: Order,
        price: float,
        size: float,
        force_update: bool = False,
    ) -> None:
        """
        Update an existing order.

        Parameters
        ----------
        instrument : Instrument
            The trading instrument
        order : Order
            The order to update
        price : float
            The new order price
        size : float
            The new order size
        force_update : bool
            Whether to force update the order
        """
        if isinstance(order, MarketOrder):
            return # Market orders cannot be modified
        # Check if we need to rate limit updates
        if not force_update and self.strategy.clock.timestamp_ns() - order.ts_last < 1e9*self.min_update_interval_seconds:
            return

        # Check if we need to update the order
        price_changed = instrument.make_price(price) != order.price
        size_changed = instrument.make_qty(size) != order.quantity

        if not price_changed and not size_changed:
            return

        # Log the changes
        if price_changed:
            price_diff_pct = (price - order.price.as_double()) / order.price.as_double() * 100
            self.log.info(f"Price change for order {order.client_order_id}: {order.price.as_double()} -> {price} ({price_diff_pct:.2f}%)")

        if size_changed:
            size_diff = size - order.quantity.as_double()
            self.log.info(f"Size change for order {order.client_order_id}: {order.quantity.as_double()} -> {size} (diff: {size_diff})")

        # Modify the order
        try:
            self.strategy.modify_order(
                order=order,
                price=instrument.make_price(price) if price_changed else None,
                quantity=instrument.make_qty(size) if size_changed else None,
            )
        except Exception as e:
            self.log.error(f"Error modifying order {order.client_order_id}: {e}")

    def create_new_order(
        self,
        order_dict: Dict[str, Dict[OrderSide, Order]],
        instrument: Instrument,
        side: OrderSide,
        price: float,
        size: float,
        is_maker: bool = True,
        time_in_force: TimeInForce = TimeInForce.GTC,
    ) -> Order:
        """
        Create a new order.

        Parameters
        ----------
        order_dict : Dict[str, Dict[OrderSide, Order]]
            The order dictionary to store the order in
        instrument : Instrument
            The trading instrument
        side : OrderSide
            The order side (BUY or SELL)
        price : float
            The order price
        size : float
            The order size
        is_maker : bool
            Whether the order is a maker order
        time_in_force : TimeInForce
            Time in force for the order

        Returns
        -------
        Order
            The created order
        """
        # Initialize the instrument entry in the dictionary if it doesn't exist
        if instrument.id.value not in order_dict:
            order_dict[instrument.id.value] = {}

        # Place the order
        if is_maker:
            order = self._place_limit_order(
                instrument=instrument,
                side=side,
                price=price,
                size=size,
                post_only=True,
                time_in_force=time_in_force,
            )
        else:
            order = self._place_market_order(
                instrument=instrument,
                side=side,
                size=size,
            )

        # Store the order in the dictionary
        if order:
            order_dict[instrument.id.value][side] = order
            self.log.info(f"Created new {'maker' if is_maker else 'taker'} order {order.client_order_id} for {instrument.id.value} {side} at {price}")

        return order

    def handle_position_management(
        self,
        instrument: Instrument,
        position: Position,
        current_price: float,
        best_ask_price: Optional[float] = None,
        best_bid_price: Optional[float] = None,
        exit_with_maker: bool = False,
        n_tickers: int = 1,
    ) -> None:
        """
        Handle position management including stop-loss and trailing stop.

        This method implements a sophisticated trailing stop mechanism with multiple
        thresholds based on profit levels. It's inspired by the chua_bn.py implementation.

        Parameters
        ----------
        instrument : Instrument
            The trading instrument
        position : Position
            The position to manage
        current_price : float
            Current market price
        best_ask_price : Optional[float]
            Best ask price from the order book
        best_bid_price : Optional[float]
            Best bid price from the order book
        exit_with_maker : bool
            Whether to exit with maker orders (limit) or taker orders (market)
        n_tickers : int
            Number of ticks to add/subtract from best bid/ask for maker exit orders
        """
        # Update highest profit tracking
        self.update_highest_profit_of_position(position, current_price)

        # Get current profit rate
        current_profit = self.current_pnl_rate(position, current_price)
        position_id = str(position.id)

        # Get highest profit achieved
        highest_profit = self.highest_profit_of_position_dict.get(position_id, 0.0)

        # Check if stop-loss is triggered (fixed stop loss)
        if current_profit < -self.stop_loss_pct:
            self.log.info(f"Stop-loss triggered for position {position_id}: {current_profit:.4%} < -{self.stop_loss_pct:.4%}")
            self._exit_position(
                instrument=instrument,
                position=position,
                current_price=current_price,
                best_ask_price=best_ask_price,
                best_bid_price=best_bid_price,
                exit_with_maker=False,  # Use market orders for stop loss
                n_tickers=n_tickers,
                reason="stop-loss"
            )
            return

        # Determine current trailing stop tier based on highest profit
        # This approach first computes the tier, then applies the appropriate trailing stop
        trail_tier = 0
        trail_pct = 0.0
        trail_name = ""

        if highest_profit > self.second_trail_profit_threshold:
            # Tier 3: Higher trailing stop (20% retracement) when profit > 3%
            trail_tier = 3
            trail_pct = self.higher_trail_stop_loss_pct
            trail_name = "higher"
        elif highest_profit > self.first_trail_profit_threshold:
            # Tier 2: Standard trailing stop (30% retracement) when profit > 2%
            trail_tier = 2
            trail_pct = self.trail_stop_loss_pct
            trail_name = "standard"
        elif highest_profit > self.low_trail_profit_threshold:
            # Tier 1: Low trailing stop (50% retracement) when profit > 1%
            trail_tier = 1
            trail_pct = self.low_trail_stop_loss_pct
            trail_name = "low"

        # If we have an active trailing stop tier, check if it's triggered
        if trail_tier > 0:
            # Calculate trailing stop threshold based on the tier
            trail_threshold = highest_profit * (1.0 - trail_pct)

            # Check if current profit has fallen below the trailing stop threshold
            if current_profit < trail_threshold:
                self.log.info(
                    f"{trail_name.capitalize()} trailing stop (Tier {trail_tier}) triggered for position {position_id}: "
                    f"current profit {current_profit:.4%} < threshold {trail_threshold:.4%} "
                    f"(highest: {highest_profit:.4%}, retracement: {trail_pct:.2%})"
                )
                self._exit_position(
                    instrument=instrument,
                    position=position,
                    current_price=current_price,
                    best_ask_price=best_ask_price,
                    best_bid_price=best_bid_price,
                    exit_with_maker=exit_with_maker,
                    n_tickers=n_tickers,
                    reason=f"{trail_name}-trailing-stop"
                )
                return
            else:
                # Log active trailing stop tier for debugging/monitoring
                self.log.debug(
                    f"Active {trail_name} trailing stop (Tier {trail_tier}) for position {position_id}: "
                    f"current profit {current_profit:.4%}, threshold {trail_threshold:.4%}, "
                    f"highest profit {highest_profit:.4%}"
                )

    def _exit_position(
        self,
        instrument: Instrument,
        position: Position,
        current_price: float,
        best_ask_price: Optional[float],
        best_bid_price: Optional[float],
        exit_with_maker: bool,
        n_tickers: int,
        reason: str,
    ) -> None:
        """
        Exit a position with appropriate order type.

        Parameters
        ----------
        instrument : Instrument
            The trading instrument
        position : Position
            The position to exit
        current_price : float
            Current market price
        best_ask_price : Optional[float]
            Best ask price from the order book
        best_bid_price : Optional[float]
            Best bid price from the order book
        exit_with_maker : bool
            Whether to exit with maker orders (limit) or taker orders (market)
        n_tickers : int
            Number of ticks to add/subtract from best bid/ask for maker exit orders
        reason : str
            Reason for exiting the position
        """
        # Determine exit side (opposite of position side)
        exit_side = OrderSide.SELL if position.entry == OrderSide.BUY else OrderSide.BUY

        # Calculate exit price for maker orders
        exit_price = current_price
        if exit_with_maker:
            if position.entry == OrderSide.BUY and best_bid_price is not None:
                # For long positions, sell at bid + n ticks
                exit_price = best_bid_price + n_tickers * instrument.price_increment.as_double()
            elif position.entry == OrderSide.SELL and best_ask_price is not None:
                # For short positions, buy at ask - n ticks
                exit_price = best_ask_price - n_tickers * instrument.price_increment.as_double()

        # Log the exit
        self.log.info(
            f"Exiting position {position.id} ({position.entry} {float(position.quantity)}) "
            f"at {exit_price} with {'maker' if exit_with_maker else 'taker'} order. "
            f"Reason: {reason}, Current profit: {self.current_pnl_rate(position, current_price):.4%}"
        )

        # Place the exit order
        self.process_signal(
            order_dict=self.exit_signals,
            instrument=instrument,
            side=exit_side,
            price=exit_price,
            size=float(position.quantity),
            is_maker=exit_with_maker,
            time_in_force=TimeInForce.GTC,
            force_update=True,  # Force update for exit orders
        )