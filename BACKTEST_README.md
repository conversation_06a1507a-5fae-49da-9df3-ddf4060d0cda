# 新闻预测策略回测系统

本目录包含了基于Nautilus Trader框架的新闻预测策略回测脚本，专门为`news_prediction_strategy.py`策略设计。

## 文件说明

### 1. `simple_backtest_news_strategy.py`
**简化版回测脚本**
- 适合快速测试策略框架
- 使用模拟数据
- 主要验证策略配置和基本功能
- 运行速度快，资源消耗少

### 2. `comprehensive_backtest_news_strategy.py`
**综合版回测脚本**
- 使用真实的K线数据
- 完整的数据加载和转换
- 支持自定义交易工具
- 提供详细的回测结果分析

### 3. `backtest_news_prediction_strategy.py`
**完整版回测脚本**
- 包含完整的数据管道
- 支持自定义新闻数据注入
- 高度可配置的回测环境

## 前置条件

### 必需文件
确保以下文件存在：

```
/root/news_train/process/models/
├── crypto_tokenizer_small_with_symbols.model  # SentencePiece分词器
├── real_tfidf.pkl                            # TF-IDF向量化器
└── real_rf_model.pkl                         # RandomForest模型
```

### 数据文件
K线数据文件（可选，用于综合回测）：
```
/root/news_train/data/
├── 1000PEPEUSDT_5m.parquet  # PEPE 5分钟K线数据
└── BTCUSDT_5m.parquet       # BTC 5分钟K线数据
```

### 依赖包
```bash
pip install nautilus_trader pandas pyarrow sentencepiece scikit-learn
```

## 使用方法

### 快速开始（推荐）
```bash
cd /root/news_train
python simple_backtest_news_strategy.py
```

### 综合回测（需要数据文件）
```bash
cd /root/news_train
python comprehensive_backtest_news_strategy.py
```

### 完整回测
```bash
cd /root/news_train
python backtest_news_prediction_strategy.py
```

## 策略配置

### 核心参数
- **测试标的**: 1000PEPEUSDT（可修改）
- **对冲标的**: BTCUSDT
- **时间框架**: 5分钟K线
- **起始资金**: 100,000 USDT

### 风险管理参数
```python
{
    "stop_loss_pct": 0.02,                    # 固定止损 2%
    "low_trail_stop_loss_pct": 0.5,           # 低级移动止损 50%
    "trail_stop_loss_pct": 0.3,               # 标准移动止损 30%
    "higher_trail_stop_loss_pct": 0.2,        # 高级移动止损 20%
    "low_trail_profit_threshold": 0.01,       # 低级移动止损触发阈值 1%
    "first_trail_profit_threshold": 0.02,     # 第一级移动止损触发阈值 2%
    "second_trail_profit_threshold": 0.03,    # 第二级移动止损触发阈值 3%
    "max_trade_usd": 1000.0,                  # 最大交易金额
    "min_trade_usd": 100.0                    # 最小交易金额
}
```

## 策略逻辑

### 交易信号生成
1. **新闻处理**: 使用CryptoNewsTextProcessor处理新闻文本
2. **特征提取**: SentencePiece分词 + TF-IDF向量化
3. **技术指标**: 结合RSI、MFI、NATR等技术指标
4. **模型预测**: RandomForest分类器预测价格方向

### 交易执行
- **看涨信号**: 做多目标币种，做空BTC
- **看跌信号**: 做空目标币种，做多BTC
- **信号强度**: 根据预测置信度调整交易金额

### 风险控制
- **固定止损**: 2%亏损时强制平仓
- **移动止盈**: 三级移动止盈机制
- **资金管理**: 基于信号强度的动态仓位管理

## 回测结果分析

### 输出指标
- **账户余额**: 最终账户总余额
- **总PnL**: 未实现盈亏
- **订单统计**: 总订单数量
- **仓位统计**: 总仓位数量
- **策略统计**: 策略执行情况

### 性能评估
回测完成后，可以通过以下方式评估策略性能：

1. **收益率**: (最终余额 - 初始余额) / 初始余额
2. **最大回撤**: 从峰值到谷值的最大跌幅
3. **夏普比率**: 风险调整后收益
4. **胜率**: 盈利交易占总交易的比例

## 自定义配置

### 修改测试标的
```python
# 在脚本中修改symbol参数
runner = NewsBacktestRunner(symbol="1000SHIBUSDT")  # 改为SHIB
```

### 调整回测时间范围
```python
run_config = BacktestRunConfig(
    # ...
    start="2024-01-01T00:00:00Z",  # 开始时间
    end="2024-12-31T23:59:59Z",   # 结束时间
)
```

### 修改风险参数
```python
strategy_config = {
    # ...
    "stop_loss_pct": 0.03,        # 调整止损比例
    "max_trade_usd": 2000.0,      # 调整最大交易金额
}
```

## 故障排除

### 常见问题

1. **模型文件不存在**
   ```
   ❌ 缺少必要文件: /root/news_train/process/models/real_rf_model.pkl
   ```
   **解决方案**: 先运行模型训练脚本生成必要的模型文件

2. **数据文件格式错误**
   ```
   ❌ 缺少必要列: ['timestamp', 'open', 'high', 'low', 'close', 'volume']
   ```
   **解决方案**: 检查数据文件格式，确保包含必要的OHLCV列

3. **内存不足**
   ```
   MemoryError: Unable to allocate array
   ```
   **解决方案**: 减少数据量或使用简化版回测脚本

### 调试模式
启用详细日志：
```python
logging.basicConfig(level=logging.DEBUG)
```

## 扩展功能

### 添加自定义指标
可以在策略中添加更多技术指标：
```python
# 在策略的on_start方法中
self.custom_indicator = CustomIndicator(period=20)
self.register_indicator_for_bars(self.bar_type, self.custom_indicator)
```

### 集成实时新闻
可以扩展脚本以支持实时新闻数据：
```python
# 添加新闻数据源
news_config = NewsDataConfig(
    source="real_news_api",
    symbols=[self.symbol]
)
```

## 注意事项

1. **回测结果仅供参考**: 历史表现不代表未来收益
2. **滑点和手续费**: 实盘交易中需要考虑额外成本
3. **数据质量**: 确保使用高质量的历史数据
4. **过拟合风险**: 避免过度优化参数

## 联系支持

如有问题或建议，请查看：
- 策略源码: `process/strategy/news_prediction_strategy.py`
- 数据类型定义: `news_data_type.py`
- 项目文档: `PROJECT_STATUS_SUMMARY.md`
