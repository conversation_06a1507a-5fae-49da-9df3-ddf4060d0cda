#!/usr/bin/env python3
"""
新闻预测策略回测脚本

基于nautilus_trader框架的新闻预测策略回测，使用1000PEPEUSDT作为测试标的
"""

import asyncio
import logging
from pathlib import Path
from datetime import datetime, timezone
import pandas as pd

# Nautilus Trader imports
from nautilus_trader.config import BacktestVenueConfig, BacktestDataConfig, BacktestEngineConfig, BacktestRunConfig
from nautilus_trader.config import ImportableStrategyConfig, LoggingConfig
from nautilus_trader.backtest.node import BacktestNode
from nautilus_trader.model.identifiers import InstrumentId, Venue
from nautilus_trader.model.currencies import USD, USDT
from nautilus_trader.model.enums import AccountType, OmsType
from nautilus_trader.model.objects import Money
from nautilus_trader.persistence.catalog import ParquetDataCatalog
from nautilus_trader.test_kit.providers import TestInstrumentProvider

# 导入策略和数据类型
from process.strategy.news_prediction_strategy import NewsPnlTradingStrategy, NewsPnlTradingStrategyConfig
from news_data_type import NewsData

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class NewsBacktestRunner:
    """新闻预测策略回测运行器"""
    
    def __init__(self, symbol: str = "1000PEPEUSDT"):
        self.symbol = symbol
        self.btc_symbol = "BTCUSDT"
        self.venue = "BINANCE"
        
        # 路径配置
        self.base_path = Path("/root/news_train")
        self.catalog_path = self.base_path / "catalog"
        self.data_path = self.base_path / "data"
        self.models_path = self.base_path / "process" / "models"
        
        # 模型文件路径
        self.tokenizer_path = str(self.models_path / "crypto_tokenizer_small_with_symbols.model")
        self.tfidf_path = str(self.models_path / "real_tfidf.pkl")
        self.model_path = str(self.models_path / "real_rf_model.pkl")
        
        # 创建catalog目录
        self.catalog_path.mkdir(exist_ok=True)
        
    def create_sample_news_data(self) -> NewsData:
        """创建示例新闻数据用于测试"""
        from nautilus_trader.core.datetime import dt_to_unix_nanos
        
        # 创建示例新闻
        ts_event = dt_to_unix_nanos(datetime(2024, 1, 15, 10, 0, 0, tzinfo=timezone.utc))
        ts_init = ts_event
        
        news = NewsData(
            instrument_id=InstrumentId.from_str(f"{self.symbol}.{self.venue}"),
            title="PEPE价格突破关键阻力位，市场情绪乐观",
            content="PEPE代币今日突破重要技术阻力位，交易量大幅增加，市场分析师认为这可能是新一轮上涨的开始。技术指标显示强劲的买入信号。",
            time="10:00:00",
            date="2024-01-15",
            url="https://example.com/news/pepe-breakout",
            is_featured=True,
            scraped_at="2024-01-15T10:00:00Z",
            ts_event=ts_event,
            ts_init=ts_init
        )
        
        return news
        
    def setup_instruments_and_data(self):
        """设置交易工具和数据"""
        # 创建catalog
        catalog = ParquetDataCatalog(str(self.catalog_path))
        
        # 创建测试工具
        pepe_instrument = TestInstrumentProvider.crypto_perpetual(
            symbol=self.symbol,
            venue=Venue(self.venue),
            price_precision=8,
            size_precision=0
        )
        
        btc_instrument = TestInstrumentProvider.crypto_perpetual(
            symbol=self.btc_symbol,
            venue=Venue(self.venue),
            price_precision=2,
            size_precision=6
        )
        
        # 保存工具定义到catalog
        try:
            catalog.write_data([pepe_instrument, btc_instrument])
            print(f"✅ 已保存交易工具到catalog: {pepe_instrument.id}, {btc_instrument.id}")
        except Exception as e:
            print(f"⚠️ 保存交易工具时出错: {e}")
            
        return catalog
        
    def load_bar_data(self, catalog):
        """加载K线数据到catalog"""
        try:
            from nautilus_trader.model.data import Bar, BarType
            from nautilus_trader.core.datetime import dt_to_unix_nanos
            from nautilus_trader.model.enums import BarAggregation, PriceType
            import numpy as np

            # 加载PEPE数据
            pepe_file = self.data_path / f"{self.symbol}_5m.parquet"
            if pepe_file.exists():
                print(f"📊 加载PEPE数据: {pepe_file}")
                df = pd.read_parquet(pepe_file)

                # 创建简单的测试数据（如果实际数据格式不匹配）
                if len(df) > 0:
                    print(f"📈 PEPE数据行数: {len(df)}")
                    # 这里可以添加实际的Bar对象创建逻辑

            # 加载BTC数据
            btc_file = self.data_path / f"{self.btc_symbol}_5m.parquet"
            if btc_file.exists():
                print(f"📊 加载BTC数据: {btc_file}")
                df = pd.read_parquet(btc_file)
                if len(df) > 0:
                    print(f"📈 BTC数据行数: {len(df)}")

        except Exception as e:
            print(f"⚠️ 加载K线数据时出错: {e}")
            print("💡 将使用模拟数据进行回测")
            
    def create_strategy_config(self, start_news: NewsData) -> ImportableStrategyConfig:
        """创建策略配置"""
        
        strategy_config = {
            # 模型路径
            "tokenizer_path": self.tokenizer_path,
            "tfidf_path": self.tfidf_path,
            "model_path": self.model_path,
            
            # 交易工具
            "instrument_str": f"{self.symbol}.{self.venue}",
            "btc_instrument_str": f"{self.btc_symbol}.{self.venue}",
            "bar_str": f"{self.symbol}.{self.venue}-5-MINUTE-LAST-EXTERNAL",
            "btc_bar_str": f"{self.btc_symbol}.{self.venue}-5-MINUTE-LAST-EXTERNAL",
            
            # 风险管理参数
            "stop_loss_pct": 0.02,
            "low_trail_stop_loss_pct": 0.5,
            "trail_stop_loss_pct": 0.3,
            "higher_trail_stop_loss_pct": 0.2,
            "low_trail_profit_threshold": 0.01,
            "first_trail_profit_threshold": 0.02,
            "second_trail_profit_threshold": 0.03,
            
            # 交易金额
            "max_trade_usd": 1000.0,
            "min_trade_usd": 100.0,
            
            # 策略ID
            "strategy_id": "NewsPnlStrategy-001"
        }
        
        return ImportableStrategyConfig(
            strategy_path="process.strategy.news_prediction_strategy:NewsPnlTradingStrategy",
            config_path="process.strategy.news_prediction_strategy:NewsPnlTradingStrategyConfig",
            config=strategy_config
        )
        
    def create_backtest_config(self, start_news: NewsData) -> BacktestRunConfig:
        """创建回测配置"""
        
        # 场所配置
        venue_config = BacktestVenueConfig(
            name=self.venue,
            oms_type=OmsType.HEDGING,
            account_type=AccountType.MARGIN,
            base_currency=USDT,
            starting_balances=[Money(100000, USDT)],  # 10万USDT起始资金
        )
        
        # 数据配置 - 使用catalog中的数据
        data_config = BacktestDataConfig(
            catalog_path=str(self.catalog_path),
            data_cls="news_data_type:NewsData",
            client_id="NewsDataClient",
        )
        
        # 策略配置
        strategy_config = self.create_strategy_config(start_news)
        
        # 引擎配置
        engine_config = BacktestEngineConfig(
            strategies=[strategy_config],
            logging=LoggingConfig(
                log_level="INFO",
                bypass_logging=False,
            ),
        )
        
        # 回测运行配置
        run_config = BacktestRunConfig(
            engine=engine_config,
            venues=[venue_config],
            data=[data_config],
            start="2024-01-01T00:00:00Z",
            end="2024-01-31T23:59:59Z",
        )
        
        return run_config
        
    async def run_backtest(self):
        """运行回测"""
        print("🚀 开始新闻预测策略回测...")
        print(f"📈 测试标的: {self.symbol}")
        print(f"🏦 交易所: {self.venue}")
        print(f"📁 Catalog路径: {self.catalog_path}")
        
        # 检查必要文件
        required_files = [
            self.tokenizer_path,
            self.tfidf_path,
            self.model_path
        ]
        
        for file_path in required_files:
            if not Path(file_path).exists():
                print(f"❌ 缺少必要文件: {file_path}")
                return False
                
        print("✅ 所有必要文件检查通过")
        
        # 设置数据和工具
        catalog = self.setup_instruments_and_data()
        self.load_bar_data(catalog)
        
        # 创建示例新闻数据
        start_news = self.create_sample_news_data()
        
        # 创建回测配置
        config = self.create_backtest_config(start_news)
        
        # 创建回测节点
        node = BacktestNode(configs=[config])
        
        try:
            # 运行回测
            print("⏳ 正在运行回测...")
            await node.run_async()
            
            print("✅ 回测完成!")
            
            # 获取回测结果
            engine = node.get_engine()
            if engine:
                print("\n📊 回测结果:")
                print(f"账户余额: {engine.portfolio.account().balance_total()}")
                print(f"总PnL: {engine.portfolio.account().unrealized_pnl()}")
                
        except Exception as e:
            print(f"❌ 回测失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            await node.dispose()
            
        return True


async def main():
    """主函数"""
    print("=" * 60)
    print("🎯 新闻预测策略回测系统")
    print("=" * 60)
    
    # 创建回测运行器
    runner = NewsBacktestRunner(symbol="1000PEPEUSDT")
    
    # 运行回测
    success = await runner.run_backtest()
    
    if success:
        print("\n🎉 回测成功完成!")
    else:
        print("\n💥 回测失败!")
        
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
