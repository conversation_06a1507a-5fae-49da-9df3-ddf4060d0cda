"""
High-Frequency Trading strategy using LightGBM models with enhanced position management.

This strategy uses multiple LightGBM models to predict market movements and
executes trades based on these predictions with improved stop-loss management.
"""
import os
import pickle
from collections import deque
from datetime import datetime, timezone, timedelta
from typing import Optional, List, Tuple

import numpy as np
import lightgbm as lgb

from nautilus_trader.core.data import Data
from nautilus_trader.core.message import Event
from nautilus_trader.model.data import DataType, TradeTick
from nautilus_trader.model.enums import OrderSide, AggressorSide, TimeInForce
from nautilus_trader.model.identifiers import InstrumentId, ClientId
from nautilus_trader.model.position import Position

from nautilus_trader.model.data import OrderBookDeltas
from nautilus_trader.model.book import OrderBook
from hft_toolbox.orderbook_indicators import OrderbookIndicators
from hft_toolbox.high_pass_3_filter import NumbaHighPassFilter
from hft_toolbox.position_executor_refactor import  EnhancedOrderManager
from hft_toolbox.streaming_agg_improve import (
    Aggregator,
    WindowType,
    WindowConfig,
    MultiWindowAggregator,
)
from hft_toolbox.trade_indicators import TradeIndicators
from nautilus_trader.config import StrategyConfig
from nautilus_trader.trading.strategy import Strategy
from nautilus_trader.model.events.position import PositionOpened, PositionClosed,PositionChanged

class High(Aggregator):
    """Calculates the highest price in a list of trade ticks."""
    def __init__(self):
        self.name = "high"

    def __call__(self, data: List[TradeTick]) -> float:
        return np.max([tick.price.as_double() for tick in data])


class Low(Aggregator):
    """Calculates the lowest price in a list of trade ticks."""
    def __init__(self):
        self.name = "low"

    def __call__(self, data: List[TradeTick]) -> float:
        return np.min([tick.price.as_double() for tick in data])


class Close(Aggregator):
    """Returns the closing price (last trade tick price)."""
    def __init__(self):
        self.name = "close"

    def __call__(self, data: List[TradeTick]) -> float:
        return data[-1].price.as_double()


class Volume(Aggregator):
    """Calculates the total volume from a list of trade ticks."""
    def __init__(self):
        self.name = "volume"

    def __call__(self, data: List[TradeTick]) -> float:
        return np.sum([tick.size.as_double() for tick in data])


class OFI(Aggregator):
    """Calculates Order Flow Imbalance from a list of trade ticks."""
    def __init__(self):
        self.name = "ofi"

    def __call__(self, data: List[TradeTick]) -> float:
        return np.sum([
            tick.size.as_double() if tick.aggressor_side == AggressorSide.BUYER
            else -tick.size.as_double() for tick in data
        ])

TRADE_BAR_CONFIG = {
    "1s": WindowConfig(
        interval=timedelta(seconds=1),
        window_type=WindowType.TUMBLING,
        align_to_interval=True,
        closed="left",
        aggregators=[
            High(),
            Low(),
            Close(),
            Volume(),
            OFI(),
        ]
    )
}

def load_lightgbm_models(
    model_prefix: str,
    num_models: int = 5,
    directory: str = "models",
) -> List[lgb.Booster]:
    """
    Load LightGBM models from the specified directory.

    Parameters
    ----------
    model_prefix : str
        Type of data the models were trained on.
    num_models : int, default 5
        Number of models to load.
    directory : str, default "models"
        Directory containing the model files.

    Returns
    -------
    List[lgb.Booster]
        List of loaded LightGBM models.
    """
    models = []
    for i in range(num_models):
        model_path = os.path.join(directory, f"{model_prefix}_lightgbm_model_fold_{i}.txt")
        model = lgb.Booster(model_file=model_path)
        models.append(model)
    return models


def get_bet_size(
    prediction_class: int,
    confidence: float,
    max_trade_size: float,
    num_classes: int = 7
) -> float:
    """
    Calculate position size based on multi-class prediction and confidence.

    Parameters
    ----------
    prediction_class : int
        The predicted class (0 to num_classes-1).
    confidence : float
        Confidence score of the prediction (0-1).
    max_trade_size : float
        Maximum position size.
    num_classes : int, default 7
        Number of prediction classes.

    Returns
    -------
    float
        Position size (positive for long, negative for short).
    """
    return confidence * (prediction_class - num_classes // 2) / num_classes * max_trade_size


def detect_significant_order_book_walls(
    instrument_id_value: str,
    bid_prices: np.ndarray,
    bid_sizes: np.ndarray,
    ask_prices: np.ndarray,
    ask_sizes: np.ndarray,
    current_price: float,
    volume_dynamic_threshold: float,
    levels: int = 20,
    base_threshold_factor: float = 5.0,
    price_range_threshold: float = 0.1,
) -> Tuple[List[Tuple[float, float]], List[Tuple[float, float]]]:
    """
    Detects significant walls in the order book based on a volume threshold within a specified price range.

    Args:
        instrument_id_value (str): The identifier of the instrument.
        bid_prices (np.ndarray): Array of bid prices.
        bid_sizes (np.ndarray): Array of bid sizes.
        ask_prices (np.ndarray): Array of ask prices.
        ask_sizes (np.ndarray): Array of ask sizes.
        current_price (float): The current market price.
        volume_dynamic_threshold (float): The dynamic volume threshold based on market volatility.
        levels (int): The number of top levels to consider for average size calculation (default 20).
        base_threshold_factor (float): The base multiplier for the volume threshold to determine a wall (default 5.0).
        price_range_threshold (float): The percentage of the current price to determine proximity (default 0.1).

    Returns:
        Tuple[List[Tuple[float, float]], List[Tuple[float, float]]]: A tuple containing lists of detected significant bid walls and ask walls.
    """
    # Ensure there are enough orders to perform analysis
    if len(bid_prices) < levels or len(ask_prices) < levels:
        return [], []

    # Calculate average sizes for the top levels
    avg_bid_size = np.mean(bid_sizes[:levels])
    avg_ask_size = np.mean(ask_sizes[:levels])

    # Calculate proximity threshold as a percentage of the current price
    proximity_threshold = price_range_threshold * current_price

    # Function to check wall significance
    def is_wall_significant(price, size, threshold, avg_size):
        return size > avg_size * base_threshold_factor and price*size > threshold and abs(price - current_price) <= proximity_threshold

    # Detect significant bid and ask walls
    significant_bid_walls = []
    for price, size in zip(bid_prices, bid_sizes):
        if is_wall_significant(price, size, volume_dynamic_threshold, avg_bid_size):
            significant_bid_walls.append((price, size))
            
    significant_ask_walls = []
    for price, size in zip(ask_prices, ask_sizes):
        if is_wall_significant(price, size, volume_dynamic_threshold, avg_ask_size):
            significant_ask_walls.append((price, size))

    return significant_bid_walls, significant_ask_walls


class HftLightGBMConfig(StrategyConfig, kw_only=True):
    """
    Configuration for HftLightGBM strategy instances.

    Extends StrategyConfig with parameters specific to the
    LightGBM-based trading strategy.
    """
    # Basic parameters
    instrument_id: str

    # LightGBM model parameters
    model_prefix: str
    model_save_dir: str
    num_models: int = 5
    num_classes: int = 7

    # Feature configuration
    orderbook_features_path: str
    trade_features_path: str

    # Trading parameters
    max_trade_usd: float
    min_notional: float
    entry_threshold: float = 0.7
    exit_threshold: float = 0.6

    #order manager paramters 
    min_update_interval_seconds: int = 10
    stop_loss_pct: float = 0.02
    low_trail_stop_loss_pct: float = 0.5
    trail_stop_loss_pct: float = 0.3
    higher_trail_stop_loss_pct: float = 0.2
    low_trail_profit_threshold: float = 0.01
    first_trail_profit_threshold: float = 0.02
    second_trail_profit_threshold: float = 0.03
    take_fee: float = 0.0004 # 0.04% trading fee

    # Data collection parameters
    book_depth: int = 20
    seconds_interval: int = 1
    max_orderbook_sequence_length: int = 100
    save_features: bool = False
    features_save_dir: Optional[str] = None

    # Model evaluation
    prediction_shift: int = 10

class HftLightGBMStrategy(Strategy):
    """
    High-Frequency Trading strategy using LightGBM models with enhanced position management.

    This strategy uses multiple LightGBM models to predict market movements and
    executes trades based on these predictions. It implements dynamic model weighting
    based on prediction accuracy and uses the refactored position executor for
    improved stop-loss management.

    Parameters
    ----------
    config : HftLightGBMConfig
        The configuration for the strategy instance.
    """

    def __init__(self, config: HftLightGBMConfig) -> None:
        """
        Initialize the strategy with configuration parameters.

        Sets up model loading, feature processing, and trading parameters.
        """
        super().__init__(config)
        # Set instrument ID
        self.instrument_id = InstrumentId.from_str(config.instrument_id)

        self.num_models = config.num_models
        self.model_prefix = config.model_prefix
        self.orderbook_features_path = config.orderbook_features_path
        self.trade_features_path = config.trade_features_path
        # Trading parameters
        self.max_trade_usd = config.max_trade_usd
        self.min_notional = config.min_notional
        self.entry_threshold = config.entry_threshold
        self.exit_threshold = config.exit_threshold
        self.num_classes = config.num_classes

        # Initialize EnhancedOrderManager
        self.order_manager = EnhancedOrderManager(
            strategy=self,
            min_update_interval_seconds=config.min_update_interval_seconds,
            stop_loss_pct=config.stop_loss_pct,
            low_trail_stop_loss_pct=config.low_trail_stop_loss_pct,  # 50% retracement for low trailing stop
            trail_stop_loss_pct=config.trail_stop_loss_pct,  # 30% retracement for standard trailing stop
            higher_trail_stop_loss_pct=config.higher_trail_stop_loss_pct,  # 20% retracement for higher trailing stop
            low_trail_profit_threshold=config.low_trail_profit_threshold,  # 1% profit threshold for low trailing stop
            first_trail_profit_threshold=config.first_trail_profit_threshold,  # 2% profit threshold for first trailing stop
            second_trail_profit_threshold=config.second_trail_profit_threshold,  # 3% profit threshold for second trailing stop
            take_fee=config.take_fee,  # 0.04% trading fee
        )

        # Data collection parameters
        self.book_depth = config.book_depth
        self.seconds_interval = config.seconds_interval
        self.max_orderbook_sequence_length = config.max_orderbook_sequence_length
        self.save_features = config.save_features
        self.model_save_dir = config.model_save_dir
        self.features_save_dir = config.features_save_dir if config.features_save_dir else config.model_save_dir
        self.trade_aggregator_period = "1s"  # Default period for trade aggregation

        # Position tracking
        self._book = None 
        self.side = 0  # 0 for no position, 1 for long, -1 for short
        self.entry_time = None
        self.entry_price = None
        self.latest_trade = None
        self.latest_orderbook_features = None
        self.latest_trade_features = None

        # Model prediction and evaluation
        self.prediction_shift = config.prediction_shift
        self.history_predicted = deque(maxlen=300)
        self.history_returns = deque(maxlen=300)
        self.model_weights = [1.0/config.num_models] * config.num_models  # Equal weights initially

        # Initialize components
        self.orderbook_indicators = None
        self.models = None
        #Init high pass filter
        self.filter = NumbaHighPassFilter(10)
        if self.save_features:
            self.log.info("Feature saving enabled")

    def _load_models_and_features(self) -> None:
        """
        Load LightGBM models and feature configurations.

        Initializes all data structures needed for model prediction and feature calculation.
        """
        try:
            # Load models
            self.log.info(f"Loading {self.num_models} LightGBM models from {self.model_save_dir}")
            self.models = load_lightgbm_models(
                model_prefix=self.model_prefix,
                num_models=self.num_models,
                directory=self.model_save_dir,
            )

            # Load feature configurations
            with open(self.orderbook_features_path, "rb") as f:
                self.orderbook_features_order = pickle.load(f)

            with open(self.trade_features_path, "rb") as f:
                self.trade_features_order = pickle.load(f)

            self.log.info(f"Successfully loaded {len(self.models)} LightGBM models")
            self.log.info(f"Orderbook features: {self.orderbook_features_order}")
            self.log.info(f"Trade features: {self.trade_features_order}")

            # Initialize orderbook data structures
            self.orderbook_arrays = {
                'ask_prices': deque(maxlen=self.max_orderbook_sequence_length),
                'bid_prices': deque(maxlen=self.max_orderbook_sequence_length),
                'bid_sizes': deque(maxlen=self.max_orderbook_sequence_length),
                'ask_sizes': deque(maxlen=self.max_orderbook_sequence_length),
                'top_vwap_prices': deque(maxlen=self.max_orderbook_sequence_length),
                'timestamps': deque(maxlen=self.max_orderbook_sequence_length),
            }

            # Initialize orderbook indicators
            self.orderbook_indicators = OrderbookIndicators(
                n_level=self.book_depth,
                orderbook_windows_list=[60,300,900,3600],
                indicators_to_compute=self.orderbook_features_order,
            )

            self.log.info("Orderbook indicators initialized")

            # Initialize trade aggregator
            self.trade_aggregator = MultiWindowAggregator(
                configs=TRADE_BAR_CONFIG,
                clock=self.clock,
                auto_compute=True,
            )
            self.log.info("Trade aggregator initialized")

            # Initialize trade data structures
            self.trade_arrays = {
                "close": deque(maxlen=self.max_orderbook_sequence_length),
                "volume": deque(maxlen=self.max_orderbook_sequence_length),
                "ofi": deque(maxlen=self.max_orderbook_sequence_length),
                "timestamp": deque(maxlen=self.max_orderbook_sequence_length),
            }

            # Initialize trade indicators
            self.trade_indicators = TradeIndicators(
                trade_windows_list=[60,300,900,3600],
                indicators_to_compute=self.trade_features_order
            )

        except Exception as e:
            self.log.error(f"Failed to load models and features: {e}")
            self.stop()

    def on_start(self) -> None:
        """
        Initialize strategy components on start.

        Sets up instrument data and subscribes to required data streams.
        """
        self.log.info(f"Starting strategy for {self.instrument_id}")

        # Call parent on_start to initialize price_increment
        super().on_start()
        self._load_models_and_features()
        # Get instrument
        self.instrument = self.cache.instrument(self.instrument_id)
        if self.instrument is None:
            self.log.error(f"Could not find instrument for {self.instrument_id}")
            self.stop()
            return

        # Create orderbook
        self._book = OrderBook(
            instrument_id=self.instrument_id,
            book_type=BookType.L2_MBP,
        )

        # Subscribe to live data 
        self.log.info(f"Subscribing to {self.instrument_id} orderbook data")
        self.subscribe_order_book_deltas(self.instrument_id, depth=self.book_depth)

        align_start_time_ns = self.clock.timestamp_ns() // (self.seconds_interval * 1e9) * self.seconds_interval * 1e9
        align_start_time_ns += 1e9 * self.seconds_interval
        align_start_time = datetime.fromtimestamp(align_start_time_ns / 1e9, tz=timezone.utc)
        self.log.info(f"Aligning start time to {align_start_time} UTC for {self.seconds_interval} seconds interval")
        # Set up a timer for periodic execution
        self.clock.set_timer(
            name="hft_multi_factor",
            start_time=align_start_time,
            interval=timedelta(seconds=self.seconds_interval),
            callback=self.on_time_event,
        )

        self.subscribe_trade_ticks(self.instrument_id)
        self.request_trade_ticks(self.instrument_id)

    def on_historical_data(self, data: Data) -> None:
        """
        Process historical data.

        Routes historical trade ticks to the trade tick handler.
        """
        if isinstance(data, TradeTick):
            self.on_trade_tick(data)

    def on_trade_tick(self, tick: TradeTick) -> None:
        """
        Process incoming trade ticks.

        Updates trade aggregator and calculates trade features when new bars are generated.
        """
        # Skip invalid ticks
        if (tick.price.as_double() < self.instrument.price_increment.as_double() or
            tick.size.as_double() < self.instrument.size_increment.as_double()):
            return

        self.latest_trade = tick
        # Update trade aggregator
        self.trade_aggregator.add(tick)
        # Process new bar if generated
        if self.trade_aggregator.window_managers[self.trade_aggregator_period].generate_new_data:
            self.latest_trade_features = {}  # Clear cache
            latest_bar = self.trade_aggregator.get_latest(
                self.trade_aggregator_period
            )
            if latest_bar == {}:
                return
            current_bar_ts = latest_bar["timestamp"]
            interval_ns = self.seconds_interval * 1e9  # Convert seconds to nanoseconds
            # check if we need to filled gap
            if len(self.trade_arrays["close"]) > 0:
                last_bar_ts = self.trade_arrays["timestamp"][-1]
                if current_bar_ts - last_bar_ts >  interval_ns:
                    num_missing_bars = int((current_bar_ts - last_bar_ts) // interval_ns) - 1
                    #current bar missing also
                    if "close" not in latest_bar:
                        num_missing_bars += 1
                    last_close = self.trade_arrays["close"][-1]
                    for i in range(num_missing_bars):
                        jump_ts = last_bar_ts + (i + 1) * interval_ns
                        self.trade_arrays["close"].append(last_close)
                        self.trade_arrays["volume"].append(0.0)
                        self.trade_arrays["ofi"].append(0.0)
                        self.trade_arrays["timestamp"].append(jump_ts)
                        self.latest_trade_features = self.trade_indicators.get_latest_indicators_for_aggregation_bars(
                            self.trade_arrays,
                            high = last_close,
                            low = last_close,
                            close = last_close,
                            volume = 0.0,
                        )
            #check if we should add the latest_bar
            if "close" in latest_bar and "volume" in latest_bar and "ofi" in latest_bar:
                self.trade_arrays["close"].append(latest_bar["close"])
                self.trade_arrays["volume"].append(latest_bar["volume"])
                self.trade_arrays["ofi"].append(latest_bar["ofi"])
                self.trade_arrays["timestamp"].append(current_bar_ts)
                self.latest_trade_features = self.trade_indicators.get_latest_indicators_for_aggregation_bars(
                    self.trade_arrays,
                    high = latest_bar["high"],
                    low = latest_bar["low"],
                    close = latest_bar["close"],
                    volume = latest_bar["volume"],
                )

    def on_order_book_deltas(self, deltas: OrderBookDeltas) -> None:
        """
        Update order book with received deltas.
        
        Parameters
        ----------
        deltas : OrderBookDeltas
            The order book deltas received.
        """
        if not self._book:
            self.log.error("No book being maintained.")
            return
        self._book.apply_deltas(deltas)

    def on_time_event(self, event: TimeEvent) -> None:
        """Optimized time event processing."""
        if not self._book:
            self.log.error("No order book maintained.")
            return False
        if len(self._book.bids()) <= self.book_depth or len(self._book.asks()) <= self.book_depth:
            self.log.error("book depth is not enough.")
            return False
        self._update_orderbook_features()
        
        # Execute trading logic when enough data is collected
        if len(self.orderbook_arrays['ask_prices']) >= self.max_orderbook_sequence_length:
            self._update_model_weights()
            self._execute_trades()


    def _update_orderbook_features(self) -> None:
        """
        Update orderbook data and compute features.

        Extracts data from the latest orderbook snapshot, calculates VWAP,
        and updates feature arrays.
        """
        current_timestamp = self.clock.timestamp_ns()

        # Extract price and size arrays from the orderbook snapshot
        self.bid_prices = np.array([level.price.as_double() for level in self._book.bids()])
        self.bid_sizes = np.array([level.size.as_double() for level in self._book.bids()])
        self.ask_prices = np.array([level.price.as_double() for level in self._book.asks()])
        self.ask_sizes = np.array([level.size.as_double() for level in self._book.asks()])

        # Update orderbook arrays
        self.orderbook_arrays['bid_prices'].append(self.bid_prices)
        self.orderbook_arrays['bid_sizes'].append(self.bid_sizes)
        self.orderbook_arrays['ask_prices'].append(self.ask_prices)
        self.orderbook_arrays['ask_sizes'].append(self.ask_sizes)
        self.orderbook_arrays['timestamps'].append(current_timestamp)

        # Extract best bid/ask prices and sizes
        self.best_bid_price = self.bid_prices[0]
        self.best_ask_price = self.ask_prices[0]
        self.best_bid_size = self.bid_sizes[0]
        self.best_ask_size = self.ask_sizes[0]

        # Calculate VWAP (Volume-Weighted Average Price)
        self.vwap = (self.best_bid_price * self.best_ask_size +
                     self.best_ask_price * self.best_bid_size) / (self.best_bid_size + self.best_ask_size)
        self.filter_top_vwap = self.filter.filter(self.vwap)
        self.orderbook_arrays['top_vwap_prices'].append(self.vwap)

        # Calculate orderbook features
        self.latest_orderbook_features = self.orderbook_indicators.get_latest_indicators(
            self.orderbook_arrays
        )

        # Save features if enabled and enough data is available
        if(
            len(self.orderbook_arrays['ask_prices']) >= self.max_orderbook_sequence_length and
            self.save_features
        ):
            self._save_features()

    def _get_weighted_prediction(self) -> Optional[Tuple[int, float]]:
        """
        Generate weighted prediction from multiple models.

        Combines orderbook and trade features, applies all models with their weights,
        and returns the predicted class and confidence.

        Returns
        -------
        Optional[Tuple[int, float]]
            A tuple containing (predicted_class, confidence) or None if prediction fails.
        """
        # Check if we have all required features
        if not self.latest_orderbook_features or not self.latest_trade_features:
            return None
        # Check if we have valid model weights
        if np.sum(self.model_weights) < 1e-6:
            return None

        try:
            # Concatenate orderbook and trade features
            features = np.concatenate([
                [self.latest_orderbook_features[feature_name] for feature_name in self.orderbook_features_order],
                [self.latest_trade_features[feature_name] for feature_name in self.trade_features_order]
            ])

            # Get predictions from all models
            all_probas = []
            for model in self.models:
                proba = model.predict([features])[0]
                all_probas.append(proba)

            # Store prediction history for model weight updates
            self.history_predicted.append([np.argmax(proba) for proba in all_probas])

            # Calculate and store return for this period
            current_price = self.orderbook_arrays['top_vwap_prices'][-1]
            past_price = self.orderbook_arrays['top_vwap_prices'][-self.prediction_shift]
            period_return = (current_price - past_price) / past_price
            self.history_returns.append(period_return)

            # Compute weighted prediction
            predict_proba = np.sum([proba * weight for proba, weight in zip(all_probas, self.model_weights)], axis=0)
            predict_label = np.argmax(predict_proba)
            predict_confidence = predict_proba[predict_label]

            return (predict_label, predict_confidence)

        except Exception as e:
            self.log.error(f"Error in _get_weighted_prediction: {e}")
            return None

    def _update_model_weights(self) -> None:
        """
        Update model weights based on prediction accuracy compared to actual future returns.

        Weights are calculated based on each model's prediction accuracy over
        the recent history window. Accuracy is measured by correctly predicting
        the direction of price movement after prediction_shift intervals.

        The history_predicted array contains model predictions at time t, while
        history_returns contains actual returns that can be compared with predictions
        made prediction_shift intervals ago.
        """
        # Wait until we have enough history data
        if len(self.history_returns) < 300:
            return

        # Initialize accuracy counters for each model
        model_accuracies = [0.0] * len(self.models)

        # Calculate accuracy for each model by comparing past predictions with actual returns
        for i in range(len(self.history_returns) - self.prediction_shift - 1, -1, -1):
            latest_predicted = self.history_predicted[i] # predicted  (price_cur - price_past)/price_past 
            real_return = self.history_returns[i + self.prediction_shift] # (price_cur - price_past) / price_past
            for j, prediction in enumerate(latest_predicted):
                if ((prediction > self.num_classes // 2 and real_return > 0) or
                        (prediction < self.num_classes // 2 and real_return < 0)):
                        model_accuracies[j] += 1

        # Normalize weights to sum to 1.0
        time_based_model_accuracies = [acc / 300 for acc in model_accuracies]
        self.log.info(f"the mode acc is {time_based_model_accuracies}")
        if sum(time_based_model_accuracies) > 1:
            self.model_weights = time_based_model_accuracies
        else:
            self.model_weights = [0.0, 0.0, 0.0]

    def _execute_trades(self) -> None:
        """
        Execute trades based on multi-class prediction signals.

        Handles position entry and exit based on model predictions and confidence levels.
        Implements a three-class system: predicted up (long), down (short), and no change.
        """
        # Get prediction
        prediction = self._get_weighted_prediction()
        if prediction is None:
            return

        predict_label, predict_confidence = prediction
        self.log.info(f" the predicted label is {predict_label}, the predicted confidence is {predict_confidence}")

        # Handle position exits - close long position on bearish prediction
        if (self.portfolio.is_net_long(self.instrument_id) and
            predict_label < self.num_classes // 2 and
            predict_confidence > self.exit_threshold):
            self.log.info(f"Closing long position: bearish prediction {predict_label} with confidence {predict_confidence:.4f}")
            # Get the position first
            positions = self.cache.positions(instrument_id=self.instrument_id)
            if positions:
                position = positions[0]  # Get the first position
                is_maker = predict_label >= self.num_classes//4
                self.order_manager.process_signal(
                    self.order_manager.exit_signals,
                    instrument=self.instrument,
                    side=OrderSide.SELL,
                    price=self.best_ask_price,
                    size=position.quantity.as_double(),
                    is_maker=is_maker,
                    time_in_force=TimeInForce.GTC,
                    force_update=False
                )
            return

        # Handle position exits - close short position on bullish prediction
        if (self.portfolio.is_net_short(self.instrument_id) and
            predict_label > self.num_classes // 2 and
            predict_confidence > self.exit_threshold):
            self.log.info(f"Closing short position: bullish prediction {predict_label} with confidence {predict_confidence:.4f}")
            # Get the position first
            positions = self.cache.positions(instrument_id=self.instrument_id)
            if positions:
                position = positions[0]  # Get the first position
                is_maker = predict_label <= self.num_classes//4*3
                self.order_manager.process_signal(
                    self.order_manager.exit_signals,
                    instrument=self.instrument,
                    side=OrderSide.BUY,
                    price=self.best_bid_price,  # Using bid price for buy orders
                    size=position.quantity.as_double(),
                    is_maker=is_maker,
                    time_in_force=TimeInForce.GTC,
                    force_update=False
                )
            return

        if not self.portfolio.is_flat(self.instrument_id):
            positions = self.cache.positions(instrument_id=self.instrument_id)
            for position in positions:
                # Use EnhancedOrderManager for position management
                self.order_manager.handle_position_management(
                    instrument=self.instrument,
                    position=position,
                    current_price=self.filter_top_vwap,
                    best_ask_price=self.best_ask_price,
                    best_bid_price=self.best_bid_price,
                    exit_with_maker=True,  
                    n_tickers=1, 
                )

    
        # Handle position entries when flat
        if self.portfolio.is_flat(self.instrument_id) and predict_confidence > self.entry_threshold and predict_label!=self.num_classes//2:
            # Calculate position size based on prediction
            position_usd = get_bet_size(
                prediction_class=predict_label,
                confidence=predict_confidence,
                max_trade_size=self.max_trade_usd,
                num_classes=self.num_classes
            )       
            if position_usd > 0:
                position_size  = min(position_usd/self.filter_top_vwap,self.best_bid_size)
            else:
                position_size  = min(abs(position_usd/self.filter_top_vwap),self.best_ask_size)
            self.log.info(f"the position usd is {position_usd}")       
            # Only enter if position size meets minimum notional value
            if abs(position_usd) >= self.min_notional:
                is_maker = (predict_label >= self.num_classes//4 and predict_label <= self.num_classes//4*3)
                self.order_manager.process_signal(
                    self.order_manager.entry_signals,
                    instrument=self.instrument,
                    side=OrderSide.BUY if position_usd > 0 else OrderSide.SELL,
                    price=self.best_bid_price if position_usd > 0 else self.best_ask_price,
                    size=position_size,
                    is_maker=is_maker,
                    time_in_force=TimeInForce.GTC,
                    force_update=False
                )
                self.log.info(
                    f"Entering {'long' if position_usd > 0 else 'short'} position: "
                    f"{position_size} unit, Class: {predict_label}, Confidence: {predict_confidence:.4f}"
                )

    def on_event(self, event: Event) -> None:
        """
        Handle incoming events.

        Processes order events and updates the strategy state accordingly.
        """
        self.order_manager.on_event(event)
        
    def _save_features(self) -> None:
        """
        Save the latest orderbook and trade features to a file.

        Combines features from both orderbook and trade sources and saves them
        to a pickle file for later analysis or model training.
        """
        try:
            # Generate filename with timestamp
            timestamp = self.clock.timestamp_ns()
            filename = f"{self.instrument_id.symbol}_{timestamp}.pkl"
            filepath = os.path.join(self.features_save_dir, filename)

            # Initialize data dictionary with timestamp
            data = {'timestamp': timestamp}

            # Add orderbook features
            for name in self.orderbook_features_order:
                data[name] = self.latest_orderbook_features[name]

            # Add trade features if available
            if self.latest_trade_features:
                for name in self.trade_features_order:
                    data[name] = self.latest_trade_features[name]

            # Save to pickle file
            with open(filepath, "wb") as f:
                pickle.dump(data, f)

            self.log.debug(f"Features saved to {filepath}")

        except Exception as e:
            self.log.error(f"Error saving features: {e}")

    def on_stop(self) -> None:
        """
        Stop the strategy and perform cleanup.

        Logs final state and calls parent cleanup.
        """
        self.log.info(f"Stopping strategy for {self.instrument_id}")

        # Call parent handler
        super().on_stop()



