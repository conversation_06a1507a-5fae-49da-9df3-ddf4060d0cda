import polars as pl
import numpy as np
from datetime import datetime, timedelta, timezone
import os
import glob
from pathlib import Path
import requests
import pandas as pd
import time

def calc_change_since_pivot(current,last_pivot):
    if(last_pivot == 0): last_pivot = 1 ** (-100) # avoid division by 0
    perc_change_since_pivot = (current - last_pivot) / abs(last_pivot)
    return perc_change_since_pivot

def get_zigzag(idx, row, taip=None):
    '''
    item:datetime,close,high,low
    '''
    if (taip == "Peak"):
        key = 2
    elif (taip == "Trough"):
        key = 3
    else:
        key = 1

    return {
        "datetime": row[0],
        "value": row[key],
        "type": taip,
        "idx":idx,
    }

def create_label(
    df,
    threshold = 0.03,
    stop_loss = None,
    cut_label = True,
    log_return = True,
):
    zigzags = []
    for idx,item in enumerate(df.select(["datetime","close","high","low"]).iter_rows()):
        is_starting = (idx == 0)
        if is_starting:
            zigzags.append(get_zigzag(idx,item))
            continue  

        is_first_line = (len(zigzags) == 1) 
        if is_first_line:
            perc_change_since_pivot = calc_change_since_pivot(item[1],zigzags[-1]["value"])
            if abs(perc_change_since_pivot) >= threshold:
                if perc_change_since_pivot > 0:
                    zigzags.append(get_zigzag(idx, item,"Peak"))
                    zigzags[0]["type"] = "Through"
                else:
                    zigzags.append(get_zigzag(idx, item, "Trough"))
                    zigzags[0]["type"] = "Peak" 
            continue 
        is_through = zigzags[-2]["value"] > zigzags[-1]["value"]
        is_ending = (idx == df.shape[0] - 1)
        last_pivot = float(zigzags[-1]["value"])
        # based on last pivot type, look for reversal or continuation
        if(is_through):
            perc_change_since_pivot = calc_change_since_pivot(item[1],zigzags[-1]["value"])
            is_reversing = (perc_change_since_pivot >= threshold) or is_ending
            is_continuing = item[1] <= last_pivot
            if (is_continuing): 
                zigzags[-1] = get_zigzag(idx,item, "Trough")
            elif (is_reversing): 
                zigzags.append(get_zigzag(idx,item, "Peak"))
        else:
            perc_change_since_pivot = calc_change_since_pivot(item[1],zigzags[-1]["value"])
            is_reversing = (perc_change_since_pivot <= -threshold) or is_ending
            is_continuing = item[1] >= last_pivot
            if(is_continuing): 
                zigzags[-1] = get_zigzag(idx,item, "Peak")
            elif (is_reversing): 
                zigzags.append(get_zigzag(idx,item, "Trough"))

    zigzags = pl.DataFrame(zigzags)
    
    # Ensure datetime precision matches between dataframes
    # Cast zigzag datetime to match the main dataframe datetime precision
    main_dt_dtype = df.select("datetime").dtypes[0]
    zigzags = zigzags.with_columns([
        pl.col("datetime").cast(main_dt_dtype)
    ])
    
    zigzags = zigzags.select([
        pl.all(),
        pl.col("datetime").shift(-1).alias("event_ends"),
        pl.col("value").shift(-1).alias("prevext")
    ])
    if zigzags.shape[0] < 2:
        print(f"⚠️  Insufficient zigzag points: {zigzags.shape[0]}, need at least 2")
        return None 
    df = df.join(zigzags, on = "datetime", how = "left")
    df = df.select(
        [pl.col(item).fill_null(strategy = "forward") if item in ["prevext","event_ends"] else pl.col(item) for item in df.columns]
    )
    df = df.select(
        [pl.all(), (pl.col("prevext")/pl.col("close") - 1.0).alias("label")]
    )
    correct_label = [] 
    event_ends = [] 
    if stop_loss:
        total_returns = df.select("label").to_numpy().flatten() 
        original_event_ends = df.select("event_ends").to_numpy().flatten() 
        original_datetime = df.select("datetime").to_numpy().flatten() 
        close_array = df.select("close").to_numpy().flatten() 
        high_array = df.select("high").to_numpy().flatten()
        low_array = df.select("low").to_numpy().flatten()

        for i in range(zigzags.shape[0]-1):
            start_idx = zigzags[i,"idx"]
            end_idx = zigzags[i+1,"idx"]
            next_end_idx = zigzags[i+2,"idx"] if i+2 < zigzags.shape[0] else df.shape[0]-1
            for j in range(start_idx,end_idx):
                if total_returns[j] > 0:
                    min_acc_arg = np.argmin(low_array[j+1:end_idx+1]) + j+1
                    min_acc = min((low_array[min_acc_arg]-close_array[j])/close_array[j],0)
                    if min_acc > -stop_loss:
                        correct_label.append(total_returns[j])
                        event_ends.append(original_event_ends[j])
                    else:
                        correct_label.append(min_acc)
                        event_ends.append(original_datetime[min_acc_arg])
                else:
                    min_acc_arg = np.argmax(high_array[j+1:end_idx+1]) + j+1
                    min_acc = max((high_array[min_acc_arg]-close_array[j])/close_array[j],0)
                    if min_acc < stop_loss:
                        correct_label.append(total_returns[j])
                        event_ends.append(original_event_ends[j])
                    else:
                        correct_label.append(min_acc)
                        event_ends.append(original_datetime[min_acc_arg])
        #replace label of df 
        df = df[:len(correct_label),:]
        df.replace("label",pl.Series(correct_label))
        df.replace("event_ends",pl.Series(event_ends))
       
    ## drop the front data because zigzag is meanless on these data 
    df = df.filter((pl.col("datetime")>=zigzags[1,"datetime"]))

    df = df.select(pl.all().exclude(['value', 'type', 'idx', 'prevext']))
    df = df.with_columns( [pl.col("datetime").alias("event_starts")])
    if cut_label:
        label_array = df[:,"label"].to_numpy()
        df = df.select([
            pl.all().exclude("label"),
            (pl.when(pl.col("label")>label_array.mean() +5.0*label_array.std())
             .then(label_array.mean() +5.0*label_array.std())
             .otherwise(pl.col("label"))).alias("label"),
        ])
        df = df.select([
            pl.all().exclude("label"),
            (pl.when(pl.col("label")< label_array.mean() - 5.0*label_array.std())
             .then(label_array.mean() - 5.0*label_array.std())
             .otherwise(pl.col("label"))).alias("label"),
        ])
    df = df.select(
            [pl.all(), pl.arange(0, pl.count()).alias("count_index")]
        )
    if log_return:
        df = df.with_columns(
            [
                (pl.col("label")+1.0).log().alias("label"),
            ]
        )

    return df 

def convert_utc8_to_utc0(time_str, date_str):
    """
    Convert UTC+8 time to UTC+0
    Args:
        time_str: Time string like "23:45"
        date_str: Date string like "1月1日"
    Returns:
        UTC datetime
    """
    try:
        # Parse Chinese date format
        month_day = date_str.replace("月", "-").replace("日", "")
        if "-" in month_day:
            month, day = month_day.split("-")
            # Assume current year (2025 based on the data)
            year = 2025
            
            # Create datetime in UTC+8
            dt_utc8 = datetime(year, int(month), int(day))
            
            # Parse time
            if ":" in time_str:
                hour, minute = time_str.split(":")
                dt_utc8 = dt_utc8.replace(hour=int(hour), minute=int(minute))
            
            # Convert UTC+8 to UTC+0 (subtract 8 hours)
            dt_utc0 = dt_utc8 - timedelta(hours=8)
            
            return dt_utc0
    except Exception as e:
        print(f"Error converting time {time_str} {date_str}: {e}")
        return None

def compute_labels_for_symbol(symbol, threshold=0.03, stop_loss=None):
    """
    Compute labels for a single symbol and save to file
    """
    print(f"Computing labels for {symbol}")
    
    price_file = f"data/{symbol}_5m.parquet"
    if not os.path.exists(price_file):
        print(f"Price data not found for {symbol}: {price_file}")
        return False
    
    try:
        # Load price data
        df = pl.read_parquet(price_file)
        print(f"Loaded {df.shape[0]} price records for {symbol}")
        
        # Check required columns
        required_cols = ["timestamp", "close", "high", "low"]
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            print(f"Missing required columns for {symbol}: {missing_cols}")
            return False
        
        # Rename timestamp to datetime for compatibility with create_label function
        df = df.rename({"timestamp": "datetime"})
        
        # Check if we have enough data
        if df.shape[0] < 10:
            print(f"Insufficient data for {symbol}: only {df.shape[0]} records")
            return False
        
        # Use create_label function to calculate returns
        print(f"Computing labels for {symbol} with threshold={threshold}")
        labeled_df = create_label(
            df,
            threshold=threshold,
            stop_loss=stop_loss,
            cut_label=True,
            log_return=True
        )
        
        # Verify label computation was successful
        if labeled_df is None or labeled_df.shape[0] == 0:
            print(f"Label computation failed for {symbol}: empty result")
            return False
        
        # Check if labels were actually computed
        if "label" not in labeled_df.columns:
            print(f"Label computation failed for {symbol}: no label column")
            return False
        
        # Check for valid labels (not all null)
        label_count = labeled_df.filter(pl.col("label").is_not_null()).shape[0]
        if label_count == 0:
            print(f"Label computation failed for {symbol}: all labels are null")
            return False
        
        print(f"Successfully computed {label_count} valid labels for {symbol}")
        
        # Keep all original columns plus computed labels
        result_df = labeled_df.select([
            "datetime",
            "open",
            "high", 
            "low",
            "close",
            "volume",
            "label",
            "event_starts",
            "event_ends"
        ])
        
        # Save labeled data
        output_file = f"data/{symbol}_5m_labeled.parquet"
        result_df.write_parquet(output_file)
        
        print(f"Saved labeled data for {symbol} to {output_file}")
        print(f"Generated {result_df.shape[0]} labeled records with {label_count} valid labels")
        
        # Print label statistics
        label_stats = result_df.filter(pl.col("label").is_not_null()).select([
            pl.col("label").mean().alias("mean_label"),
            pl.col("label").std().alias("std_label"),
            pl.col("label").min().alias("min_label"),
            pl.col("label").max().alias("max_label")
        ])
        print(f"Label statistics for {symbol}: {label_stats.to_dicts()[0]}")
        
        return True
        
    except Exception as e:
        print(f"Error computing labels for {symbol}: {e}")
        import traceback
        traceback.print_exc()
        return False

def download_binance_data(symbol, start_time, end_time, interval="5m"):
    """
    Download data from Binance API for a specific time range
    Args:
        symbol: Trading symbol (e.g., 'BTCUSDT')
        start_time: Start datetime
        end_time: End datetime
        interval: Time interval (default '5m')
    Returns:
        DataFrame with OHLCV data or None if failed
    """
    try:
        base_url = "https://fapi.binance.com"
        url = f"{base_url}/fapi/v1/klines"
        
        # Convert to timestamps
        start_timestamp = int(start_time.timestamp() * 1000)
        end_timestamp = int(end_time.timestamp() * 1000)
        
        params = {
            "symbol": symbol,
            "interval": interval,
            "startTime": start_timestamp,
            "endTime": end_timestamp,
            "limit": 1500
        }
        
        print(f"📊 Downloading {symbol} data from {start_time} to {end_time}")
        
        response = requests.get(url, params=params)
        response.raise_for_status()
        
        data = response.json()
        
        if not data:
            print(f"❌ No data returned for {symbol}")
            return None
        
        # Convert to structured format
        klines = []
        for kline in data:
            klines.append({
                'timestamp': pd.to_datetime(int(kline[0]), unit='ms', utc=True),
                'open': float(kline[1]),
                'high': float(kline[2]),
                'low': float(kline[3]),
                'close': float(kline[4]),
                'volume': float(kline[5]),
            })
        
        # Convert to Polars DataFrame
        df = pl.DataFrame(klines)
        df = df.sort("timestamp").unique(subset=["timestamp"], keep="last")
        
        print(f"✅ Downloaded {df.shape[0]} records for {symbol}")
        return df
        
    except requests.exceptions.RequestException as e:
        print(f"❌ API Error downloading {symbol}: {e}")
        return None
    except Exception as e:
        print(f"❌ Error downloading {symbol}: {e}")
        return None

def download_and_compute_labels_for_symbol(symbol, news_timestamp, threshold=0.03, max_attempts=3):
    """
    Download data around news timestamp and compute labels on-the-fly with adaptive time range
    Args:
        symbol: Trading symbol
        news_timestamp: News datetime
        threshold: Threshold for label computation
        max_attempts: Maximum attempts with increasing time ranges
    Returns:
        DataFrame with computed labels or None if failed
    """
    
    # Try with increasing time ranges to improve success rate
    time_ranges = [
        (2, "2 days"),    # Start with 2 days before/after
        (3, "3 days"),    # Extend to 3 days if needed
        (5, "5 days"),    # Final attempt with 5 days
    ]
    
    for attempt, (days, description) in enumerate(time_ranges, 1):
        try:
            print(f"🔄 Auto-downloading and computing labels for {symbol} (attempt {attempt}/{len(time_ranges)}: {description})")
            
            # Define time range with current attempt's days
            start_time = news_timestamp - timedelta(days=days)
            end_time = news_timestamp + timedelta(days=days)
            
            print(f"📅 Time range: {start_time} to {end_time}")
            
            # Download data in chunks
            all_data = []
            current_start = start_time
            
            while current_start < end_time:
                chunk_end = min(current_start + timedelta(hours=60), end_time)
                
                chunk_df = download_binance_data(symbol, current_start, chunk_end)
                
                if chunk_df is not None and chunk_df.shape[0] > 0:
                    all_data.append(chunk_df)
                
                current_start = chunk_end
                
                # Small delay to avoid API limits
                time.sleep(0.1)
            
            if not all_data:
                print(f"❌ No data downloaded for {symbol} in attempt {attempt}")
                continue
            
            # Combine all chunks
            df = pl.concat(all_data)
            df = df.sort("timestamp").unique(subset=["timestamp"], keep="last")
            
            print(f"📊 Combined {df.shape[0]} records for {symbol}")
            
            # Check if we have enough data for zigzag computation
            min_records_needed = max(100, int(days * 288 * 0.1))  # At least 10% of expected 5min records per day
            if df.shape[0] < min_records_needed:
                print(f"⚠️  Insufficient data for {symbol}: {df.shape[0]} records, need at least {min_records_needed}")
                if attempt < len(time_ranges):
                    print(f"🔄 Trying with longer time range...")
                    continue
                else:
                    return None
            
            # Rename timestamp to datetime for create_label compatibility
            df = df.rename({"timestamp": "datetime"})
            
            # Compute labels
            print(f"🧮 Computing labels for {symbol} with threshold={threshold}")
            labeled_df = create_label(
                df,
                threshold=threshold,
                stop_loss=None,
                cut_label=True,
                log_return=True
            )
            
            if labeled_df is None:
                print(f"⚠️  Label computation returned None for {symbol} (attempt {attempt})")
                if attempt < len(time_ranges):
                    print(f"🔄 Trying with longer time range...")
                    continue
                else:
                    return None
            
            if labeled_df.shape[0] == 0:
                print(f"⚠️  Label computation returned empty result for {symbol} (attempt {attempt})")
                if attempt < len(time_ranges):
                    print(f"🔄 Trying with longer time range...")
                    continue
                else:
                    return None
            
            # Check for valid labels
            label_count = labeled_df.filter(pl.col("label").is_not_null()).shape[0]
            if label_count == 0:
                print(f"⚠️  No valid labels computed for {symbol} (attempt {attempt})")
                if attempt < len(time_ranges):
                    print(f"🔄 Trying with longer time range...")
                    continue
                else:
                    return None
            
            print(f"✅ Successfully computed {label_count} labels for {symbol} using {description}")
            
            # Print time range for debugging
            time_range = f"{labeled_df['datetime'].min()} to {labeled_df['datetime'].max()}"
            print(f"📅 Label time range: {time_range}")
            
            # Check if news timestamp is within the label range
            news_dt64 = np.datetime64(news_timestamp.isoformat())
            timestamps = labeled_df.select("datetime").to_numpy().flatten()
            
            if timestamps.dtype.kind != 'M':
                timestamps = timestamps.astype('datetime64[ns]')
            
            if news_dt64 >= timestamps.min() and news_dt64 <= timestamps.max():
                print(f"✅ News timestamp {news_timestamp} is within label range")
            else:
                print(f"⚠️  News timestamp {news_timestamp} is outside label range")
            
            return labeled_df
            
        except Exception as e:
            print(f"❌ Error in attempt {attempt} for {symbol}: {e}")
            if attempt < len(time_ranges):
                print(f"🔄 Trying with longer time range...")
                continue
            else:
                import traceback
                traceback.print_exc()
                return None
    
    print(f"❌ All attempts failed for {symbol}")
    return None

def load_labeled_data(symbol):
    """
    Load pre-computed labeled data for a symbol
    """
    labeled_file = f"data/{symbol}_5m_labeled.parquet"
    if os.path.exists(labeled_file):
        try:
            return pl.read_parquet(labeled_file)
        except Exception as e:
            print(f"Error loading labeled data for {symbol}: {e}")
            return None
    else:
        # Don't print "not found" message as this is normal during lookup
        return None

def load_or_download_labeled_data(symbol, news_timestamp=None):
    """
    Load pre-computed labeled data, or download and compute if not available
    Priority: 1) Existing *_labeled.parquet 2) Auto-download and compute
    Args:
        symbol: Trading symbol
        news_timestamp: News datetime (used for download range if needed)
    Returns:
        DataFrame with labeled data or None if failed
    """
    # Step 1: Try to load existing labeled data from data folder
    labeled_df = load_labeled_data(symbol)
    
    if labeled_df is not None:
        print(f"✅ Using existing labeled data for {symbol}")
        return labeled_df
    
    # Step 2: If not found and we have a news timestamp, auto-download and compute
    if news_timestamp is not None:
        print(f"📂 No existing labeled data found for {symbol}")
        print(f"🔄 Attempting to download and compute labels for {symbol}")
        
        # Add small delay to avoid hitting API limits
        time.sleep(0.2)
        
        labeled_df = download_and_compute_labels_for_symbol(symbol, news_timestamp)
        
        if labeled_df is not None:
            # Save the computed data as temporary file for future use
            try:
                output_file = f"data/{symbol}_5m_labeled_temp.parquet"
                result_df = labeled_df.select([
                    "datetime",
                    "open",
                    "high", 
                    "low",
                    "close",
                    "volume",
                    "label",
                    "event_starts",
                    "event_ends"
                ])
                result_df.write_parquet(output_file)
                print(f"💾 Saved temporary labeled data to {output_file}")
            except Exception as e:
                print(f"⚠️  Could not save temporary data: {e}")
        
        return labeled_df
    
    print(f"❌ No labeled data available for {symbol} and no news timestamp provided")
    return None

def find_nearest_label(news_timestamp, labeled_df):
    """
    Find the nearest label for a given news timestamp
    """
    if news_timestamp is None or labeled_df is None:
        return None
    
    try:
        # Convert news timestamp to numpy datetime64 for comparison
        news_dt64 = np.datetime64(news_timestamp.isoformat())
        
        # Get timestamps from labeled data as datetime64 array
        timestamps = labeled_df.select("datetime").to_numpy().flatten()
        
        # Ensure timestamps are datetime64 type
        if timestamps.dtype.kind != 'M':  # 'M' is datetime64
            # If timestamps are integers (nanoseconds), convert to datetime64
            timestamps = timestamps.astype('datetime64[ns]')
        
        # Find index of closest timestamp that is >= news timestamp (future looking)
        future_mask = timestamps >= news_dt64
        if not np.any(future_mask):
            return None
        
        future_timestamps = timestamps[future_mask]
        time_diffs = future_timestamps - news_dt64
        closest_idx = np.argmin(time_diffs)
        actual_idx = np.where(timestamps >= news_dt64)[0][closest_idx]
        
        # Return the label using proper Polars indexing
        return labeled_df.row(actual_idx, named=True)["label"]
        
    except Exception as e:
        print(f"Error finding nearest label: {e}")
        return None

def compute_all_labels():
    """
    Compute labels for all available symbols
    """
    print("=== Computing labels for all symbols ===")
    
    # Find all price data files
    price_files = glob.glob("data/*_5m.parquet")
    symbols = [os.path.basename(f).replace("_5m.parquet", "") for f in price_files]
    
    print(f"Found {len(symbols)} symbols: {symbols}")
    
    success_count = 0
    for symbol in symbols:
        if compute_labels_for_symbol(symbol):
            success_count += 1
    
    print(f"Successfully computed labels for {success_count}/{len(symbols)} symbols")

def process_news_file(news_file_path):
    """
    Process a single news CSV file and add labels using pre-computed data
    """
    print(f"Processing {news_file_path}")
    
    try:
        # Try to load news data with different encodings
        news_df = None
        for encoding in ['utf-8-sig', 'utf-8', 'gbk', 'gb2312']:
            try:
                # Use pandas to read with specific encoding, then convert to polars
                import pandas as pd
                pandas_df = pd.read_csv(news_file_path, encoding=encoding)
                news_df = pl.from_pandas(pandas_df)
                print(f"Successfully loaded {news_file_path} with {encoding} encoding")
                break
            except Exception as e:
                continue
        
        if news_df is None:
            print(f"Failed to load {news_file_path} with any encoding")
            return None
        
        print(f"Loaded {news_df.shape[0]} news records from {news_file_path}")
        
        # Add columns for labels
        results = []
        processed_count = 0
        labeled_count = 0
        
        for row in news_df.iter_rows(named=True):
            result_row = dict(row)
            processed_count += 1
            
            # Convert time to UTC
            utc_time = convert_utc8_to_utc0(row['time'], row['date'])
            result_row['utc_timestamp'] = utc_time.isoformat() if utc_time else None
            
            if utc_time is None:
                result_row['future_returns'] = None
                result_row['processed_symbols'] = None
                results.append(result_row)
                continue
            
            # Parse relative symbols
            symbols = []
            if row['relative_symbols'] and row['relative_symbols'].strip():
                symbols = [s.strip() for s in row['relative_symbols'].split(',')]
            
            if not symbols:
                result_row['future_returns'] = None
                result_row['processed_symbols'] = None
                results.append(result_row)
                continue
            
            # Get labels for each symbol in the same order as relative_symbols
            symbol_returns = []
            processed_symbols = []
            
            for symbol in symbols:
                # Load pre-computed labeled data or download and compute if needed
                labeled_df = load_or_download_labeled_data(symbol, utc_time)
                
                if labeled_df is not None:
                    # Find nearest label
                    label = find_nearest_label(utc_time, labeled_df)
                    
                    if label is not None:
                        symbol_returns.append(f"{label:.6f}")  # Only the numeric value
                        processed_symbols.append(symbol)
                    else:
                        symbol_returns.append("")  # Empty string for missing labels
                        processed_symbols.append(symbol)
                else:
                    symbol_returns.append("")  # Empty string for missing data
                    processed_symbols.append(symbol)
            
            # Join returns with comma - maintains order with relative_symbols
            result_row['future_returns'] = ','.join(symbol_returns) if any(symbol_returns) else None
            result_row['processed_symbols'] = ','.join(processed_symbols) if processed_symbols else None
            
            if result_row['future_returns'] is not None:
                labeled_count += 1
            
            results.append(result_row)
        
        # Convert back to DataFrame
        result_df = pl.DataFrame(results)
        
        # Save processed file with UTF-8-BOM encoding for Chinese text
        output_file = news_file_path.replace('.csv', '_labeled.csv')
        
        # Convert to pandas for UTF-8-BOM saving
        import pandas as pd
        pandas_result = result_df.to_pandas()
        pandas_result.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        print(f"Saved labeled data to {output_file} with UTF-8-BOM encoding")
        print(f"Processed {processed_count} news items, {labeled_count} with labels ({labeled_count/processed_count*100:.1f}%)")
        
        # Verify the saved file can be read back
        try:
            verify_df = pd.read_csv(output_file, encoding='utf-8-sig')
            print(f"Verification: Successfully read back {len(verify_df)} records from saved file")
        except Exception as e:
            print(f"Warning: Could not verify saved file: {e}")
        
        return result_df
        
    except Exception as e:
        print(f"Error processing {news_file_path}: {e}")
        import traceback
        traceback.print_exc()
        return None

def process_all_news():
    """
    Process all news files using pre-computed labels
    """
    print("=== Processing all news files ===")
    
    # Find all CSV files in filter_news directory, excluding already labeled ones
    all_files = glob.glob("filter_news/*.csv")
    news_files = [f for f in all_files if not f.endswith('_labeled.csv')]
    
    if not news_files:
        print("No CSV files found in filter_news directory (excluding already labeled files)")
        return
    
    print(f"Found {len(news_files)} news files to process (excluding {len(all_files) - len(news_files)} already labeled files)")
    
    # Process each file
    success_count = 0
    for news_file in news_files:
        if os.path.exists(
            news_file.replace(".csv","_labeled.csv")
        ):
            print("file has processed!")
            continue
        try:
            if process_news_file(news_file) is not None:
                success_count += 1
        except Exception as e:
            print(f"Failed to process {news_file}: {e}")
            continue
    
    print(f"Successfully processed {success_count}/{len(news_files)} news files")

def main():
    """
    Main function - first compute all labels for existing data, then process news
    """
    print("Starting news labeling process...")
    print("🔄 Step 1: Computing labels for all existing price data...")
    
    # Step 1: Compute labels for all existing symbols in data folder
    compute_all_labels()
    
    print("\n" + "="*50 + "\n")
    print("🔄 Step 2: Processing news files (with auto-download fallback)...")
    
    # Step 2: Process all news files (will use existing labels or auto-download)
    process_all_news()
    
    print("\n🎉 Processing complete!")
    print("💡 Summary:")
    print("   ✅ All existing price data has been labeled")
    print("   ✅ News processing uses existing labels when available")
    print("   ✅ Auto-download is used only for missing symbols")

if __name__ == "__main__":
    main()