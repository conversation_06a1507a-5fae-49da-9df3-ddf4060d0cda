import json
import time
import urllib.request
from datetime import datetime

# Binance Futures API endpoints
EXCHANGE_INFO_URL = "https://fapi.binance.com/fapi/v1/exchangeInfo"
KLINES_URL = "https://fapi.binance.com/fapi/v1/klines"

def fetch_url(url):
    """Fetches data from a URL using urllib."""
    try:
        with urllib.request.urlopen(url, timeout=10) as response:
            if response.status == 200:
                return json.loads(response.read().decode('utf-8'))
            else:
                print(f"Error fetching {url}: HTTP Status {response.status}")
                return None
    except Exception as e:
        print(f"Error fetching {url}: {e}")
        return None

def get_tradable_symbols():
    """Fetches all tradable symbols from Binance."""
    data = fetch_url(EXCHANGE_INFO_URL)
    if data:
        # We will fetch info for all symbols, not just currently trading ones,
        # to potentially get delisting dates for recently offline assets.
        symbols = [
            {
                'symbol': s['symbol'],
                'contractType': s.get('contractType'),
                'deliveryDate': s.get('deliveryDate'),
                'onboardDate': s.get('onboardDate')
            }
            for s in data['symbols'] if s['symbol'].endswith('USDT')
        ]
        print(f"Found {len(symbols)} USDT pairs to process.")
        return symbols
    return []

def main():
    """Main function to get all symbols and their trading windows and save to a file."""
    print("Fetching symbols from Binance...")
    symbols = get_tradable_symbols()
    if not symbols:
        print("No symbols found. Exiting.")
        return
    symbol_data = {}
    total_symbols = len(symbols)
    print(f"\nProcessing {total_symbols} symbols...")
    for i, symbol_info in enumerate(symbols):
        symbol = symbol_info['symbol']
        print(f"Processing symbol {i+1}/{total_symbols}: {symbol}")
        symbol_data[symbol] = {
            'contractType': symbol_info['contractType'],
            'deliveryDate': symbol_info['deliveryDate'],
            'onboardDate': symbol_info['onboardDate']
        }
    output_filename = 'binance_symbols.json'
    with open(output_filename, 'w') as f:
        json.dump(symbol_data, f, indent=4)
    print(f"\nSuccessfully saved symbol data to {output_filename}")

if __name__ == "__main__":
    main()