"""
新闻数据预处理和训练集构建
处理多symbol新闻，拆分数据，映射连续return为5分类
"""

import pandas as pd
import numpy as np
import os
import glob
from sklearn.model_selection import train_test_split
from sklearn.feature_extraction.text import TfidfVectorizer
import jieba
import re
from typing import List, Tuple, Dict
import warnings
warnings.filterwarnings('ignore')

class NewsDataPreprocessor:
    def __init__(self, data_dir: str = "filter_news"):
        self.data_dir = data_dir
        self.vectorizer = None
        self.processed_data = None
        
    def load_labeled_data(self) -> pd.DataFrame:
        """加载所有labeled.csv文件"""
        labeled_files = glob.glob(os.path.join(self.data_dir, "*_labeled.csv"))
        
        all_data = []
        for file in labeled_files:
            try:
                df = pd.read_csv(file)
                all_data.append(df)
                print(f"加载文件: {file}, 数据量: {len(df)}")
            except Exception as e:
                print(f"加载文件失败 {file}: {e}")
                
        if not all_data:
            raise ValueError("未找到任何labeled.csv文件")
            
        combined_df = pd.concat(all_data, ignore_index=True)
        print(f"总数据量: {len(combined_df)}")
        return combined_df
    
    def split_multi_symbol_news(self, df: pd.DataFrame) -> pd.DataFrame:
        """拆分多symbol新闻数据"""
        expanded_rows = []
        
        for idx, row in df.iterrows():
            # 检查原始字符串中的逗号数量是否一致
            symbols_str = str(row['relative_symbols'])
            returns_str = str(row['future_returns'])
            
            symbols_commas = symbols_str.count(',')
            returns_commas = returns_str.count(',')
            
            # 如果逗号数量不一致，跳过整行
            if symbols_commas != returns_commas:
                print(f"警告: 行{idx} symbols和returns逗号数量不一致，跳过 - symbols:{symbols_commas+1}, returns:{returns_commas+1}")
                continue
                
            # 拆分为列表
            symbols = symbols_str.split(',')
            returns = returns_str.split(',')
            
            # 逐一处理每对symbol和return
            for symbol, return_val in zip(symbols, returns):
                symbol = symbol.strip()
                return_val = return_val.strip()
                
                # 跳过空的return值或symbol
                if not symbol or return_val == '' or return_val == 'nan':
                    print(f"跳过空值: symbol={symbol}, return={return_val}")
                    continue
                    
                try:
                    return_float = float(return_val)
                    
                    # 创建新行
                    new_row = row.copy()
                    new_row['symbol'] = symbol
                    new_row['future_return'] = return_float
                    expanded_rows.append(new_row)
                    
                except ValueError:
                    print(f"无法转换return值为float: {return_val}")
                    continue
        
        expanded_df = pd.DataFrame(expanded_rows)
        print(f"拆分后数据量: {len(expanded_df)}")
        return expanded_df
    
    def create_return_categories(self, returns: np.ndarray) -> np.ndarray:
        """将连续return映射为5分类，使用固定阈值"""
        # 先分析数据分布（仅用于参考）
        print("分析return数据分布...")
        q10 = np.percentile(returns, 10)
        q25 = np.percentile(returns, 25)
        q50 = np.percentile(returns, 50)
        q75 = np.percentile(returns, 75)
        q90 = np.percentile(returns, 90)
        print(f"Return分位数: 10%={q10:.4f}, 25%={q25:.4f}, 50%={q50:.4f}, 75%={q75:.4f}, 90%={q90:.4f}")
        
        # 使用手动设置的固定阈值，更符合金融意义
        strong_down_threshold = -0.05  # 强跌阈值 -5%
        weak_down_threshold = -0.015   # 弱跌阈值 -1.5%
        neutral_threshold_pos = 0.015   # 横盘上限 1.5%
        weak_up_threshold = 0.05       # 弱涨上限 5%
        
        print(f"手动设置的固定阈值: 强跌<={strong_down_threshold:.4f}, 弱跌<={weak_down_threshold:.4f}, " +
              f"横盘<={neutral_threshold_pos:.4f}, 弱涨<={weak_up_threshold:.4f}, 强涨>{weak_up_threshold:.4f}")
        
        categories = np.zeros(len(returns), dtype=int)
        categories[returns <= strong_down_threshold] = 0  # 强跌
        categories[(returns > strong_down_threshold) & (returns <= weak_down_threshold)] = 1  # 弱跌
        categories[(returns > weak_down_threshold) & (returns <= neutral_threshold_pos)] = 2  # 横盘
        categories[(returns > neutral_threshold_pos) & (returns <= weak_up_threshold)] = 3  # 弱涨
        categories[returns > weak_up_threshold] = 4  # 强涨
        
        # 打印分类分布
        unique, counts = np.unique(categories, return_counts=True)
        for cat, count in zip(unique, counts):
            cat_names = ['强跌', '弱跌', '横盘', '弱涨', '强涨']
            print(f"类别{cat}({cat_names[cat]}): {count}个样本 ({count/len(categories)*100:.1f}%)")
            
        return categories
    
    def preprocess_text(self, text: str) -> str:
        """文本预处理"""
        if pd.isna(text):
            return ""
        
        # 使用轻量级文本处理器进行预处理
        try:
            from crypto_text_processor import CryptoNewsTextProcessor
            processor = CryptoNewsTextProcessor()
            text = processor.process(str(text))
        except ImportError:
            print("未找到CryptoNewsTextProcessor，使用基本清洗")
            # 基本清洗：移除特殊字符，保留中文、英文、数字
            text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]', ' ', str(text))
            # 英文转小写
            text = re.sub(r'[a-zA-Z]+', lambda x: x.group(0).lower(), text)
        
        # 使用SentencePiece分词器（如果可用）
        try:
            import sentencepiece as spm
            # 检查是否存在预训练的分词器模型
            model_path = "models/crypto_tokenizer_medium_with_symbols.model"
            if os.path.exists(model_path):
                sp = spm.SentencePieceProcessor()
                sp.load(model_path)
                tokens = sp.encode_as_pieces(text)
                return ' '.join(tokens)
            else:
                # 如果没有找到模型，回退到jieba
                print("未找到SentencePiece模型，回退到jieba分词")
                words = jieba.cut(text)
                return ' '.join(words)
        except ImportError:
            # 如果没有安装SentencePiece，回退到jieba
            print("未安装SentencePiece，回退到jieba分词")
            words = jieba.cut(text)
            return ' '.join(words)
    
    def create_features(self, df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """创建特征和标签"""
        # 合并标题和内容
        df['combined_text'] = df['title'].fillna('') + ' ' + df['content'].fillna('')
        
        # 文本预处理
        print("开始文本预处理...")
        df['processed_text'] = df['combined_text'].apply(self.preprocess_text)
        
        # 移除空文本
        df = df[df['processed_text'].str.len() > 0].copy()
        print(f"移除空文本后数据量: {len(df)}")
        
        # 创建TF-IDF特征
        print("创建TF-IDF特征...")
        self.vectorizer = TfidfVectorizer(
            max_features=5000,
            min_df=2,
            max_df=0.95,
            ngram_range=(1, 2),
            stop_words=None
        )
        
        X = self.vectorizer.fit_transform(df['processed_text'])
        
        # 创建分类标签
        y = self.create_return_categories(df['future_return'].values)
        
        print(f"特征维度: {X.shape}")
        print(f"标签数量: {len(y)}")
        
        return X, y, df
    
    def process_data(self) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """完整的数据处理流程"""
        print("=== 开始数据预处理 ===")
        
        # 1. 加载数据
        df = self.load_labeled_data()
        
        # 2. 拆分多symbol新闻
        df_expanded = self.split_multi_symbol_news(df)
        
        # 3. 创建特征和标签
        X, y, processed_df = self.create_features(df_expanded)
        
        # 4. 划分训练测试集
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        print(f"训练集大小: {X_train.shape[0]}")
        print(f"测试集大小: {X_test.shape[0]}")
        
        # 保存处理后的数据
        self.processed_data = {
            'X_train': X_train,
            'X_test': X_test, 
            'y_train': y_train,
            'y_test': y_test,
            'vectorizer': self.vectorizer,
            'processed_df': processed_df
        }
        
        return X_train, X_test, y_train, y_test
    
    def save_processed_data(self, filename: str = "processed_news_data.pkl"):
        """保存处理后的数据"""
        if self.processed_data is None:
            raise ValueError("请先运行process_data()方法")
            
        import pickle
        with open(filename, 'wb') as f:
            pickle.dump(self.processed_data, f)
        print(f"数据已保存到: {filename}")
    
    def load_processed_data(self, filename: str = "processed_news_data.pkl"):
        """加载处理后的数据"""
        import pickle
        with open(filename, 'rb') as f:
            self.processed_data = pickle.load(f)
        print(f"数据已从{filename}加载")
        return (
            self.processed_data['X_train'],
            self.processed_data['X_test'],
            self.processed_data['y_train'], 
            self.processed_data['y_test']
        )

def main():
    """主函数"""
    preprocessor = NewsDataPreprocessor()
    
    try:
        # 处理数据
        X_train, X_test, y_train, y_test = preprocessor.process_data()
        
        # 保存数据
        preprocessor.save_processed_data()
        
        print("\n=== 数据预处理完成 ===")
        print("可以使用以下代码加载数据进行模型训练:")
        print("from news_data_preprocessor import NewsDataPreprocessor")
        print("preprocessor = NewsDataPreprocessor()")
        print("X_train, X_test, y_train, y_test = preprocessor.load_processed_data()")
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()