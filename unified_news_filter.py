"""
统一新闻过滤模块
基于关键词匹配和主题相关性的新闻过滤器

核心功能：
1. 关键词匹配：使用模型分析获得的高价值关键词
2. 主题相关性：与预测性主题的匹配度计算
3. 综合评分：关键词数量 + 主题相关性 = 最终分数
4. 过滤决策：基于阈值判断新闻是否值得预测

设计原则：
- 参数化配置：所有文件路径和阈值通过参数传入
- 模块化设计：功能独立，易于测试和维护
- 简洁逻辑：专注核心功能，避免复杂评分
"""

import os
import json
import re
import logging
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass

# 注意：接受预处理后的文本作为输入，不再内置预处理器

logger = logging.getLogger(__name__)


@dataclass
class FilterResult:
    """过滤结果数据类 - 只返回得分，让用户自己决定"""
    keyword_score: float
    topic_score: float
    relevance_score: float
    confidence: float
    matched_keywords: List[str]
    matched_topics: List[str]
    keyword_count: int
    topic_count: int
    details: Dict


class UnifiedNewsFilter:
    """统一新闻过滤器 - 参数化配置版本"""

    def __init__(
        self,
        keywords_file: str = "topic_correlation_analysis/high_impact_keywords.json",
        trading_filters_file: str = "topic_correlation_analysis/trading_filters.json",
        topics_file: str = "topic_analysis_output/topic_analysis_results.json",
        min_keywords: int = 2,
        min_topic_relevance: float = 0.05,
        min_confidence: float = 0.2,
        keyword_weight: float = 0.3,
        topic_weight: float = 3.0,
        min_frequency: int = 2,
        min_enrichment: float = 2.0
    ):
        """
        初始化过滤器 - 接受预处理后的文本作为输入

        Args:
            keywords_file: 高影响力关键词文件路径
            trading_filters_file: 交易过滤器文件路径
            topics_file: 主题分析结果文件路径
            min_keywords: 最小关键词匹配数量
            min_topic_relevance: 最小主题相关性阈值
            min_confidence: 最小置信度阈值
            keyword_weight: 关键词权重
            topic_weight: 主题权重
            min_frequency: 关键词最小频率
            min_enrichment: 关键词最小富集度
        """

        # 配置参数
        self.config = {
            'keywords_file': keywords_file,
            'trading_filters_file': trading_filters_file,
            'topics_file': topics_file,
            'min_keywords': min_keywords,
            'min_topic_relevance': min_topic_relevance,
            'min_confidence': min_confidence,
            'keyword_weight': keyword_weight,
            'topic_weight': topic_weight,
            'min_frequency': min_frequency,
            'min_enrichment': min_enrichment
        }

        # 初始化数据
        self.valuable_keywords = set()
        self.predictive_topics = []

        # 加载数据
        self._load_keywords()
        self._load_topics()

        logger.info(f"✅ 新闻过滤器初始化完成 (关键词:{len(self.valuable_keywords)}, 主题:{len(self.predictive_topics)})")

    def _normalize_keyword(self, keyword: str) -> str:
        """
        标准化关键词：将英文转换为小写，保留中文、数字、符号原样

        Args:
            keyword: 原始关键词

        Returns:
            str: 标准化后的关键词
        """
        result = []
        for char in keyword:
            # 只对英文字母进行小写转换
            if 'A' <= char <= 'Z':
                result.append(char.lower())
            else:
                # 中文、数字、符号、已经是小写的英文字母都保持原样
                result.append(char)
        return ''.join(result)

    def _load_keywords(self):
        """加载有价值关键词"""
        # 从高影响力关键词文件加载
        if os.path.exists(self.config['keywords_file']):
            try:
                with open(self.config['keywords_file'], 'r', encoding='utf-8') as f:
                    high_impact_data = json.load(f)

                # 收集符合条件的关键词
                for category, keywords in high_impact_data.items():
                    for keyword_info in keywords:
                        keyword = keyword_info['keyword']
                        frequency = keyword_info.get('frequency', 0)
                        enrichment = keyword_info.get('avg_enrichment', 0)

                        if frequency >= self.config['min_frequency'] and enrichment >= self.config['min_enrichment']:
                            # 对英文关键词进行小写转换
                            normalized_keyword = self._normalize_keyword(keyword)
                            self.valuable_keywords.add(normalized_keyword)

                logger.info(f"✅ 从高影响力关键词加载了 {len(self.valuable_keywords)} 个关键词")
            except Exception as e:
                logger.warning(f"⚠️ 加载高影响力关键词失败: {e}")

        # 从交易过滤器文件加载
        if os.path.exists(self.config['trading_filters_file']):
            try:
                with open(self.config['trading_filters_file'], 'r', encoding='utf-8') as f:
                    trading_data = json.load(f)

                # 收集所有交易信号关键词
                for signal_type, keywords in trading_data.items():
                    for keyword in keywords:
                        # 对英文关键词进行小写转换
                        normalized_keyword = self._normalize_keyword(keyword)
                        self.valuable_keywords.add(normalized_keyword)

                logger.info(f"✅ 总共加载了 {len(self.valuable_keywords)} 个模型分析关键词")
            except Exception as e:
                logger.warning(f"⚠️ 加载交易过滤器失败: {e}")

        # 添加补充的高价值关键词（避免与原始文件重复）
        additional_keywords = {
            # 金额相关
            '万美元', '亿美元', '千万', '百万', '万枚', '亿枚',
            # 交易相关
            '交易所', '提出', '充值', '转入', '转出', '聪明钱',
            # 新闻相关
            '引发', '关注', '市场',
            # 价格动作关键词
            '新高', '历史', '涨幅', '跌幅', '上涨', '下跌', '价格'
        }
        # 对补充关键词也进行标准化
        normalized_additional = {self._normalize_keyword(kw) for kw in additional_keywords}
        self.valuable_keywords.update(normalized_additional)
    
    def _load_topics(self):
        """加载预测性主题"""
        if os.path.exists(self.config['topics_file']):
            try:
                with open(self.config['topics_file'], 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # 加载所有预测性主题
                for direction in ['bearish_topics', 'bullish_topics', 'neutral_topics']:
                    if direction in data.get('predictive_topics', {}):
                        for topic in data['predictive_topics'][direction]:
                            self.predictive_topics.append({
                                'name': topic['name'],
                                'keywords': topic['keywords'],
                                'enrichment': topic['enrichment']
                            })

                logger.info(f"✅ 加载了 {len(self.predictive_topics)} 个预测性主题")
            except Exception as e:
                logger.warning(f"⚠️ 加载主题文件失败: {e}")
        else:
            logger.warning(f"⚠️ 主题文件不存在: {self.config['topics_file']}")
    

    
    def _calculate_topic_relevance(self, preprocessed_text: str) -> Tuple[float, Dict]:
        """
        计算主题相关性分数

        Args:
            preprocessed_text: 预处理后的文本

        Returns:
            Tuple[float, Dict]: (相关性分数, 详细信息)
        """
        if not self.predictive_topics:
            return 0.0, {'reason': '无可用预测性主题'}

        if not preprocessed_text.strip():
            return 0.0, {'reason': '预处理后文本为空'}

        # 直接在文本中匹配关键词（与关键词匹配逻辑保持一致）
        best_score = 0.0
        all_matches = []

        # 与每个预测性主题进行匹配
        for topic in self.predictive_topics:
            topic_keywords = topic['keywords']
            matched_keywords = []

            # 检查每个主题关键词是否在文本中出现
            for keyword in topic_keywords:
                # 对主题关键词也进行标准化
                normalized_keyword = self._normalize_keyword(keyword)
                if normalized_keyword in preprocessed_text:
                    matched_keywords.append(normalized_keyword)

            match_count = len(matched_keywords)

            if match_count > 0:
                # 计算相关性分数
                relevance_score = match_count / len(topic_keywords)
                weighted_score = relevance_score * topic['enrichment']

                match_info = {
                    'topic_name': topic['name'],
                    'matched_keywords': matched_keywords,
                    'match_count': match_count,
                    'weighted_score': weighted_score
                }

                all_matches.append(match_info)
                best_score = max(best_score, weighted_score)

        details = {
            'all_matches': all_matches,
            'total_topics_checked': len(self.predictive_topics)
        }

        return best_score, details

    def filter_news(self, preprocessed_title: str, preprocessed_content: str) -> FilterResult:
        """
        主要过滤方法：计算各种得分，让用户自己决定是否过滤

        Args:
            preprocessed_title: 预处理后的标题
            preprocessed_content: 预处理后的内容

        Returns:
            FilterResult: 包含各种得分的结果，不包含过滤决策
        """

        # 合并预处理后的文本
        combined_text = f"{preprocessed_title} {preprocessed_content}".strip()

        if not combined_text:
            return FilterResult(
                keyword_score=0.0,
                topic_score=0.0,
                relevance_score=0.0,
                confidence=0.0,
                matched_keywords=[],
                matched_topics=[],
                keyword_count=0,
                topic_count=0,
                details={'reason': '预处理后文本为空'}
            )

        # 1. 关键词匹配分析
        matched_keywords = self._match_keywords(combined_text)

        # 2. 主题相关性分析
        topic_score, topic_details = self._calculate_topic_relevance(combined_text)
        matched_topics = [match['topic_name'] for match in topic_details.get('all_matches', [])]

        # 3. 计算各种得分（不做决策）
        keyword_score = len(matched_keywords) * self.config['keyword_weight']
        relevance_score = keyword_score + topic_score * self.config['topic_weight']
        confidence = min((len(matched_keywords) * 0.1 + topic_score), 1.0)

        # 4. 构建详细信息
        details = {
            'text_length': len(combined_text),
            'topic_details': topic_details,
            'config_used': self.config  # 提供配置信息供用户参考
        }

        return FilterResult(
            keyword_score=keyword_score,
            topic_score=topic_score,
            relevance_score=relevance_score,
            confidence=confidence,
            matched_keywords=matched_keywords,
            matched_topics=matched_topics,
            keyword_count=len(matched_keywords),
            topic_count=len(matched_topics),
            details=details
        )

    def _match_keywords(self, combined_text: str) -> List[str]:
        """匹配有价值关键词"""
        matched_keywords = []

        # 检查有价值关键词匹配
        for keyword in self.valuable_keywords:
            if keyword in combined_text:
                matched_keywords.append(keyword)

        # 检查数字信息（百分比、金额等）
        numbers = re.findall(r'\d+\.?\d*%', combined_text)
        amounts = re.findall(r'\d+万美元|\d+亿美元|\d+千万|\d+百万|\d+万枚|\d+亿枚', combined_text)

        if numbers:
            matched_keywords.extend([f"百分比:{num}" for num in numbers[:2]])
        if amounts:
            matched_keywords.extend([f"金额:{amt}" for amt in amounts[:2]])

        return matched_keywords



    def batch_filter(self, news_list: List[Dict]) -> List[FilterResult]:
        """
        批量过滤新闻
        注意：需要预处理后的标题和内容
        """
        results = []

        logger.info(f"开始批量过滤 {len(news_list)} 条新闻...")

        for i, news in enumerate(news_list):
            # 假设输入已经是预处理后的数据
            preprocessed_title = news.get('preprocessed_title', news.get('title', ''))
            preprocessed_content = news.get('preprocessed_content', news.get('content', ''))

            result = self.filter_news(preprocessed_title, preprocessed_content)
            results.append(result)

            if (i + 1) % 100 == 0:
                logger.info(f"已处理 {i + 1}/{len(news_list)} 条新闻")

        # 统计结果
        worth_predicting = sum(1 for r in results if r.worth_predicting)
        logger.info(f"批量过滤完成: {worth_predicting}/{len(news_list)} 条新闻值得预测")

        return results

    def get_stats(self) -> Dict:
        """获取过滤器统计信息"""
        return {
            'valuable_keywords_count': len(self.valuable_keywords),
            'predictive_topics_count': len(self.predictive_topics),
            'config': self.config
        }
