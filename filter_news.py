import os
import csv
import json
import re
from datetime import datetime, timedelta

# --- Configuration ---
SYMBOL_DATA_PATH = 'binance_symbols.json'
INPUT_DIR = 'news_data'
OUTPUT_DIR = 'news_data_filtered'

def extract_base_symbols(binance_symbols):
    """
    Extract base symbols from Binance symbols, filtering out problematic symbols.
    Handles special prefixes like 1000, 1000000, 1M for future market naming.
    """
    # Common English words that should not be treated as crypto symbols
    EXCLUDED_WORDS = {
        'THE', 'AND', 'OR', 'BUT', 'FOR', 'WITH', 'FROM', 'TO', 'IN', 'ON', 'AT',
        'BY', 'UP', 'OUT', 'OFF', 'OVER', 'UNDER', 'AGAIN', 'FURTHER', 'THEN',
        'ONCE', 'HERE', 'THERE', 'WHEN', 'WHERE', 'WHY', 'HOW', 'ALL', 'ANY',
        'BOTH', 'EACH', 'FEW', 'MORE', 'MOST', 'OTHER', 'SOME', 'SUCH', 'NO',
        'NOR', 'NOT', 'ONLY', 'OWN', 'SAME', 'SO', 'THAN', 'TOO', 'VERY', 'CAN',
        'WILL', 'JUST', 'SHOULD', 'NOW', 'GET', 'SET', 'PUT', 'GO', 'COME', 'SEE'
    }

    base_symbols = {}
    for symbol in binance_symbols.keys():
        if symbol.endswith('USDT'):
            base_asset = symbol.replace('USDT', '')

            # Handle special prefixes for future market naming
            original_base_asset = base_asset

            # Check for 1000000 prefix first (longer prefix takes priority)
            if base_asset.startswith('1000000'):
                base_asset = base_asset[7:]  # Remove '1000000' prefix
            # Check for 1000 prefix
            elif base_asset.startswith('1000'):
                base_asset = base_asset[4:]  # Remove '1000' prefix
            # Check for 1M prefix
            elif base_asset.startswith('1M'):
                base_asset = base_asset[2:]  # Remove '1M' prefix

            # Skip single character symbols and common English words
            if len(base_asset) > 1 and base_asset not in EXCLUDED_WORDS:
                # Map the clean base asset to the original symbol
                base_symbols[base_asset] = symbol

                # Also add the original base asset if it's different and valid
                if (original_base_asset != base_asset and
                    len(original_base_asset) > 1 and
                    original_base_asset not in EXCLUDED_WORDS):
                    base_symbols[original_base_asset] = symbol

    return base_symbols

def extract_english_words(text):
    """
    Extract complete English words from text, avoiding partial matches.
    """
    # Use regex to find complete words (letters only, minimum 2 characters)
    words = re.findall(r'\b[A-Za-z]{2,}\b', text)
    return [word.upper() for word in words]

def load_comprehensive_crypto_aliases():
    """
    Load comprehensive cryptocurrency aliases including common terms and professional names.
    Only includes high-confidence mappings to avoid false positives.
    """
    # Professional terms and common nicknames - carefully curated
    crypto_aliases = {
        # Bitcoin aliases
        "BITCOIN": "BTCUSDT",
        "BTC": "BTCUSDT",
        "比特币": "BTCUSDT",
        "数字黄金": "BTCUSDT",
        "DIGITAL GOLD": "BTCUSDT",

        # Ethereum aliases
        "ETHEREUM": "ETHUSDT",
        "ETH": "ETHUSDT",
        "以太坊": "ETHUSDT",
        "以太币": "ETHUSDT",
        "ETHER": "ETHUSDT",

        # Major cryptocurrencies with clear mappings
        "BINANCE COIN": "BNBUSDT",
        "BNB": "BNBUSDT",
        "币安币": "BNBUSDT",

        "RIPPLE": "XRPUSDT",
        "XRP": "XRPUSDT",
        "瑞波币": "XRPUSDT",

        "CARDANO": "ADAUSDT",
        "ADA": "ADAUSDT",
        "卡尔达诺": "ADAUSDT",

        "SOLANA": "SOLUSDT",
        "SOL": "SOLUSDT",
        "索拉纳": "SOLUSDT",

        "DOGECOIN": "DOGEUSDT",
        "DOGE": "DOGEUSDT",
        "狗狗币": "DOGEUSDT",

        "POLKADOT": "DOTUSDT",
        "DOT": "DOTUSDT",
        "波卡": "DOTUSDT",

        "CHAINLINK": "LINKUSDT",
        "LINK": "LINKUSDT",

        "LITECOIN": "LTCUSDT",
        "LTC": "LTCUSDT",
        "莱特币": "LTCUSDT",

        "BITCOIN CASH": "BCHUSDT",
        "BCH": "BCHUSDT",
        "比特币现金": "BCHUSDT",

        "AVALANCHE": "AVAXUSDT",
        "AVAX": "AVAXUSDT",
        "雪崩": "AVAXUSDT",

        "POLYGON": "MATICUSDT",
        "MATIC": "MATICUSDT",

        "UNISWAP": "UNIUSDT",
        "UNI": "UNIUSDT",
    }

    return crypto_aliases

def load_binance_symbols(json_path):
    """
    Loads the Binance symbol data and creates keyword mapping using only
    crypto_aliases and binance_symbols (no external symbol_aliases.json).
    """
    try:
        with open(json_path, 'r') as f:
            print(f"Successfully loaded symbol data from {json_path}")
            binance_symbols = json.load(f)
    except FileNotFoundError:
        print(f"Error: The file {json_path} was not found.")
        print("Please run the get_binance_symbols.py script first to generate it.")
        return None, None
    except json.JSONDecodeError:
        print(f"Error: Could not decode JSON from {json_path}.")
        return None, None

    # Get base symbols from Binance symbols (excluding single characters)
    base_symbols = extract_base_symbols(binance_symbols)
    print(f"Extracted {len(base_symbols)} base symbols from Binance")

    # Load comprehensive crypto aliases (built-in professional terms)
    crypto_aliases = load_comprehensive_crypto_aliases()
    print(f"Loaded {len(crypto_aliases)} built-in crypto aliases")

    # Combine only crypto_aliases and base_symbols (no external symbol_aliases)
    # Ensure all keys are uppercase for consistent comparison
    all_aliases = {}

    # Add base symbols (already uppercase keys)
    all_aliases.update(base_symbols)

    # Add crypto aliases with uppercase keys
    for key, value in crypto_aliases.items():
        all_aliases[key.upper()] = value.upper()  # Ensure both key and value are uppercase

    # Create uppercase mapping of binance_symbols for efficient lookup
    binance_symbols_upper = {k.upper(): k for k in binance_symbols.keys()}

    # Sort keywords by length in descending order to prioritize longer matches
    sorted_keywords = sorted(all_aliases.keys(), key=len, reverse=True)

    # Create final keyword mapping with exclusion filter
    EXCLUDED_WORDS = {
        'THE', 'AND', 'OR', 'BUT', 'FOR', 'WITH', 'FROM', 'TO', 'IN', 'ON', 'AT',
        'BY', 'UP', 'OUT', 'OFF', 'OVER', 'UNDER', 'AGAIN', 'FURTHER', 'THEN',
        'ONCE', 'HERE', 'THERE', 'WHEN', 'WHERE', 'WHY', 'HOW', 'ALL', 'ANY',
        'BOTH', 'EACH', 'FEW', 'MORE', 'MOST', 'OTHER', 'SOME', 'SUCH', 'NO',
        'NOR', 'NOT', 'ONLY', 'OWN', 'SAME', 'SO', 'THAN', 'TOO', 'VERY', 'CAN',
        'WILL', 'JUST', 'SHOULD', 'NOW', 'GET', 'SET', 'PUT', 'GO', 'COME', 'SEE'
    }

    comprehensive_symbol_keywords = {}
    for keyword in sorted_keywords:
        # Skip excluded words
        if keyword.upper() in EXCLUDED_WORDS:
            continue

        binance_symbol = all_aliases[keyword]
        # Use case-insensitive comparison with binance_symbols
        if binance_symbol.upper() in binance_symbols_upper:
            # Use the original case from binance_symbols
            original_symbol = binance_symbols_upper[binance_symbol.upper()]
            comprehensive_symbol_keywords[keyword.upper()] = original_symbol
        else:
            print(f"Warning: Alias '{keyword}' maps to '{binance_symbol}' which is not in binance_symbols.json. Skipping.")

    print(f"Final keyword mappings: {len(comprehensive_symbol_keywords)}")
    print("Sources: Binance base symbols + Built-in crypto aliases only")
    return binance_symbols, comprehensive_symbol_keywords

def is_low_value_news(title, content):
    """
    Check if news is low-value (price movements, simple statistics) that should be filtered out.
    """
    text = (title + " " + content).upper()

    # Price movement patterns
    price_patterns = [
        r'涨\s*\d+%', r'跌\s*\d+%', r'下跌\s*\d+%', r'上涨\s*\d+%',
        r'RISE\s*\d+%', r'FALL\s*\d+%', r'UP\s*\d+%', r'DOWN\s*\d+%',
        r'增长\s*\d+%', r'下降\s*\d+%', r'暴涨', r'暴跌',
        r'价格\s*(上涨|下跌|涨|跌)', r'PRICE\s*(RISE|FALL|UP|DOWN)',
        r'\d+%\s*(涨幅|跌幅)', r'\d+%\s*(GAIN|LOSS)',
        r'24小时.*?(涨|跌)', r'24H.*?(UP|DOWN)',
        r'市值.*?(增加|减少|涨|跌)', r'MARKET\s*CAP.*?(UP|DOWN|RISE|FALL)'
    ]

    # Simple statistics patterns
    stats_patterns = [
        r'交易量.*?\d+', r'VOLUME.*?\d+',
        r'持仓.*?\d+', r'POSITION.*?\d+',
        r'爆仓.*?\d+', r'LIQUIDATION.*?\d+',
        r'资金费率', r'FUNDING\s*RATE',
        r'未平仓', r'OPEN\s*INTEREST'
    ]

    # Check for low-value patterns
    for pattern in price_patterns + stats_patterns:
        if re.search(pattern, text):
            return True

    return False

def calculate_relevance_score(keyword, title, content):
    """
    Calculate relevance score for a keyword match with frequency consideration.
    Higher scores indicate more relevant matches.
    """
    score = 0
    keyword_upper = keyword.upper()
    title_upper = title.upper()
    content_upper = content.upper()
    full_text = title_upper + " " + content_upper

    # Count total occurrences (frequency matters!)
    total_matches = full_text.count(keyword_upper)

    # Title matches are more important
    title_matches = title_upper.count(keyword_upper)
    if title_matches > 0:
        score += title_matches * 3
        # Bonus if keyword appears early in title
        if title_upper.find(keyword_upper) < 20:
            score += 1

    # Content matches with frequency bonus
    content_matches = content_upper.count(keyword_upper)
    score += content_matches

    # Frequency bonus: multiple mentions are more significant
    if total_matches >= 3:
        score += 2  # Strong signal
    elif total_matches >= 2:
        score += 1  # Moderate signal

    # Bonus for longer keywords (more specific)
    if len(keyword) >= 5:
        score += 1

    # Context bonus: check for token/coin context
    token_context_patterns = [
        r'代币', r'TOKEN', r'COIN', r'币', r'货币', r'CURRENCY',
        r'项目', r'PROJECT', r'协议', r'PROTOCOL', r'网络', r'NETWORK'
    ]

    for pattern in token_context_patterns:
        if re.search(pattern, full_text):
            score += 1
            break

    return score

def select_symbols_by_frequency(symbol_matches, text):
    """
    Select symbols based on frequency. Prioritize symbols with higher frequency.
    If frequencies are equal, keep multiple symbols.

    Args:
        symbol_matches: dict {symbol: (keyword, score)}
        text: full text to analyze

    Returns:
        set of selected symbols
    """
    if not symbol_matches:
        return set()

    # Calculate frequency for each symbol's keywords
    symbol_frequencies = {}
    text_upper = text.upper()

    for symbol, (keyword, score) in symbol_matches.items():
        # Count frequency of the keyword in text using proper matching
        keyword_upper = keyword.upper()

        # Use appropriate matching method based on keyword type
        if any(u'\u4e00' <= char <= u'\u9fff' for char in keyword):
            # Chinese keywords: simple count
            frequency = text_upper.count(keyword_upper)
        else:
            # English keywords: use enhanced matching for mixed language text
            import re
            # Try multiple patterns for better mixed-language support
            patterns = [
                r'\b' + re.escape(keyword_upper) + r'\b',  # Standard word boundary
                r'(?<![A-Z])' + re.escape(keyword_upper) + r'(?![A-Z])',  # Letter boundaries
                r'(?<![A-Z0-9])' + re.escape(keyword_upper) + r'(?![A-Z0-9])'  # Alphanumeric boundaries
            ]

            frequency = 0
            for pattern in patterns:
                try:
                    matches = re.findall(pattern, text_upper)
                    if matches:
                        frequency = len(matches)
                        break  # Use the first pattern that finds matches
                except re.error:
                    continue  # Skip invalid patterns

        # If symbol already exists, take the higher frequency
        if symbol in symbol_frequencies:
            symbol_frequencies[symbol] = max(symbol_frequencies[symbol], frequency)
        else:
            symbol_frequencies[symbol] = frequency

    if not symbol_frequencies:
        return set()

    # Find the maximum frequency
    max_frequency = max(symbol_frequencies.values())

    # Select symbols with maximum frequency
    selected_symbols = {
        symbol for symbol, freq in symbol_frequencies.items()
        if freq == max_frequency
    }

    # If only one symbol has max frequency, return it
    if len(selected_symbols) == 1:
        return selected_symbols

    # If multiple symbols have same max frequency, check if max frequency > 1
    # If so, keep all symbols with max frequency
    # If max frequency = 1, apply additional filtering
    if max_frequency > 1:
        return selected_symbols
    else:
        # All symbols appear only once, apply score-based selection
        symbol_scores = {symbol: score for symbol, (keyword, score) in symbol_matches.items()}
        max_score = max(symbol_scores.values())

        # Keep symbols with highest scores
        high_score_symbols = {
            symbol for symbol, score in symbol_scores.items()
            if score == max_score
        }

        # If still multiple symbols, limit to top 3 to avoid noise
        if len(high_score_symbols) > 3:
            # Sort by score and take top 3
            sorted_symbols = sorted(
                high_score_symbols,
                key=lambda s: symbol_scores[s],
                reverse=True
            )
            return set(sorted_symbols[:3])

        return high_score_symbols

def parse_news_timestamp_manual(date_str, time_str, filename):
    """
    Parses the news timestamp (UTC+8) and converts it to a UTC Unix timestamp in ms.
    """
    try:
        year_str = filename.split('_')[2][:4]
        full_datetime_str = f"{year_str}-{date_str.replace('月', '-').replace('日', '')} {time_str}"
        naive_dt = datetime.strptime(full_datetime_str, '%Y-%m-%d %H:%M')
        utc_dt = naive_dt - timedelta(hours=8)
        return int(utc_dt.timestamp() * 1000)
    except Exception as e:
        return None

def filter_csv(input_path, output_path, binance_symbols, symbol_keywords):
    """
    Reads a CSV, filters rows based on the symbol's trading window,
    and writes the valid, relevant news to a new CSV file.
    """
    try:
        filename = os.path.basename(input_path)
        
        with open(input_path, 'r', newline='', encoding='utf-8-sig') as infile, \
             open(output_path, 'w', newline='', encoding='utf-8-sig') as outfile:

            reader = csv.reader(infile)
            writer = csv.writer(outfile)

            header = next(reader)
            header.append('relative_symbols')
            writer.writerow(header)
            
            title_index = header.index('title')
            content_index = header.index('content')
            date_index = header.index('date')
            time_index = header.index('time')

            for row in reader:
                news_ts = parse_news_timestamp_manual(row[date_index], row[time_index], filename)
                if not news_ts:
                    continue

                title = row[title_index]
                content = row[content_index]

                # # Filter out low-value news (price movements, simple statistics)
                # if is_low_value_news(title, content):
                #     continue

                text_to_search = (title + " " + content).upper()

                # Minimum relevance score threshold (increased due to better scoring)
                MIN_RELEVANCE_SCORE = 2

                # Collect all potential symbol matches with their keywords and scores
                symbol_matches = {}

                for keyword, binance_symbol in symbol_keywords.items():
                    matched = False

                    # Check for Chinese keywords (assuming they don't need word boundaries)
                    if any(u'\u4e00' <= char <= u'\u9fff' for char in keyword):
                        if keyword in text_to_search:
                            matched = True
                    else:
                        # English keywords - use enhanced matching for mixed language text
                        patterns = [
                            r'\b' + re.escape(keyword) + r'\b',  # Standard word boundary
                            r'(?<![A-Z])' + re.escape(keyword) + r'(?![A-Z])',  # Letter boundaries
                            r'(?<![A-Z0-9])' + re.escape(keyword) + r'(?![A-Z0-9])'  # Alphanumeric boundaries
                        ]

                        for pattern in patterns:
                            try:
                                if re.search(pattern, text_to_search):
                                    matched = True
                                    break
                            except re.error:
                                continue  # Skip invalid patterns

                    if matched:
                        # Calculate relevance score
                        relevance_score = calculate_relevance_score(keyword, title, content)

                        if relevance_score >= MIN_RELEVANCE_SCORE:
                            symbol = binance_symbol
                            data = binance_symbols[symbol]
                            onboard_ts = data.get('onboardDate')
                            delivery_ts = data.get('deliveryDate')

                            # Check if news timestamp is within trading window
                            if onboard_ts and news_ts >= onboard_ts:
                                if delivery_ts == 0 or delivery_ts is None or news_ts <= delivery_ts:
                                    # Store symbol with its keyword and score for frequency analysis
                                    if symbol not in symbol_matches or relevance_score > symbol_matches[symbol][1]:
                                        symbol_matches[symbol] = (keyword, relevance_score)

                # Select symbols based on frequency and relevance
                relevant_symbols = select_symbols_by_frequency(symbol_matches, title + " " + content)
                
                if relevant_symbols:
                    row.append(','.join(sorted(list(relevant_symbols))))
                    writer.writerow(row)
                            
    except Exception as e:
        print(f"An error occurred while processing file {input_path}: {e}")

def main():
    """Main function to run the filtering script."""
    print("Starting the news filtering process...")
    
    binance_symbols, symbol_keywords = load_binance_symbols(SYMBOL_DATA_PATH)
    if not binance_symbols or not symbol_keywords:
        return

    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)
        print(f"Created output directory: {OUTPUT_DIR}")

    processed_files = 0
    for filename in os.listdir(INPUT_DIR):
        if filename.endswith('.csv'):
            input_file_path = os.path.join(INPUT_DIR, filename)
            output_file_path = os.path.join(OUTPUT_DIR, filename)
            
            print(f"Processing {input_file_path}...")
            filter_csv(input_file_path, output_file_path, binance_symbols, symbol_keywords)
            processed_files += 1
    
    print(f"\nFinished processing. Filtered {processed_files} files.")
    print(f"Relevant news has been saved to the '{OUTPUT_DIR}' directory.")

if __name__ == '__main__':
    main()