#!/usr/bin/env python3
"""
测试导入脚本
"""

def test_basic_imports():
    """测试基础导入"""
    try:
        import pandas as pd
        print("✅ pandas导入成功")
    except ImportError as e:
        print(f"❌ pandas导入失败: {e}")
        
    try:
        import numpy as np
        print("✅ numpy导入成功")
    except ImportError as e:
        print(f"❌ numpy导入失败: {e}")

def test_nautilus_imports():
    """测试Nautilus Trader导入"""
    try:
        from nautilus_trader.config import BacktestVenueConfig
        print("✅ nautilus_trader基础配置导入成功")
    except ImportError as e:
        print(f"❌ nautilus_trader基础配置导入失败: {e}")
        
    try:
        from nautilus_trader.backtest.node import BacktestNode
        print("✅ nautilus_trader回测节点导入成功")
    except ImportError as e:
        print(f"❌ nautilus_trader回测节点导入失败: {e}")

def test_custom_imports():
    """测试自定义模块导入"""
    try:
        from news_data_type import NewsData
        print("✅ NewsData导入成功")
    except ImportError as e:
        print(f"❌ NewsData导入失败: {e}")
        
    try:
        from crypto_text_processor import CryptoNewsTextProcessor
        print("✅ CryptoNewsTextProcessor导入成功")
    except ImportError as e:
        print(f"❌ CryptoNewsTextProcessor导入失败: {e}")

def test_strategy_imports():
    """测试策略导入"""
    try:
        from process.strategy.news_prediction_strategy import NewsPnlTradingStrategy
        print("✅ NewsPnlTradingStrategy导入成功")
    except ImportError as e:
        print(f"❌ NewsPnlTradingStrategy导入失败: {e}")
        
    try:
        from process.strategy.news_prediction_strategy import NewsPnlTradingStrategyConfig
        print("✅ NewsPnlTradingStrategyConfig导入成功")
    except ImportError as e:
        print(f"❌ NewsPnlTradingStrategyConfig导入失败: {e}")

def main():
    print("🧪 开始导入测试...")
    print("=" * 50)
    
    print("\n📦 测试基础包导入:")
    test_basic_imports()
    
    print("\n🚢 测试Nautilus Trader导入:")
    test_nautilus_imports()
    
    print("\n🔧 测试自定义模块导入:")
    test_custom_imports()
    
    print("\n📈 测试策略导入:")
    test_strategy_imports()
    
    print("\n" + "=" * 50)
    print("✅ 导入测试完成!")

if __name__ == "__main__":
    main()
