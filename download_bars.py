import requests
import pandas as pd
from datetime import datetime, timezone
from pathlib import Path
import time
import os

class SimpleBinanceDownloader:
    """Simple Binance futures data downloader"""
    
    def __init__(self, output_dir: str = "./data"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.base_url = "https://fapi.binance.com"
        
    def get_binance_klines(
        self, 
        symbol: str, 
        interval: str = "5m", 
        start_time: int | None = None,
        end_time: int | None = None,
        limit: int = 1500
    ) -> list[dict]:
        """Fetch kline data from Binance API"""
        url = f"{self.base_url}/fapi/v1/klines"
        params = {
            "symbol": symbol,
            "interval": interval,
            "limit": limit
        }
        
        if start_time:
            params["startTime"] = start_time
        if end_time:
            params["endTime"] = end_time
            
        print(f"Fetching {symbol} from {datetime.fromtimestamp(start_time/1000 if start_time else 0)} to {datetime.fromtimestamp(end_time/1000 if end_time else time.time())}")
        
        response = requests.get(url, params=params)
        response.raise_for_status()
        
        data = response.json()
        
        # Convert to structured format
        klines = []
        for kline in data:
            klines.append({
                'timestamp': pd.to_datetime(int(kline[0]), unit='ms', utc=True),
                'open': float(kline[1]),
                'high': float(kline[2]),
                'low': float(kline[3]),
                'close': float(kline[4]),
                'volume': float(kline[5]),
                'close_time': pd.to_datetime(int(kline[6]), unit='ms', utc=True),
                'quote_volume': float(kline[7]),
                'count': int(kline[8]),
                'taker_buy_volume': float(kline[9]),
                'taker_buy_quote_volume': float(kline[10]),
                'symbol': symbol,
                'interval': interval
            })
            
        return klines
    
    def download_historical_data(
        self,
        symbol: str,
        start_date: str = "2025-01-01",
        end_date: str | None = None,
        interval: str = "5m"
    ) -> pd.DataFrame:
        """Download historical data in chunks"""
        
        # Convert dates to timestamps
        start_dt = datetime.strptime(start_date, "%Y-%m-%d").replace(tzinfo=timezone.utc)
        start_timestamp = int(start_dt.timestamp() * 1000)
        
        if end_date:
            end_dt = datetime.strptime(end_date, "%Y-%m-%d").replace(tzinfo=timezone.utc)
            end_timestamp = int(end_dt.timestamp() * 1000)
        else:
            end_timestamp = int(datetime.now(timezone.utc).timestamp() * 1000)
        
        all_data = []
        current_start = start_timestamp
        
        print(f"📊 Downloading {symbol} {interval} data...")
        print(f"📅 From: {start_dt}")
        print(f"📅 To: {datetime.fromtimestamp(end_timestamp/1000, tz=timezone.utc)}")
        
        max_retries = 5
        retries = 0
        
        while current_start < end_timestamp:
            try:
                # Fetch data chunk
                klines = self.get_binance_klines(
                    symbol=symbol,
                    interval=interval,
                    start_time=current_start,
                    end_time=end_timestamp,
                    limit=1500
                )
                
                if not klines:
                    print("No more data available")
                    break
                    
                all_data.extend(klines)
                
                # Update start time for next chunk (add 1ms to avoid overlap)
                last_close_time = klines[-1]['close_time']
                current_start = int(last_close_time.timestamp() * 1000) + 1
                
                print(f"📈 Downloaded {len(klines)} records, total: {len(all_data)}")
                
                # Rate limiting to avoid hitting API limits
                time.sleep(0.1)
                retries = 0
                
            except requests.exceptions.RequestException as e:
                retries += 1
                print(f"❌ API Error: {e}")
                if retries >= max_retries:
                    print(f"❌ Max retries exceeded for {symbol}. Skipping symbol.")
                    return pd.DataFrame()

                print(f"⏳ Waiting 5 seconds before retry... (Attempt {retries}/{max_retries})")
                time.sleep(5)
                continue
            except Exception as e:
                print(f"❌ Unexpected error: {e}")
                time.sleep(1)
                continue
        
        if not all_data:
            print("❌ No data downloaded")
            return pd.DataFrame()
        
        # Convert to DataFrame
        df = pd.DataFrame(all_data)
        
        # Sort by timestamp and remove duplicates
        df = df.sort_values('timestamp').drop_duplicates(subset=['timestamp'], keep='last')
        
        print(f"✅ Downloaded {len(df)} unique records")
        print(f"📊 Date range: {df['timestamp'].min()} to {df['timestamp'].max()}")
        
        return df
    
    def save_data(self, df: pd.DataFrame, symbol: str, interval: str = "5m"):
        """Save data to parquet and CSV"""
        if df.empty:
            print("❌ No data to save")
            return
            
        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_filename = f"{symbol}_{interval}"
        
        # Save as parquet (efficient for large datasets)
        parquet_path = self.output_dir / f"{base_filename}.parquet"
        df.to_parquet(parquet_path, index=False)
        print(f"💾 Parquet saved: {parquet_path}")
        
        # Save summary
        summary = {
            'symbol': symbol,
            'interval': interval,
            'records': len(df),
            'start_date': str(df['timestamp'].min()),
            'end_date': str(df['timestamp'].max()),
            'download_time': datetime.now().isoformat()
        }
        
        summary_path = self.output_dir / f"{base_filename}_summary.txt"
        with open(summary_path, 'w') as f:
            for key, value in summary.items():
                f.write(f"{key}: {value}\n")
        print(f"📋 Summary saved: {summary_path}")
        
        return parquet_path
    
    def download_and_save(
        self,
        symbol: str = "BTCUSDT",
        start_date: str = "2025-01-01",
        end_date: str | None = None,
        interval: str = "5m"
    ):
        """Main method to download and save data"""
        print(f"🚀 Starting download for {symbol}")
        
        # Check if summary file exists
        summary_path = self.output_dir / f"{symbol}_{interval}_summary.txt"
        if summary_path.exists():
            print(f"✅ Summary file already exists for {symbol}. Skipping download.")
            return None

        # Download data
        df = self.download_historical_data(
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            interval=interval
        )
        
        if df.empty:
            return None
            
        # Save data
        return self.save_data(df, symbol, interval)


def main():
    """Main function"""
    print("Simple Binance Futures Data Downloader")
    print("=" * 50)

    # Create downloader
    downloader = SimpleBinanceDownloader(output_dir="/root/news_train/data")

    # Read symbols from csv
    try:
        df_symbols = pd.read_csv("/root/news_train/symbol_frequency.csv")
        symbols_to_download = df_symbols[df_symbols["frequency"] > 0]["symbol"].tolist()
        print(f"Found {len(symbols_to_download)} symbols with frequency > 0")
    except Exception as e:
        print(f"Error reading or processing symbol_frequency.csv: {e}")
        return

    failed_symbols = []

    # Download data for each symbol
    for symbol in symbols_to_download:
        result = downloader.download_and_save(
            symbol=symbol,
            start_date="2025-01-01",
            end_date=None,  # Until now
            interval="5m"
        )

        if result:
            parquet_path = result
            print(f"\n✅ Download completed!")
            print(f"📁 Files saved to: {downloader.output_dir}")
            print(f"📊 Parquet: {parquet_path.name}")
            print("⏳ Sleeping for 5 seconds...")
            time.sleep(5)
        else:
            print(f"❌ Download failed for {symbol}")
            failed_symbols.append(symbol)

    if failed_symbols:
        print("\n--- Failed Symbols ---")
        for symbol in failed_symbols:
            print(symbol)


if __name__ == "__main__":
    main()
